{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue", "mtime": 1754285969806}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_engineer<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "_projectItemOrder", "_vueTreeselect", "_interopRequireDefault", "_customer", "_validate", "_executionAddOrEdit", "name", "components", "Treeselect", "executionAddOrEdit", "data", "loading", "ids", "selectedRows", "single", "multiple", "showSearch", "total", "engineer<PERSON>ampleOrderList", "researchDeptDatas", "dylbOptions", "title", "open", "queryParams", "pageNum", "pageSize", "userId", "nick<PERSON><PERSON>", "sampleOrderCode", "completionStatus", "scheduledDate", "startDate", "actualStartTime", "actualFinishTime", "deptId", "deptIds", "associationStatus", "customerId", "productName", "confirmCode", "isOverdue", "laboratory", "form", "rules", "required", "message", "trigger", "statusOptions", "serviceModeOptions", "date<PERSON><PERSON><PERSON>", "scheduledDateRange", "startDateRange", "actualStartTimeRange", "actualFinishTimeRange", "dataPickerOptions", "shortcuts", "text", "onClick", "picker", "today", "Date", "$emit", "yesterday", "setTime", "getTime", "end", "start", "statusOpen", "dashboardStats", "changeEngineerOpen", "changeEngineerForm", "id", "currentEngineerName", "oldEngineerId", "newEngineerId", "adjustWorkSchedule", "changeEngineerRules", "engineerOptions", "batchManagementOpen", "activeBatches", "allBatches", "selectedOrderForBatch", "currentBatchRow", "batchDetailOpen", "batchDetailData", "finishBatchOpen", "finishBatchForm", "qualityEvaluation", "remark", "currentFinishBatch", "finishBatchMode", "addExperimentOpen", "addExperimentForm", "experimentCode", "experimentNote", "addExperimentRules", "currentBatchForExperiment", "futureDatePickerOptions", "disabledDate", "time", "now", "engineerSelectType", "searchedEngineers", "unassignedOrders", "unassigned<PERSON>ueryP<PERSON><PERSON>", "unassignedTotal", "isUnassignedPanelCollapsed", "finishTaskOpen", "finishTaskLoading", "finishTaskForm", "laboratoryCode", "experimentCodeList", "finishTaskRules", "currentFinishRow", "updateLaboratoryCodeOpen", "updateLaboratoryCodeLoading", "updateLaboratoryCodeForm", "updateLaboratoryCodeRules", "currentUpdateLaboratoryCodeRow", "exportDialogOpen", "exportOptions", "exportLoading", "readonly", "currentProjectType", "confirmItemCodes", "customerOptions", "itemNames", "rejectDialogOpen", "rejectLoading", "rejectForm", "rejectReason", "overdueOperationOpen", "overdueOperationLoading", "overdueOperationForm", "expectedSampleTime", "reasonForNoSample", "solution", "currentOverdueRow", "rejectRules", "currentRejectRow", "addSampleOrderOpen", "addSampleOrderLoading", "addSampleOrderForm", "labNo", "categoryText", "dylb", "addSampleOrderRules", "sckxxpgCplbs", "computed", "canEditBatch", "created", "_this", "todayStr", "getFullYear", "String", "getMonth", "padStart", "getDate", "getList", "customerBaseAll", "then", "res", "getDicts", "response", "loadDashboardStats", "getResearchDepartments", "handleTree", "handleUnassignedOrders", "methods", "getProjectLevel", "row", "customerLevel", "projectLevel", "_this2", "params", "_objectSpread2", "default", "addDateRange", "beginDateRange", "endDateRange", "length", "listEngineerSampleOrder", "rows", "cancel", "reset", "serviceMode", "difficultyLevelId", "actualManHours", "estimatedManHours", "standardManHours", "isLocked", "endDate", "checkType", "sampleOrderRemark", "createBy", "createTime", "updateBy", "updateTime", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "handleAdd", "handleUpdate", "_this3", "getEngineerSampleOrder", "addEngineerSampleOrder", "submitForm", "_this4", "$refs", "validate", "valid", "updateEngineerSampleOrder", "msgSuccess", "handleDelete", "_this5", "$confirm", "delEngineerSampleOrder", "catch", "handleExport", "confirmExport", "$message", "warning", "executeExports", "index", "_this6", "success", "concat", "option", "typeName", "doExport", "error", "exportType", "_this7", "exportEngineerSampleOrder", "download", "msg", "doExportSampleOrder", "_this8", "confirmButtonText", "cancelButtonText", "type", "handleStart", "_this9", "updateSampleOrderStatus", "status", "handleDownloadSampleOrder", "_this10", "exportNrwItem", "itemId", "projectId", "handleBatchExportNrw", "_this11", "$modal", "msgError", "exportData", "exportMultipleNrw", "handleFinish", "_this12", "loadExperimentCodeList", "$nextTick", "clearValidate", "confirmFinishTask", "_this13", "Array", "isArray", "join", "handleFinishFromBatch", "_this14", "handleStartFromBatch", "_this15", "loadBatchData", "handleUpdateLaboratoryCode", "_this16", "confirmUpdateLaboratoryCode", "_this17", "handleReject", "_this18", "confirmReject", "_this19", "handleOverdueOperation", "_this20", "confirmOverdueOperation", "_this21", "orderDetail", "_this22", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "orderId", "wrap", "_callee$", "_context", "prev", "next", "projectOrderId", "getExecutionByOrderInfo", "sent", "isNull", "show", "t0", "console", "stop", "handleLockChange", "value", "_this23", "handleTableLockChange", "_this24", "_this25", "getDashboardStats", "handleChangeEngineer", "_this26", "getEngineersByDifficultyLevel", "categoryId", "getResearchDepartmentsUser", "submitChangeEngineer", "_this27", "sampleOrderId", "changeEngineer", "_this28", "handleAssignEngineer", "_this29", "handleDeleteUnassigned", "_this30", "handleRejectUnassigned", "_this31", "handleUnassignedSizeChange", "newSize", "handleUnassignedCurrentChange", "newPage", "toggleUnassignedPanel", "handleStatusFilter", "handleOverdueFilter", "info", "getUrgencyType", "latestStartTime", "latest", "daysToEnd", "Math", "ceil", "getUrgencyText", "handleRefreshUnassigned", "getOverdueInfo", "overdueDays", "handleBatchManagement", "engineerSampleOrderId", "_this32", "_callee2", "activeBatchesResponse", "_iterator", "_step", "batch", "batchesResponse", "_callee2$", "_context2", "getActiveBatchesData", "_createForOfIteratorHelper2", "s", "n", "done", "loadBatchExperiments", "e", "f", "finish", "getBatchesByOrderId", "t1", "_this33", "_callee3", "_callee3$", "_context3", "getExperimentsByBatchId", "$set", "_callee4", "_callee4$", "_context4", "getActiveBatches", "abrupt", "startNewBatch", "_this34", "_callee5", "_callee5$", "_context5", "startSingleBatch", "_this35", "_callee6", "_callee6$", "_context6", "batchIndex", "finishSingleBatch", "finishAllActiveBatches", "submitFinishBatch", "_this36", "_callee7", "_callee7$", "_context7", "viewBatchDetail", "_this37", "_callee8", "experiments", "_callee8$", "_context8", "calculateDuration", "startTime", "diffMs", "diffHours", "floor", "diffMinutes", "editLaboratoryCode", "_this38", "input", "$el", "querySelector", "focus", "showAddExperimentDialog", "closeAddExperiment", "resetFields", "submitAddExperiment", "_this39", "_callee9", "experimentRecord", "_callee9$", "_context9", "batchId", "addExperimentToBatch", "removeExperiment", "experiment", "_this40", "_callee10", "_callee10$", "_context10", "removeExperimentFromBatch", "getBatchStatusText", "statusMap", "getBatchStatusType", "typeMap", "getQualityEvaluationType", "evaluation", "lowerEval", "toLowerCase", "includes", "closeBatchManagement", "closeBatchDetail", "closeFinishBatch", "refreshBatchData", "_this41", "_callee11", "_callee11$", "_context11", "_this42", "_callee12", "_callee12$", "_context12", "getBatchExperimentCodeList"], "sources": ["src/views/software/engineerSampleOrder/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 统计概览 -->\r\n    <el-row :gutter=\"20\" class=\"stats-row\" style=\"margin-bottom: 20px;\">\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card total\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-s-order\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">总任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.total || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card completed\" @click.native=\"handleStatusFilter('2')\" :class=\"{'active': queryParams.completionStatus === '2'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-check\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">已完成</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.completed || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card in-progress\" @click.native=\"handleStatusFilter('1')\" :class=\"{'active': queryParams.completionStatus === '1'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-loading\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">进行中</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.inProgress || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card overdue\" @click.native=\"handleOverdueFilter\" :class=\"{'active': queryParams.isOverdue === 1}\" style=\"cursor: pointer;\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-warning\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">逾期任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.overdue || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 未分配工程师的打样单告警 -->\r\n    <el-card class=\"alert-card\">\r\n      <div slot=\"header\" class=\"alert-header\">\r\n        <div class=\"alert-title\" @click=\"toggleUnassignedPanel\">\r\n          <span><i class=\"el-icon-warning-outline\"></i>待分配打样单({{ unassignedTotal }}个)</span>\r\n        </div>\r\n        <div class=\"alert-actions\">\r\n          <el-button\r\n            type=\"text\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"handleRefreshUnassigned\"\r\n            style=\"margin-right: 10px\"\r\n            title=\"刷新列表\">\r\n            刷新\r\n          </el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            @click=\"toggleUnassignedPanel\"\r\n            style=\"margin-right: 10px\">\r\n            {{ isUnassignedPanelCollapsed ? '展开' : '收起' }}\r\n            <i :class=\"isUnassignedPanelCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\r\n          </el-button>\r\n          <el-pagination\r\n            @size-change=\"handleUnassignedSizeChange\"\r\n            @current-change=\"handleUnassignedCurrentChange\"\r\n            :current-page=\"unassignedQueryParams.pageNum\"\r\n            :page-sizes=\"[8, 16, 24, 32]\"\r\n            :page-size=\"unassignedQueryParams.pageSize\"\r\n            layout=\"total, sizes, prev, pager, next\"\r\n            :total=\"unassignedTotal\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n      <el-collapse-transition>\r\n        <div v-show=\"!isUnassignedPanelCollapsed\">\r\n          <el-row :gutter=\"20\" class=\"alert-cards\">\r\n            <el-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\" v-for=\"item in unassignedOrders\" :key=\"item.id\">\r\n              <el-card shadow=\"hover\" class=\"alert-item\">\r\n                <div class=\"alert-item-header\">\r\n                  <div class=\"order-code-section\">\r\n                    <span style=\"color: #00afff;cursor: pointer\"  @click=\"orderDetail(item)\" class=\"order-code\">{{ item.sampleOrderCode }}</span>\r\n                    <el-tag\r\n                      :type=\"getUrgencyType(item.endDate, item.latestStartTime)\"\r\n                      size=\"mini\"\r\n                      class=\"urgency-tag\">\r\n                      {{ getUrgencyText(item.endDate, item.latestStartTime) }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n                <div class=\"alert-item-content\">\r\n                  <div class=\"info-section\">\r\n                    <!-- 预估工时和难度等级 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\" v-if=\"item.latestStartTime\">\r\n                        <i class=\"el-icon-alarm-clock\" style=\"color: #909399;\"></i>\r\n                        <span class=\"info-label\">最晚开始:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-time\" style=\"color: #409EFF;\"></i>\r\n                        <span class=\"info-label\">预估工时:</span>\r\n                        <span class=\"info-value\">{{ item.estimatedManHours || '-' }}H</span>\r\n                      </div>\r\n                    </div>\r\n                    <!-- 最晚开始日期和截止日期排 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\">\r\n                        <i class=\"el-icon-date\" style=\"color: #F56C6C;\"></i>\r\n                        <span class=\"info-label\">截止日期:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.endDate, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-star-on\" style=\"color: #E6A23C;\"></i>\r\n                        <span class=\"info-label\">难度等级:</span>\r\n                        <el-tooltip :content=\"selectDictLabel(dylbOptions, item.difficultyLevelId)\" placement=\"top\">\r\n                          <span class=\"info-value difficulty-text\">{{selectDictLabel(dylbOptions,item.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n                        </el-tooltip>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <el-tooltip v-if=\"item.failureReason\" :content=\"item.failureReason\" placement=\"top\">\r\n                    <div class=\"info-reason\">\r\n                      <i class=\"el-icon-warning-outline\"></i>\r\n                      <span class=\"reason-text\">{{ item.failureReason }}</span>\r\n                    </div>\r\n                  </el-tooltip>\r\n                </div>\r\n                <div class=\"alert-item-footer\">\r\n                  <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-user-solid\" @click=\"handleAssignEngineer(item)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\">\r\n                    分配工程师\r\n                  </el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-close\" @click=\"handleRejectUnassigned(item)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" class=\"reject-btn\" style=\"color: #F56C6C;\">\r\n                    驳回\r\n                  </el-button>\r\n                </div>\r\n              </el-card>\r\n           </el-col>\r\n          </el-row>\r\n         </div>\r\n      </el-collapse-transition>\r\n    </el-card>\r\n\r\n    <!-- 筛选条件 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"客户\" prop=\"customerId\">\r\n        <el-select v-model=\"queryParams.customerId\" filterable placeholder=\"客户\" clearable size=\"small\">\r\n          <el-option\r\n            v-for=\"dict in customerOptions\"\r\n            :key=\"dict.id\"\r\n            :label=\"dict.name\"\r\n            :value=\"dict.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"日期范围\" prop=\"dateRange\" label-width=\"80px\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"\r\n          style=\"width: 240px\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n        <el-date-picker\r\n          v-model=\"scheduledDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"申请日期\" prop=\"startDate\">\r\n        <el-date-picker\r\n          v-model=\"startDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"研发组别\" prop=\"deptIds\">\r\n        <el-select v-model=\"queryParams.deptIds\" multiple filterable placeholder=\"请选择对应研发组别\">\r\n          <el-option\r\n            v-for=\"dept in researchDeptDatas\"\r\n            :key=\"dept.deptId\"\r\n            :label=\"dept.deptName\"\r\n            :value=\"dept.deptId\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"服务模式\" prop=\"serviceMode\">\r\n        <el-select v-model=\"queryParams.serviceMode\" placeholder=\"请选择客户服务模式\" clearable>\r\n          <el-option\r\n            v-for=\"dict in serviceModeOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"实验室\" prop=\"laboratory\">\r\n        <el-select v-model=\"queryParams.laboratory\" placeholder=\"请选择实验室\" clearable>\r\n          <el-option label=\"宜侬\" value=\"0\" />\r\n          <el-option label=\"瀛彩\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"确认编码\" prop=\"confirmCode\">\r\n        <el-input\r\n          v-model.trim=\"queryParams.confirmCode\"\r\n          placeholder=\"请输入确认编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"产品名称\" prop=\"productName\">\r\n        <el-input\r\n          v-model.trim=\"queryParams.productName\"\r\n          placeholder=\"请输入产品名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"姓名\" prop=\"nickName\" label-width=\"50px\">\r\n        <el-input v-model.trim=\"queryParams.nickName\" placeholder=\"请输入工程师姓名\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"打样单编号\" label-width=\"90px\" prop=\"sampleOrderCode\">\r\n        <el-input v-model.trim=\"queryParams.sampleOrderCode\" placeholder=\"请输入打样单编号\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n        <el-select v-model=\"queryParams.completionStatus\" placeholder=\"请选择完成情况\" clearable>\r\n          <el-option\r\n            v-for=\"dict in statusOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualStartTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualFinishTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:add']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:edit']\">修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:remove']\">删除</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"doExportSampleOrder\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleBatchExportNrw\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出打样单任务</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAddSampleOrder\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:add']\">新增打样单</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 主要内容区域 -->\r\n     <!-- 工单列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"engineerSampleOrderList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"主键ID\" align=\"center\" prop=\"id\" /> -->\r\n<!--      <el-table-column label=\"工程师ID\" align=\"center\" prop=\"userId\" />-->\r\n      <el-table-column align=\"center\" prop=\"sampleOrderCode\" width=\"160\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          打样单编号\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              点击编号可查看详细信息\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"orderDetail(scope.row)\">{{ scope.row.sampleOrderCode }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"startDate\" width=\"140\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          申请日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              客户提交打样申请的日期时间\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"scheduledDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          排单日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              工程师被安排处理此打样单的日期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.scheduledDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"latestStartTime\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚开始日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              必须在此日期前开始工作，否则可能影响交期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"endDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚截至日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作必须在此日期前完成\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\" prop=\"completionStatus\">\r\n        <template slot=\"header\">\r\n          完成情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              当前打样单的状态\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"scope.row.completionStatus == '0' ? 'info' :\r\n                   scope.row.completionStatus == '1' ? 'primary' :\r\n                   scope.row.completionStatus == '2' ? 'success' : 'info'\"\r\n            size=\"mini\"\r\n          >\r\n            {{selectDictLabel(statusOptions,scope.row.completionStatus)}}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          批次信息\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              显示当前批次总数\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.totalBatches > 0\">\r\n            <el-tag size=\"mini\" type=\"primary\">\r\n              {{ scope.row.totalBatches || 0 }}\r\n            </el-tag>\r\n          </div>\r\n          <div v-else>\r\n            -\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          逾期情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              根据截止日期判断是否逾期及逾期天数\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"getOverdueInfo(scope.row).isOverdue\">\r\n            <el-tag type=\"danger\" size=\"mini\" style=\"margin-bottom: 2px;\">\r\n              逾期 {{ getOverdueInfo(scope.row).overdueDays }}天\r\n            </el-tag>\r\n          </div>\r\n          <el-tag v-else type=\"success\" size=\"mini\">\r\n            正常\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"nickName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          跟进人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的工程师姓名及职级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.nickName }}{{ scope.row.rank ? '-' + scope.row.rank : '' }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"assistantName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          协助人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              此打样单难度高于工程师能力范围\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.assistantName }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"isLocked\" width=\"90\">\r\n        <template slot=\"header\">\r\n          锁定状态\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              锁定后不允许更换工程师，防止误操作\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.isLocked\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            @change=\"(val) => handleTableLockChange(val, scope.row)\">\r\n          </el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratoryCode\" width=\"150\">\r\n        <template slot=\"header\">\r\n          实验编码\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              实验室分配的唯一编码标识\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"difficultyLevelId\" width=\"80\">\r\n        <template slot=\"header\">\r\n          难度\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作的技术难度等级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"selectDictLabel(dylbOptions,scope.row.difficultyLevelId)\" placement=\"top\">\r\n            <span>{{selectDictLabel(dylbOptions,scope.row.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"标准工时\" align=\"center\" prop=\"standardManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.standardManHours\">{{ scope.row.standardManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratory\" width=\"90\">\r\n        <template slot=\"header\">\r\n          实验室\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的实验室分支\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{\r\n            scope.row.laboratory === '0' ? '宜侬' :\r\n            scope.row.laboratory === '1' ? '瀛彩' :\r\n            ''\r\n          }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"categoryName\" width=\"70\">\r\n        <template slot=\"header\">\r\n          品类\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              产品所属的分类类别\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"SKU数\" align=\"center\" prop=\"sku\" width=\"70\" />\r\n      <el-table-column align=\"center\" prop=\"applicant\" width=\"90\">\r\n        <template slot=\"header\">\r\n          申请人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              提交此打样申请的人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"sales\" width=\"70\">\r\n        <template slot=\"header\">\r\n          销售\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此项目的销售人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"customerName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          客户名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              委托打样的客户公司名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"产品/项目等级\" align=\"center\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getProjectLevel(scope.row) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"productName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          产品名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              需要打样的产品名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工作时间\" align=\"center\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div v-if=\"scope.row.actualStartTime\">开始: {{ parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <div v-if=\"scope.row.actualFinishTime\">完成: {{ parseTime(scope.row.actualFinishTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <span v-if=\"!scope.row.actualStartTime && !scope.row.actualFinishTime\">-</span>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工时\" align=\"center\" prop=\"actualManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.actualManHours\">{{ scope.row.actualManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预估工时\" align=\"center\" prop=\"estimatedManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.estimatedManHours\">{{ scope.row.estimatedManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"服务模式\" align=\"center\" prop=\"serviceMode\" width=\"150\" />\r\n      <el-table-column label=\"计算类型\" align=\"center\" prop=\"checkType\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.checkType == 1\">客户等级</span>\r\n          <span v-else>打样单难度</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"完成质量\" align=\"center\" prop=\"qualityEvaluation\" />\r\n<!--      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"140\">-->\r\n<!--        <template slot-scope=\"scope\">-->\r\n<!--          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n      <el-table-column label=\"打样单备注\" align=\"center\" prop=\"sampleOrderRemark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.sampleOrderRemark\" placement=\"top\" :disabled=\"!scope.row.sampleOrderRemark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.sampleOrderRemark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.remark\" placement=\"top\" :disabled=\"!scope.row.remark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.remark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"未出样原因\" align=\"center\" prop=\"reasonForNoSample\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.reasonForNoSample\" placement=\"top\" :disabled=\"!scope.row.reasonForNoSample\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.reasonForNoSample || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"解决方案\" align=\"center\" prop=\"solution\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.solution\" placement=\"top\" :disabled=\"!scope.row.solution\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.solution || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"驳回原因\" align=\"center\" prop=\"rejectReason\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.rejectReason\" placement=\"top\" :disabled=\"!scope.row.rejectReason\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.rejectReason || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"fixed-width\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- <el-tooltip content=\"编辑\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:edit']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"删除\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:remove']\" />\r\n          </el-tooltip> -->\r\n          <el-tooltip  v-if=\"scope.row.completionStatus==0\" content=\"更换工程师或更改日期\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-user\" v-if=\"scope.row.isLocked === 0\" @click=\"handleChangeEngineer(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\" />\r\n          </el-tooltip>\r\n\r\n\r\n          <el-tooltip content=\"批次管理\" v-if=\"[0,1,2].includes(scope.row.completionStatus)\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-collection\" @click=\"handleBatchManagement(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:batchManagement']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"逾期操作\" v-if=\"getOverdueInfo(scope.row).isOverdue\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-warning\" @click=\"handleOverdueOperation(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:overdueOperation']\" style=\"color: #E6A23C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"驳回\" v-if=\"scope.row.completionStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-close\" @click=\"handleReject(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" style=\"color: #F56C6C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"更新实验室编码\" v-if=\"scope.row.completionStatus == '2' && scope.row.itemStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit-outline\" @click=\"handleUpdateLaboratoryCode(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:editSampleOrderCode']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"下载打样单\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-download\" @click=\"handleDownloadSampleOrder(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:export']\" />\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      :auto-scroll=\"false\" @pagination=\"getList\" />\r\n\r\n    <!-- 添加或修改工程师打样单关联对话框 -->\r\n    <!-- <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"650px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程师ID\" prop=\"userId\">\r\n              <el-input v-model=\"form.userId\" placeholder=\"请输入工程师ID\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"打样单编号\" prop=\"sampleOrderCode\">\r\n              <el-input v-model=\"form.sampleOrderCode\" placeholder=\"请输入打样单编号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"难度\" prop=\"difficultyLevelId\">\r\n              <el-select v-model=\"form.difficultyLevelId\" placeholder=\"请选择打样难度等级\">\r\n                  <el-option\r\n                    v-for=\"dict in dylbOptions\"\r\n                    :key=\"dict.dictValue\"\r\n                    :label=\"dict.dictLabel\"\r\n                    :value=\"dict.dictValue\"\r\n                  ></el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n              <el-select v-model=\"form.completionStatus\" placeholder=\"请选择完成情况\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in statusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n              <el-date-picker\r\n                v-model=\"form.scheduledDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择排单日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"最晚截至日期\" prop=\"endDate\">\r\n              <el-date-picker\r\n                v-model=\"form.endDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择最晚截至日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"锁定状态\" prop=\"isLocked\">\r\n              <el-switch\r\n                v-model=\"form.isLocked\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n                active-text=\"已锁定\"\r\n                inactive-text=\"未锁定\"\r\n                @change=\"handleLockChange\">\r\n              </el-switch>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualStartTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择开始时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualFinishTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择完成时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际工时\" prop=\"actualManHours\">\r\n              <el-input-number v-model=\"form.actualManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入实际工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"预估工时\" prop=\"estimatedManHours\">\r\n              <el-input-number v-model=\"form.estimatedManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入预估工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"完成质量\" prop=\"qualityEvaluation\">\r\n              <el-input v-model=\"form.qualityEvaluation\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入完成质量情况\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"打样单备注\" prop=\"sampleOrderRemark\">\r\n              <el-input v-model=\"form.sampleOrderRemark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入打样单备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n\r\n    <!-- 新增打样单对话框 -->\r\n    <el-dialog title=\"新增打样单\" :visible.sync=\"addSampleOrderOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"addSampleOrderForm\" :model=\"addSampleOrderForm\" :rules=\"addSampleOrderRules\" label-width=\"100px\">\r\n        <el-form-item label=\"实验室\" prop=\"labNo\">\r\n          <el-select v-model=\"addSampleOrderForm.labNo\" placeholder=\"请选择实验室\" style=\"width: 100%\">\r\n            <el-option label=\"宜侬\" value=\"0\" />\r\n            <el-option label=\"瀛彩\" value=\"1\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"类别\" prop=\"categoryText\">\r\n          <el-select v-model=\"addSampleOrderForm.categoryText\" filterable placeholder=\"请选择类别\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in sckxxpgCplbs\"\r\n              :key=\"dict.dictValue\"\r\n              :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"难度\" prop=\"dylb\">\r\n          <el-select v-model=\"addSampleOrderForm.dylb\" placeholder=\"请选择难度\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in dylbOptions\"\r\n              :key=\"dict.dictValue\"\r\n              :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"addSampleOrderForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAddSampleOrder\" :loading=\"addSampleOrderLoading\">确 定</el-button>\r\n        <el-button @click=\"cancelAddSampleOrder\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 更改工程师对话框 -->\r\n    <el-dialog title=\"更改工程师\" :visible.sync=\"changeEngineerOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"changeEngineerForm\" :model=\"changeEngineerForm\" :rules=\"changeEngineerRules\" label-width=\"150px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"changeEngineerForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"当前工程师\">\r\n          <el-input v-model=\"changeEngineerForm.currentEngineerName\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"选择工程师\" prop=\"newEngineerId\">\r\n          <el-radio-group v-model=\"engineerSelectType\" style=\"margin-bottom: 15px;\">\r\n            <el-radio label=\"specified\">指定部门工程师</el-radio>\r\n            <el-radio label=\"other\">其他部门工程师</el-radio>\r\n          </el-radio-group>\r\n\r\n          <!-- 指定工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'specified'\">\r\n            <el-select\r\n              v-model=\"changeEngineerForm.newEngineerId\"\r\n              placeholder=\"请选择工程师\"\r\n              style=\"width: 100%\"\r\n              filterable>\r\n              <el-option\r\n                v-for=\"engineer in engineerOptions\"\r\n                :key=\"engineer.userId\"\r\n                :label=\"engineer.nickName\"\r\n                :value=\"engineer.userId\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n\r\n          <!-- 其他工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'other'\" class=\"other-engineer-select\">\r\n            <div class=\"select-item\" style=\"margin-top: 10px;\">\r\n              <el-select\r\n                v-model=\"changeEngineerForm.newEngineerId\"\r\n                placeholder=\"请选择工程师\"\r\n                style=\"width: 100%\"\r\n                filterable\r\n                remote>\r\n                <el-option\r\n                  v-for=\"engineer in searchedEngineers\"\r\n                  :key=\"engineer.userId\"\r\n                  :label=\"engineer.nickName\"\r\n                  :value=\"engineer.userId\"\r\n                />\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"选择指定日期\" prop=\"scheduledDate\">\r\n          <el-date-picker\r\n            v-model=\"changeEngineerForm.scheduledDate\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"重新调整工作安排\">\r\n          <el-switch\r\n            v-model=\"changeEngineerForm.adjustWorkSchedule\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            active-text=\"是\"\r\n            inactive-text=\"否\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitChangeEngineer\">确 定</el-button>\r\n        <el-button @click=\"changeEngineerOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次管理对话框 -->\r\n    <el-dialog title=\"批次管理\" :visible.sync=\"batchManagementOpen\" width=\"900px\" append-to-body @close=\"closeBatchManagement\">\r\n      <div class=\"batch-management-container\">\r\n        <!-- 状态提示信息 -->\r\n        <el-alert\r\n          v-if=\"!canEditBatch\"\r\n          title=\"当前打样单状态不允许编辑批次信息\"\r\n          type=\"warning\"\r\n          description=\"只有状态为'进行中'的打样单才可以编辑和添加测试批次内容，其他状态只能查看批次信息。\"\r\n          show-icon\r\n          :closable=\"false\"\r\n          style=\"margin-bottom: 20px;\">\r\n        </el-alert>\r\n\r\n        <!-- 进行中批次信息 -->\r\n        <el-card class=\"active-batches-card\" v-if=\"activeBatches && activeBatches.length > 0\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>进行中批次 ({{ activeBatches.length }}个)</span>\r\n            <el-button\r\n              v-if=\"canEditBatch\"\r\n              style=\"float: right; padding: 3px 0\"\r\n              type=\"text\"\r\n              @click=\"finishAllActiveBatches\">\r\n              结束所有批次\r\n            </el-button>\r\n            <el-tag\r\n              v-else\r\n              style=\"float: right;\"\r\n              type=\"info\"\r\n              size=\"mini\">\r\n              只读模式\r\n            </el-tag>\r\n          </div>\r\n          <!-- 进行中批次列表 -->\r\n          <el-table :data=\"activeBatches\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"batchIndex\" label=\"批次序号\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"primary\">第{{ scope.row.batchIndex }}批次</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"已用时长\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ calculateDuration(scope.row.startTime) }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"实验室编号\" width=\"200\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"experiment-codes\">\r\n                  <el-tag\r\n                    v-for=\"experiment in scope.row.experiments || []\"\r\n                    :key=\"experiment.id\"\r\n                    size=\"mini\"\r\n                    closable\r\n                    @close=\"removeExperiment(scope.row, experiment)\"\r\n                    style=\"margin: 2px;\"\r\n                  >\r\n                    {{ experiment.experimentCode }}\r\n                  </el-tag>\r\n                  <el-button\r\n                    v-if=\"canEditBatch\"\r\n                    type=\"text\"\r\n                    size=\"mini\"\r\n                    @click=\"showAddExperimentDialog(scope.row)\"\r\n                    icon=\"el-icon-plus\"\r\n                  >\r\n                    添加编号\r\n                  </el-button>\r\n                  <span v-if=\"(!scope.row.experiments || scope.row.experiments.length === 0) && !canEditBatch\" style=\"color: #C0C4CC;\">未设置</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"状态\" width=\"80\" align=\"center\">\r\n              <template>\r\n                <el-tag type=\"success\" size=\"mini\">进行中</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"150\" align=\"center\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"canEditBatch\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"finishSingleBatch(scope.row)\">\r\n                  结束批次\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"viewBatchDetail(scope.row)\" icon=\"el-icon-view\">\r\n                  详情\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n        </el-card>\r\n\r\n        <!-- 开始新批次按钮 -->\r\n        <div class=\"new-batch-section\" style=\"text-align: center; margin: 20px 0;\">\r\n          <el-button\r\n            v-if=\"canEditBatch\"\r\n            type=\"primary\"\r\n            size=\"medium\"\r\n            @click=\"startNewBatch\">\r\n            开始新批次\r\n          </el-button>\r\n          <div v-else style=\"text-align: center; color: #909399;\">\r\n            <i class=\"el-icon-info\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"margin-top: 16px;\">当前状态不允许开始新批次</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 所有批次列表 -->\r\n        <el-card class=\"all-batches-card\" style=\"margin-top: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>所有批次 ({{ allBatches.length }}个)</span>\r\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshBatchData\">刷新</el-button>\r\n          </div>\r\n          <el-table :data=\"allBatches\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"batchIndex\" label=\"批次序号\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"primary\">第{{ scope.row.batchIndex }}批次</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.endTime\">{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n                <span v-else style=\"color: #909399;\">未结束</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"batchStatus\" label=\"状态\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag :type=\"getBatchStatusType(scope.row.batchStatus)\" size=\"mini\">\r\n                  {{ getBatchStatusText(scope.row.batchStatus) }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"laboratoryCode\" label=\"实验室编号\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.laboratoryCode\" style=\"color: #606266;\">{{ scope.row.laboratoryCode }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">未设置</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"actualManHours\" label=\"实际工时\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" :type=\"scope.row.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ scope.row.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"qualityEvaluation\" label=\"质量评价\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag v-if=\"scope.row.qualityEvaluation\" size=\"mini\"\r\n                        :type=\"getQualityEvaluationType(scope.row.qualityEvaluation)\">\r\n                  {{ scope.row.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else style=\"color: #909399;\">未评价</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remark\" label=\"批次备注\" min-width=\"150\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.remark\" style=\"color: #606266;\">{{ scope.row.remark }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"canEditBatch && scope.row.batchStatus === 0\"\r\n                  size=\"mini\"\r\n                  type=\"success\"\r\n                  @click=\"startSingleBatch(scope.row)\">\r\n                  开始\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"canEditBatch && scope.row.batchStatus === 1\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"finishSingleBatch(scope.row)\">\r\n                  结束\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"viewBatchDetail(scope.row)\" icon=\"el-icon-view\">\r\n                  详情\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 空状态 -->\r\n          <div v-if=\"!allBatches || allBatches.length === 0\" class=\"empty-state\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-document\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">暂无批次记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button\r\n          v-if=\"currentBatchRow && currentBatchRow.completionStatus == '0'\"\r\n          type=\"success\"\r\n          icon=\"el-icon-video-play\"\r\n          @click=\"handleStartFromBatch\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:startRask']\">\r\n          开始任务\r\n        </el-button>\r\n        <el-button\r\n          v-if=\"currentBatchRow && currentBatchRow.completionStatus == '1'\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-check\"\r\n          @click=\"handleFinishFromBatch\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:endRask']\">\r\n          完成任务\r\n        </el-button>\r\n        <el-button @click=\"batchManagementOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次详情对话框 -->\r\n    <el-dialog title=\"批次详情\" :visible.sync=\"batchDetailOpen\" width=\"800px\" append-to-body @close=\"closeBatchDetail\">\r\n      <div v-if=\"batchDetailData\" class=\"batch-detail-container\">\r\n        <!-- 批次基本信息 -->\r\n        <el-card class=\"batch-info-card\" style=\"margin-bottom: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>批次基本信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>批次序号：</label>\r\n                <el-tag type=\"primary\">第{{ batchDetailData.batchIndex }}批次</el-tag>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>开始时间：</label>\r\n                <span class=\"info-value\">{{ parseTime(batchDetailData.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>结束时间：</label>\r\n                <span class=\"info-value\">\r\n                  {{ batchDetailData.endTime ? parseTime(batchDetailData.endTime, '{y}-{m}-{d} {h}:{i}:{s}') : '未结束' }}\r\n                </span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>实际工时：</label>\r\n                <el-tag :type=\"batchDetailData.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ batchDetailData.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>质量评价：</label>\r\n                <el-tag v-if=\"batchDetailData.qualityEvaluation\"\r\n                        :type=\"getQualityEvaluationType(batchDetailData.qualityEvaluation)\">\r\n                  {{ batchDetailData.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else class=\"info-value\">未评价</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row style=\"margin-top: 15px;\">\r\n            <el-col :span=\"24\">\r\n              <div class=\"info-item\">\r\n                <label>批次备注：</label>\r\n                <div class=\"remark-content\">\r\n                  {{ batchDetailData.remark || '无备注' }}\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n\r\n        <!-- 实验记录详情 -->\r\n        <el-card class=\"experiments-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>实验记录详情</span>\r\n            <el-tag size=\"mini\" style=\"margin-left: 10px;\">\r\n              共{{ batchDetailData.experiments ? batchDetailData.experiments.length : 0 }}条记录\r\n            </el-tag>\r\n          </div>\r\n          <el-table :data=\"batchDetailData.experiments\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"experimentCode\" label=\"实验编号\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span style=\"color: #409EFF; font-weight: bold;\">{{ scope.row.experimentCode }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"experimentNote\" label=\"实验备注\" min-width=\"200\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.experimentNote\">{{ scope.row.experimentNote }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"info\">{{ scope.row.createBy || '系统' }}</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 实验记录空状态 -->\r\n          <div v-if=\"!batchDetailData.experiments || batchDetailData.experiments.length === 0\"\r\n               class=\"empty-experiments\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-data-line\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">该批次暂无实验记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"batchDetailOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加实验记录对话框 -->\r\n    <el-dialog title=\"添加实验室编号\" :visible.sync=\"addExperimentOpen\" width=\"500px\" append-to-body @close=\"closeAddExperiment\">\r\n      <el-form :model=\"addExperimentForm\" ref=\"addExperimentForm\" label-width=\"100px\" :rules=\"addExperimentRules\">\r\n        <el-form-item label=\"实验编号\" prop=\"experimentCode\">\r\n          <el-input v-model=\"addExperimentForm.experimentCode\" placeholder=\"请输入实验编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"实验备注\">\r\n          <el-input v-model=\"addExperimentForm.experimentNote\" type=\"textarea\" placeholder=\"请输入实验备注\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"addExperimentForm.remark\" type=\"textarea\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAddExperiment\">确 定</el-button>\r\n        <el-button @click=\"addExperimentOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 结束批次对话框 -->\r\n    <el-dialog :title=\"finishBatchMode === 'single' ? `结束第${currentFinishBatch && currentFinishBatch.batchIndex || ''}批次` : '结束当前批次'\" :visible.sync=\"finishBatchOpen\" width=\"500px\" append-to-body @close=\"closeFinishBatch\">\r\n      <el-form :model=\"finishBatchForm\" ref=\"finishBatchForm\" label-width=\"100px\">\r\n        <el-form-item label=\"质量评价\">\r\n          <el-input v-model=\"finishBatchForm.qualityEvaluation\" type=\"textarea\" placeholder=\"请输入质量评价\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishBatchForm.remark\" type=\"textarea\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFinishBatch\">确 定</el-button>\r\n        <el-button @click=\"finishBatchOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 完成任务对话框 -->\r\n    <el-dialog title=\"完成任务\" :visible.sync=\"finishTaskOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"finishTaskForm\" :model=\"finishTaskForm\" :rules=\"finishTaskRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-select v-model=\"finishTaskForm.laboratoryCode\" multiple filterable placeholder=\"请选择实验室编码\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"item in experimentCodeList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.experimentCode\"\r\n              :value=\"item.experimentCode\">\r\n              <span style=\"float: left\">{{ item.experimentCode }}</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 13px\" v-if=\"item.experimentNote\">{{ item.experimentNote }}</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishTaskForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"finishTaskOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmFinishTask\" :loading=\"finishTaskLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 更新实验室编码对话框 -->\r\n    <el-dialog title=\"更新实验室编码\" :visible.sync=\"updateLaboratoryCodeOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"updateLaboratoryCodeForm\" :model=\"updateLaboratoryCodeForm\" :rules=\"updateLaboratoryCodeRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.laboratoryCode\" placeholder=\"请输入实验室编码(多个使用,分割)\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"updateLaboratoryCodeOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmUpdateLaboratoryCode\" :loading=\"updateLaboratoryCodeLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导出选择对话框 -->\r\n    <el-dialog title=\"选择导出内容\" :visible.sync=\"exportDialogOpen\" width=\"400px\" append-to-body>\r\n      <div class=\"export-options\">\r\n        <p style=\"margin-bottom: 20px; color: #606266;\">请选择要导出的打样单类型：</p>\r\n        <el-checkbox-group v-model=\"exportOptions\" style=\"display: flex; flex-direction: column;\">\r\n          <el-checkbox label=\"assigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">已分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出已分配给工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n          <el-checkbox label=\"unassigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">未分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出尚未分配工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n        </el-checkbox-group>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"exportDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmExport\" :disabled=\"exportOptions.length === 0\" :loading=\"exportLoading\">\r\n          确认导出\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 驳回对话框 -->\r\n    <el-dialog title=\"驳回打样单\" :visible.sync=\"rejectDialogOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" :rules=\"rejectRules\" label-width=\"100px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"rejectForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"驳回理由\" prop=\"rejectReason\">\r\n          <el-input v-model=\"rejectForm.rejectReason\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入驳回理由\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"rejectDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmReject\" :loading=\"rejectLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 逾期操作对话框 -->\r\n    <el-dialog title=\"逾期操作\" :visible.sync=\"overdueOperationOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"overdueOperationForm\" :model=\"overdueOperationForm\" label-width=\"120px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"overdueOperationForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"预计出样时间\">\r\n          <el-date-picker\r\n            v-model=\"overdueOperationForm.expectedSampleTime\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"未出样原因\">\r\n          <el-input v-model=\"overdueOperationForm.reasonForNoSample\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入未出样原因（非必填）\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"解决方案\">\r\n          <el-input v-model=\"overdueOperationForm.solution\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入解决方案（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"overdueOperationOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOverdueOperation\" :loading=\"overdueOperationLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n    <!-- 添加或修改项目流程进行对话框 -->\r\n    <executionAddOrEdit ref=\"executionAddOrEdit\"></executionAddOrEdit>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listEngineerSampleOrder,\r\n  getEngineerSampleOrder,\r\n  delEngineerSampleOrder,\r\n  addEngineerSampleOrder,\r\n  updateEngineerSampleOrder,\r\n  updateSampleOrderStatus,\r\n  getDashboardStats,\r\n  getResearchDepartments,\r\n  getEngineersByDifficultyLevel,\r\n  changeEngineer,\r\n  getResearchDepartmentsUser,\r\n  getBatchesByOrderId,\r\n  startNewBatch,\r\n  addExperimentToBatch,\r\n  getExperimentsByBatchId,\r\n  exportEngineerSampleOrder,\r\n  getExecutionByOrderInfo,\r\n  getBatchExperimentCodeList,\r\n  getActiveBatches,\r\n  startSingleBatch,\r\n  finishSingleBatch,\r\n  finishAllActiveBatches,\r\n  removeExperimentFromBatch\r\n} from \"@/api/software/engineerSampleOrder\";\r\nimport {exportNrwItem, exportMultipleNrw} from \"@/api/project/projectItemOrder\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\nimport {customerBaseAll} from \"@/api/customer/customer\";\r\nimport {isNull} from \"@/utils/validate\";\r\nimport executionAddOrEdit from \"@/components/Project/components/executionAddOrEdit.vue\";\r\n\r\nexport default {\r\n  name: \"EngineerSampleOrder\",\r\n  components: {\r\n     Treeselect,executionAddOrEdit\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中的完整行数据\r\n      selectedRows: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 工程师打样单关联表格数据\r\n      engineerSampleOrderList: [],\r\n      // 研发部部门树列表\r\n      researchDeptDatas:[],\r\n      // 打样类别字典\r\n      dylbOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userId: null,\r\n        nickName: null,\r\n        sampleOrderCode: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        startDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        deptId: null,\r\n        deptIds: [],\r\n        associationStatus: null,\r\n        customerId: null,\r\n        productName: null,\r\n        confirmCode: null,\r\n        isOverdue: null,  // 增逾期任务过滤参数\r\n        laboratory: null  // 实验室筛选参数\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        userId: [\r\n          { required: true, message: \"工程师ID不能为空\", trigger: \"blur\" }\r\n        ],\r\n        sampleOrderCode: [\r\n          { required: true, message: \"打样单编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        completionStatus: [\r\n          { required: true, message: \"完成情况不能为空\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      statusOptions: [],\r\n      serviceModeOptions: [],\r\n      dateRange: [], // 新增的日期范围筛选\r\n      scheduledDateRange: [],\r\n      startDateRange: [],\r\n      actualStartTimeRange: [],\r\n      actualFinishTimeRange: [],\r\n      // 日期选择器配置\r\n      dataPickerOptions: {\r\n        shortcuts: [{\r\n          text: '今天',\r\n          onClick(picker) {\r\n            const today = new Date();\r\n            picker.$emit('pick', [today, today]);\r\n          }\r\n        }, {\r\n          text: '昨天',\r\n          onClick(picker) {\r\n            const yesterday = new Date();\r\n            yesterday.setTime(yesterday.getTime() - 3600 * 1000 * 24);\r\n            picker.$emit('pick', [yesterday, yesterday]);\r\n          }\r\n        }, {\r\n          text: '最近一周',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近一个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }]\r\n      },\r\n      // 状态更新对话框\r\n      statusOpen: false,\r\n      dashboardStats: {\r\n        \"total\": 0,\r\n        \"completed\": 0,\r\n        \"inProgress\": 0,\r\n        \"overdue\": 0\r\n      },\r\n      // 更改工程师对话框\r\n      changeEngineerOpen: false,\r\n      // 更改工程师表单\r\n      changeEngineerForm: {\r\n        id: null,\r\n        sampleOrderCode: null,\r\n        currentEngineerName: null,\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      },\r\n      // 更改工程师表单校验\r\n      changeEngineerRules: {\r\n        newEngineerId: [\r\n          { required: true, message: \"请选择工程师\", trigger: \"change\" }\r\n        ],\r\n        scheduledDate: [\r\n          { required: true, message: \"请选择日期\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 工程师选项\r\n      engineerOptions: [],\r\n      // 批次管理相关\r\n      batchManagementOpen: false,\r\n      activeBatches: [], // 进行中的批次列表\r\n      allBatches: [], // 所有批次列表\r\n      selectedOrderForBatch: null,\r\n      currentBatchRow: null, // 当前批次管理的行数据\r\n      // 批次详情对话框\r\n      batchDetailOpen: false,\r\n      batchDetailData: null,\r\n      // 结束批次对话框\r\n      finishBatchOpen: false,\r\n      finishBatchForm: {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      },\r\n      currentFinishBatch: null, // 当前要结束的批次（单个批次时使用）\r\n      finishBatchMode: 'all', // 'all' 表示结束所有批次，'single' 表示结束单个批次\r\n      // 添加实验记录对话框\r\n      addExperimentOpen: false,\r\n      addExperimentForm: {\r\n        experimentCode: '',\r\n        experimentNote: '',\r\n        remark: ''\r\n      },\r\n      addExperimentRules: {\r\n        experimentCode: [\r\n          { required: true, message: '实验编号不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      currentBatchForExperiment: null,\r\n      // 未来日期选择器配置\r\n      futureDatePickerOptions: {\r\n        disabledDate(time) {\r\n          return time.getTime() < Date.now() - 8.64e7; // 禁用今天之前的日期\r\n        }\r\n      },\r\n      engineerSelectType: 'specified',\r\n      searchedEngineers: [],\r\n      // 未分配打样单告警\r\n      unassignedOrders: [],\r\n      unassignedQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 8\r\n      },\r\n      unassignedTotal: 0,\r\n      // 未分配面板折叠状态\r\n      isUnassignedPanelCollapsed: true,\r\n      // 完成任务对话框\r\n      finishTaskOpen: false,\r\n      finishTaskLoading: false,\r\n      finishTaskForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      experimentCodeList: [], // 实验室编码列表\r\n      finishTaskRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentFinishRow: null,\r\n      // 更新实验室编码对话框\r\n      updateLaboratoryCodeOpen: false,\r\n      updateLaboratoryCodeLoading: false,\r\n      updateLaboratoryCodeForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      updateLaboratoryCodeRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentUpdateLaboratoryCodeRow: null,\r\n      // 导出选择对话框\r\n      exportDialogOpen: false,\r\n      exportOptions: [],\r\n      exportLoading: false,\r\n      readonly: true,\r\n      // 项目详情相关\r\n      currentProjectType: null,\r\n      confirmItemCodes: [],\r\n      customerOptions: [],\r\n      itemNames: [],\r\n      // 驳回对话框\r\n      rejectDialogOpen: false,\r\n      rejectLoading: false,\r\n      rejectForm: {\r\n        sampleOrderCode: '',\r\n        rejectReason: ''\r\n      },\r\n      // 逾期操作对话框\r\n      overdueOperationOpen: false,\r\n      overdueOperationLoading: false,\r\n      overdueOperationForm: {\r\n        sampleOrderCode: '',\r\n        expectedSampleTime: '',\r\n        reasonForNoSample: '',\r\n        solution: ''\r\n      },\r\n      currentOverdueRow: null,\r\n      rejectRules: {\r\n        rejectReason: [\r\n          { required: true, message: \"驳回理由不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentRejectRow: null,\r\n      // 新增打样单对话框\r\n      addSampleOrderOpen: false,\r\n      addSampleOrderLoading: false,\r\n      addSampleOrderForm: {\r\n        labNo: '',\r\n        categoryText: '',\r\n        dylb: '',\r\n        remark: ''\r\n      },\r\n      addSampleOrderRules: {\r\n        labNo: [\r\n          { required: true, message: \"实验室不能为空\", trigger: \"change\" }\r\n        ],\r\n        categoryText: [\r\n          { required: true, message: \"品类不能为空\", trigger: \"change\" }\r\n        ],\r\n        dylb: [\r\n          { required: true, message: \"打样单难度类别不能为空\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 产品类别选项\r\n      sckxxpgCplbs: []\r\n    };\r\n  },\r\n  computed: {\r\n    /** 计算是否可以编辑批次 - 只有状态为'1'(进行中)的打样单才可以编辑 */\r\n    canEditBatch() {\r\n      return this.selectedOrderForBatch && this.selectedOrderForBatch.completionStatus === 1;\r\n    }\r\n  },\r\n  created() {\r\n    // 设置默认日期范围为当天\r\n    const today = new Date();\r\n    const todayStr = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');\r\n    this.dateRange = [todayStr, todayStr];\r\n\r\n    this.getList();\r\n    customerBaseAll().then(res=> this.customerOptions = res)\r\n\r\n    this.getDicts(\"DYD_GCSZT\").then(response => {\r\n        this.statusOptions = response.data;\r\n    });\r\n    this.getDicts(\"CUSTOMER_SERVICE_MODE\").then(response => {\r\n      this.serviceModeOptions = response.data;\r\n    });\r\n    this.getDicts(\"project_nrw_dylb\").then(response => {\r\n      this.dylbOptions = response.data;\r\n    });\r\n    this.getDicts(\"SCKXXPG_CPLB\").then(response => {\r\n      this.sckxxpgCplbs = response.data;\r\n    });\r\n    this.loadDashboardStats();\r\n    // 获取研发部部门列表\r\n    getResearchDepartments().then(response => {\r\n      this.researchDeptDatas = this.handleTree(response.data, \"deptId\");\r\n    });\r\n    // 获取未分配打样单\r\n    this.handleUnassignedOrders();\r\n  },\r\n  methods: {\r\n    /** 计算产品/项目等级 */\r\n    getProjectLevel(row) {\r\n      const customerLevel = row.customerLevel || '';\r\n      const projectLevel = row.projectLevel || '';\r\n\r\n      // 如果 projectLevel 是空字符串或 \"/\" 就不相加\r\n      if (projectLevel === '' || projectLevel === '/') {\r\n        return customerLevel;\r\n      }\r\n\r\n      return customerLevel + projectLevel;\r\n    },\r\n    /** 查询工程师打样单关联列表 */\r\n    getList() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.loading = true;\r\n      params.associationStatus = 1\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.engineerSampleOrderList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        userId: null,\r\n        sampleOrderCode: null,\r\n        serviceMode: null,\r\n        difficultyLevelId: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        actualManHours: null,\r\n        estimatedManHours: null,\r\n        standardManHours: null,\r\n        qualityEvaluation: null,\r\n        isLocked: 0,\r\n        startDate: null,\r\n        endDate: null,\r\n        checkType: null,\r\n        sampleOrderRemark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.unassignedQueryParams.pageNum = 1;\r\n      this.getList();\r\n      this.loadDashboardStats();\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.dateRange = [] // 重置日期范围\r\n      this.scheduledDateRange = []\r\n      this.startDateRange = []\r\n      this.actualStartTimeRange = []\r\n      this.actualFinishTimeRange = []\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.selectedRows = selection\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加工程师打样单关联\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getEngineerSampleOrder(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改工程师打样单关联\";addEngineerSampleOrder\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          } else {\r\n            addEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除工程师打样单关联编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function () {\r\n        return delEngineerSampleOrder(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.msgSuccess(\"删除成功\");\r\n        this.loadDashboardStats();\r\n      }).catch(() => { });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 重置导出选项并显示选择对话框\r\n      this.exportOptions = [];\r\n      this.exportDialogOpen = true;\r\n    },\r\n    /** 确认导出操作 */\r\n    confirmExport() {\r\n      if (this.exportOptions.length === 0) {\r\n        this.$message.warning('请至少选择一种导出类型');\r\n        return;\r\n      }\r\n      this.exportLoading = true;\r\n      this.executeExports(0);\r\n    },\r\n\r\n    /** 串行执行导出操作 */\r\n    executeExports(index) {\r\n      if (index >= this.exportOptions.length) {\r\n        // 所有导出完成\r\n        this.exportLoading = false;\r\n        this.exportDialogOpen = false;\r\n        this.$message.success(`导出完成，共导出${this.exportOptions.length}个文件`);\r\n        return;\r\n      }\r\n\r\n      const option = this.exportOptions[index];\r\n      const associationStatus = option === 'assigned' ? 1 : 0;\r\n      const typeName = option === 'assigned' ? '已分配' : '未分配';\r\n\r\n      this.doExport(associationStatus, option).then(() => {\r\n        this.$message.success(`${typeName}打样单导出成功`);\r\n        // 继续导出下一个\r\n        this.executeExports(index + 1);\r\n      }).catch((error) => {\r\n        this.exportLoading = false;\r\n        this.$message.error(`${typeName}打样单导出失败: ${error.message || error}`);\r\n      });\r\n    },\r\n    /** 执行导出操作 */\r\n    doExport(associationStatus, exportType) {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = associationStatus;\r\n      params.exportType = exportType;\r\n      return exportEngineerSampleOrder(params).then(response => {\r\n        this.download(response.msg);\r\n      });\r\n    },\r\n    /** 执行导出打样单（已分配/未分配） */\r\n    doExportSampleOrder() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.$confirm('是否确认导出所有（已分配/未分配）打样单关联数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportEngineerSampleOrder(params);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"开始\"按钮操作 */\r\n    handleStart(row) {\r\n      this.$confirm('确定要将打样单：' + row.sampleOrderCode + '标记为进行中吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        updateSampleOrderStatus({\r\n          id: row.id,\r\n          status: '1'\r\n        }).then(response => {\r\n          this.msgSuccess('打样单已设置为已开始');\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      });\r\n    },\r\n    /** 下载打样单操作 */\r\n    handleDownloadSampleOrder(row) {\r\n      this.$confirm('是否确认导出打样单?', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportNrwItem({itemId: row.itemId,projectId: row.projectId})\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n      }).catch(() => {});\r\n    },\r\n    /** 批量导出打样单任务操作 */\r\n    handleBatchExportNrw() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$modal.msgError(\"请选择要导出的打样单任务\");\r\n        return;\r\n      }\r\n\r\n      // 构造批量导出数据，传递itemId和projectId\r\n      const exportData = this.selectedRows.map(row => ({\r\n        itemId: row.itemId,\r\n        projectId: row.projectId\r\n      }));\r\n\r\n      this.$confirm('是否确认导出所选打样单任务的内容物数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportMultipleNrw(exportData);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"完成\"按钮操作 */\r\n    handleFinish(row) {\r\n      this.currentFinishRow = row;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      // 加载实验室编码列表\r\n      this.loadExperimentCodeList(row.id);\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认完成任务 */\r\n    confirmFinishTask() {\r\n      this.$refs.finishTaskForm.validate(valid => {\r\n        // 处理实验室编号字段\r\n        let laboratoryCode = this.finishTaskForm.laboratoryCode;\r\n\r\n        // 如果是数组类型，使用逗号拼接\r\n        if (Array.isArray(laboratoryCode)) {\r\n          laboratoryCode = laboratoryCode.join(\",\");\r\n        }\r\n        if (valid) {\r\n          this.finishTaskLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentFinishRow.id,\r\n            status: '2',\r\n            laboratoryCode: laboratoryCode,\r\n            remark: this.finishTaskForm.remark\r\n          }).then(response => {\r\n            this.finishTaskLoading = false;\r\n            this.finishTaskOpen = false;\r\n            this.msgSuccess('打样单已设置为已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.finishTaskLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 从批次管理对话框完成任务 */\r\n    handleFinishFromBatch() {\r\n      this.currentFinishRow = this.currentBatchRow;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      // 加载实验室编码列表\r\n      this.loadExperimentCodeList(this.currentBatchRow.id);\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 从批次管理对话框开始任务 */\r\n    handleStartFromBatch() {\r\n      const row = this.currentBatchRow;\r\n      this.$confirm('确定要将打样单：' + row.sampleOrderCode + '标记为进行中吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        updateSampleOrderStatus({\r\n          id: row.id,\r\n          status: '1'\r\n        }).then(response => {\r\n          this.msgSuccess('打样单已设置为已开始');\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n          // 更新当前批次行数据的状态\r\n          this.currentBatchRow.completionStatus = '1';\r\n          this.selectedOrderForBatch.completionStatus = 1;\r\n          if (this.selectedOrderForBatch) {\r\n            this.loadBatchData(this.selectedOrderForBatch.id);\r\n          }\r\n        });\r\n      });\r\n    },\r\n    /** 点击\"更新实验室编码\"按钮操作 */\r\n    handleUpdateLaboratoryCode(row) {\r\n      this.currentUpdateLaboratoryCodeRow = row;\r\n      // 回显当前的实验室编码和备注\r\n      this.updateLaboratoryCodeForm.laboratoryCode = row.laboratoryCode || '';\r\n      this.updateLaboratoryCodeForm.remark = row.remark || '';\r\n      this.updateLaboratoryCodeOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.updateLaboratoryCodeForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认更新实验室编码 */\r\n    confirmUpdateLaboratoryCode() {\r\n      this.$refs.updateLaboratoryCodeForm.validate(valid => {\r\n        if (valid) {\r\n          this.updateLaboratoryCodeLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentUpdateLaboratoryCodeRow.id,\r\n            status: this.currentUpdateLaboratoryCodeRow.completionStatus, // 保持当前状态\r\n            laboratoryCode: this.updateLaboratoryCodeForm.laboratoryCode,\r\n            remark: this.updateLaboratoryCodeForm.remark\r\n          }).then(response => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n            this.updateLaboratoryCodeOpen = false;\r\n            this.msgSuccess('实验室编码更新成功');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"驳回\"按钮操作 */\r\n    handleReject(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认驳回 */\r\n    confirmReject() {\r\n      this.$refs.rejectForm.validate(valid => {\r\n        if (valid) {\r\n          this.rejectLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentRejectRow.id,\r\n            status: '3',\r\n            rejectReason: this.rejectForm.rejectReason\r\n          }).then(response => {\r\n            this.rejectLoading = false;\r\n            this.rejectDialogOpen = false;\r\n            this.msgSuccess('打样单已驳回');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n            // 如果是未分配打样单的驳回，也需要刷新未分配列表\r\n            this.handleUnassignedOrders();\r\n          }).catch(() => {\r\n            this.rejectLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"逾期操作\"按钮操作 */\r\n    handleOverdueOperation(row) {\r\n      this.currentOverdueRow = row;\r\n      this.overdueOperationForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.overdueOperationForm.expectedSampleTime = row.expectedSampleTime;\r\n      this.overdueOperationForm.reasonForNoSample = row.reasonForNoSample;\r\n      this.overdueOperationForm.solution = row.solution;\r\n      this.overdueOperationOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.overdueOperationForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认逾期操作 */\r\n    confirmOverdueOperation() {\r\n      this.$refs.overdueOperationForm.validate(valid => {\r\n        if (valid) {\r\n          this.overdueOperationLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentOverdueRow.id,\r\n            status: \"11\", // 特殊处理，只更新“逾期情况”填写的字段\r\n            expectedSampleTime: this.overdueOperationForm.expectedSampleTime,\r\n            reasonForNoSample: this.overdueOperationForm.reasonForNoSample,\r\n            solution: this.overdueOperationForm.solution\r\n          }).then(response => {\r\n            this.overdueOperationLoading = false;\r\n            this.overdueOperationOpen = false;\r\n            this.msgSuccess('逾期操作已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.overdueOperationLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 查看打样单详情 */\r\n    async orderDetail(row) {\r\n      try {\r\n        //获取执行ID\r\n        const orderId = row.projectOrderId;\r\n        let data = await getExecutionByOrderInfo(orderId);\r\n        if(data!=null && !isNull(data.id)){\r\n            let id = data.id;\r\n            this.$refs.executionAddOrEdit.open = true;\r\n            await this.$nextTick()\r\n            this.$refs.executionAddOrEdit.reset()\r\n            this.$refs.executionAddOrEdit.show(id, 1)\r\n        }else{\r\n          this.$message.error('获取数据失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('查看项目详情失败:', error);\r\n        this.$message.error('查看项目详情失败: ' + (error.message || '未知错误'));\r\n      }\r\n    },\r\n\r\n    /** 锁定状态改变处理 */\r\n    handleLockChange(value) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.form.isLocked = 1;\r\n        }).catch(() => {\r\n          this.form.isLocked = 0;\r\n        });\r\n      }\r\n    },\r\n    /** 表格中锁定状态改变处理 */\r\n    handleTableLockChange(value, row) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          // 调用更新接口\r\n          updateEngineerSampleOrder({\r\n            id: row.id,\r\n            isLocked: 1\r\n          }).then(response => {\r\n            this.msgSuccess(\"锁定成功\");\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          });\r\n        }).catch(() => {\r\n          // 取消操作，恢复原值\r\n          row.isLocked = 0;\r\n        });\r\n      } else {\r\n        // 解锁操作\r\n        updateEngineerSampleOrder({\r\n          id: row.id,\r\n          isLocked: 0\r\n        }).then(response => {\r\n          this.msgSuccess(\"解锁成功\");\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      }\r\n    },\r\n    /** 加载统计概览数据 */\r\n    loadDashboardStats() {\r\n      let params = { ...this.queryParams };\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      getDashboardStats(params).then(response => {\r\n        this.dashboardStats = response.data || {};\r\n      });\r\n    },\r\n    /** 更改工程师按钮操作 */\r\n    handleChangeEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: row.nickName,\r\n        oldEngineerId: row.userId,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 提交更改工程师 */\r\n    submitChangeEngineer() {\r\n      this.$refs[\"changeEngineerForm\"].validate(valid => {\r\n        if (valid) {\r\n          const data = {\r\n            sampleOrderId: this.changeEngineerForm.id,\r\n            oldEngineerId: this.changeEngineerForm.oldEngineerId,\r\n            newEngineerId: this.changeEngineerForm.newEngineerId,\r\n            scheduledDate: this.changeEngineerForm.scheduledDate,\r\n            adjustWorkSchedule: this.changeEngineerForm.adjustWorkSchedule\r\n          };\r\n\r\n          // 调用更改工程师的API接口\r\n          changeEngineer(data).then(response => {\r\n            this.msgSuccess(\"更改工程师成功\");\r\n            this.changeEngineerOpen = false;\r\n            this.getList();\r\n            // 获取未分配打样单\r\n            this.handleUnassignedOrders();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.msgError(\"更改工程师失败\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 获取未分配打样单列表 */\r\n    handleUnassignedOrders() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = 0\r\n      params.pageNum = this.unassignedQueryParams.pageNum;\r\n      params.pageSize = this.unassignedQueryParams.pageSize;\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.unassignedOrders = response.rows;\r\n        this.unassignedTotal = response.total;\r\n      });\r\n    },\r\n    /** 分配工程师操作 */\r\n    handleAssignEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: '',\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 删除未分配打样单 */\r\n    handleDeleteUnassigned(row) {\r\n      this.$confirm('是否确认删除打样单编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function() {\r\n        return delEngineerSampleOrder(row.id);\r\n      }).then(() => {\r\n        this.handleUnassignedOrders();\r\n        this.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 驳回未分配打样单 */\r\n    handleRejectUnassigned(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 未分配打样单分页大小改变 */\r\n    handleUnassignedSizeChange(newSize) {\r\n      this.unassignedQueryParams.pageSize = newSize;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 未分配打样单页码改变 */\r\n    handleUnassignedCurrentChange(newPage) {\r\n      this.unassignedQueryParams.pageNum = newPage;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 切换未分配面板显示状态 */\r\n    toggleUnassignedPanel() {\r\n      this.isUnassignedPanelCollapsed = !this.isUnassignedPanelCollapsed;\r\n    },\r\n    /** 处理状态过滤 */\r\n    handleStatusFilter(status) {\r\n      // 清除逾期过滤\r\n      this.queryParams.isOverdue = null;\r\n      this.queryParams.completionStatus = status;\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 处理逾期任务过滤 */\r\n    handleOverdueFilter() {\r\n      // 清除完成状态过滤\r\n      this.queryParams.completionStatus = null;\r\n      // 设置逾期过滤\r\n      this.queryParams.isOverdue = this.queryParams.isOverdue === 1 ? null : 1;\r\n      this.handleQuery();\r\n\r\n      if (this.queryParams.isOverdue === 1) {\r\n        this.$message.info('已筛选显示逾期任务');\r\n      } else {\r\n        this.$message.info('已清除逾期任务筛选');\r\n      }\r\n    },\r\n    /** 获取紧急程度类型 */\r\n    getUrgencyType(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return 'danger'; // 已超过最晚开始时间\r\n      }\r\n\r\n      if (daysToEnd <= 1) {\r\n        return 'danger'; // 紧急：1天内截止\r\n      } else if (daysToEnd <= 3) {\r\n        return 'warning'; // 警告：3天内截止\r\n      } else if (daysToEnd <= 7) {\r\n        return 'primary'; // 一般：7天内截止\r\n      } else {\r\n        return 'success'; // 充足时间\r\n      }\r\n    },\r\n    /** 获取紧急程度文本 */\r\n    getUrgencyText(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return '超期未开始';\r\n      }\r\n\r\n      if (daysToEnd <= 0) {\r\n        return '已逾期';\r\n      } else if (daysToEnd <= 1) {\r\n        return '紧急';\r\n      } else if (daysToEnd <= 3) {\r\n        return '较急';\r\n      } else if (daysToEnd <= 7) {\r\n        return '一般';\r\n      } else {\r\n        return '充足';\r\n      }\r\n    },\r\n    /** 刷新未分配打样单列表 */\r\n    handleRefreshUnassigned() {\r\n      this.handleUnassignedOrders();\r\n      this.$message.success('刷新成功');\r\n    },\r\n    /** 获取逾期信息 */\r\n    getOverdueInfo(row) {\r\n      const now = new Date();\r\n      const endDate = new Date(row.endDate);\r\n      let isOverdue = false;\r\n      let overdueDays = 0;\r\n      // 根据后台SQL逻辑判断逾期\r\n      if (row.completionStatus === 2) {\r\n        // 已完成且逾期：实际完成时间 > 截止日期\r\n        if (row.actualFinishTime) {\r\n          const actualFinishTime = new Date(row.actualFinishTime);\r\n          if (actualFinishTime > endDate) {\r\n            isOverdue = true;\r\n            overdueDays = Math.ceil((actualFinishTime - endDate) / (1000 * 60 * 60 * 24));\r\n          }\r\n        }\r\n      } else if (row.completionStatus === 1) {\r\n        // 进行中且逾期：当前时间 > 截止日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      } else if (row.completionStatus === 0) {\r\n        // 未开始且逾期：未开始但当前时间 > 开始日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      }\r\n\r\n      return {\r\n        isOverdue,\r\n        overdueDays\r\n      };\r\n    },\r\n\r\n    // ==================== 批次管理相关方法 ====================\r\n\r\n    /** 打开批次管理对话框 */\r\n    handleBatchManagement(row) {\r\n      this.selectedOrderForBatch = row;\r\n      this.currentBatchRow = row; // 保存当前行数据\r\n      this.batchManagementOpen = true;\r\n      this.loadBatchData(row.id);\r\n    },\r\n\r\n    /** 加载批次数据 */\r\n    async loadBatchData(engineerSampleOrderId) {\r\n      try {\r\n        // 加载进行中的批次\r\n        const activeBatchesResponse = await this.getActiveBatchesData(engineerSampleOrderId);\r\n        this.activeBatches = activeBatchesResponse.data || [];\r\n\r\n        // 为每个进行中的批次加载实验记录\r\n        for (let batch of this.activeBatches) {\r\n          await this.loadBatchExperiments(batch);\r\n        }\r\n\r\n        // 加载所有批次\r\n        const batchesResponse = await getBatchesByOrderId(engineerSampleOrderId);\r\n        this.allBatches = batchesResponse.data || [];\r\n      } catch (error) {\r\n        console.error('加载批次数据失败:', error);\r\n        this.$message.error('加载批次数据失败');\r\n      }\r\n    },\r\n\r\n    /** 加载批次的实验记录 */\r\n    async loadBatchExperiments(batch) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batch.id);\r\n        this.$set(batch, 'experiments', response.data || []);\r\n      } catch (error) {\r\n        console.error('加载批次实验记录失败:', error);\r\n        this.$set(batch, 'experiments', []);\r\n      }\r\n    },\r\n\r\n    /** 获取进行中的批次 */\r\n    async getActiveBatchesData(engineerSampleOrderId) {\r\n      try {\r\n        // 调用新的API获取进行中的批次\r\n        const response = await getActiveBatches(engineerSampleOrderId);\r\n        return response;\r\n      } catch (error) {\r\n        console.error('获取进行中批次失败:', error);\r\n        return { data: [] };\r\n      }\r\n    },\r\n\r\n    /** 开始新批次 */\r\n    async startNewBatch() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许开始新批次，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$confirm('确认开始新的打样批次？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        const response = await startNewBatch(this.selectedOrderForBatch.id, '');\r\n        this.$message.success('新批次开始成功');\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('开始新批次失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 开始单个批次 */\r\n    async startSingleBatch(batch) {\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许开始批次');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$confirm(`确认开始第${batch.batchIndex}批次？`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        await startSingleBatch(batch.id);\r\n        this.$message.success('批次开始成功');\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n        this.getList();\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('开始批次失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 结束单个批次 */\r\n    finishSingleBatch(batch) {\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许结束批次');\r\n        return;\r\n      }\r\n\r\n      // 设置为单个批次结束模式\r\n      this.finishBatchMode = 'single';\r\n      this.currentFinishBatch = batch;\r\n\r\n      // 重置表单\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 显示对话框\r\n      this.finishBatchOpen = true;\r\n    },\r\n\r\n    /** 结束所有进行中批次 */\r\n    finishAllActiveBatches() {\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许结束批次');\r\n        return;\r\n      }\r\n\r\n      // 设置为结束所有批次模式\r\n      this.finishBatchMode = 'all';\r\n      this.currentFinishBatch = null;\r\n\r\n      // 重置表单\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 显示对话框\r\n      this.finishBatchOpen = true;\r\n    },\r\n\r\n    /** 提交结束批次 */\r\n    async submitFinishBatch() {\r\n      try {\r\n        if (this.finishBatchMode === 'single') {\r\n          // 结束单个批次\r\n          await finishSingleBatch(\r\n            this.currentFinishBatch.id,\r\n            this.finishBatchForm.qualityEvaluation || '',\r\n            this.finishBatchForm.remark || '',\r\n            this.currentFinishBatch.laboratoryCode || ''\r\n          );\r\n          this.$message.success('批次结束成功');\r\n        } else {\r\n          // 结束所有进行中的批次\r\n          await finishAllActiveBatches(this.selectedOrderForBatch.id);\r\n          this.$message.success('所有批次结束成功');\r\n        }\r\n\r\n        this.finishBatchOpen = false;\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        console.error('结束批次失败:', error);\r\n        this.$message.error('结束批次失败: ' + (error.message || error));\r\n      }\r\n    },\r\n\r\n    /** 查看批次详情 */\r\n    async viewBatchDetail(batch) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batch.id);\r\n        const experiments = response.data || [];\r\n\r\n        // 设置批次详情数据\r\n        this.batchDetailData = {\r\n          ...batch,\r\n          experiments: experiments\r\n        };\r\n\r\n        this.batchDetailOpen = true;\r\n      } catch (error) {\r\n        console.error('查看批次详情失败:', error);\r\n        this.$message.error('查看批次详情失败');\r\n      }\r\n    },\r\n\r\n    /** 计算持续时间 */\r\n    calculateDuration(startTime) {\r\n      if (!startTime) return '0小时';\r\n\r\n      const start = new Date(startTime);\r\n      const now = new Date();\r\n      const diffMs = now - start;\r\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\r\n      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\r\n\r\n      if (diffHours > 0) {\r\n        return `${diffHours}小时${diffMinutes}分钟`;\r\n      } else {\r\n        return `${diffMinutes}分钟`;\r\n      }\r\n    },\r\n\r\n    /** 编辑实验室编号 */\r\n    editLaboratoryCode(batch) {\r\n      this.$set(batch, 'editingLab', true);\r\n      this.$nextTick(() => {\r\n        // 聚焦到输入框\r\n        const input = this.$el.querySelector(`input[value=\"${batch.laboratoryCode || ''}\"]`);\r\n        if (input) input.focus();\r\n      });\r\n    },\r\n\r\n    /** 显示添加实验记录对话框 */\r\n    showAddExperimentDialog(batch) {\r\n      this.currentBatchForExperiment = batch;\r\n      this.addExperimentForm = {\r\n        experimentCode: '',\r\n        experimentNote: '',\r\n        remark: ''\r\n      };\r\n      this.addExperimentOpen = true;\r\n    },\r\n\r\n    /** 关闭添加实验记录对话框 */\r\n    closeAddExperiment() {\r\n      this.addExperimentOpen = false;\r\n      this.currentBatchForExperiment = null;\r\n      this.$refs.addExperimentForm && this.$refs.addExperimentForm.resetFields();\r\n    },\r\n\r\n    /** 提交添加实验记录 */\r\n    async submitAddExperiment() {\r\n      try {\r\n        await this.$refs.addExperimentForm.validate();\r\n\r\n        const experimentRecord = {\r\n          batchId: this.currentBatchForExperiment.id,\r\n          experimentCode: this.addExperimentForm.experimentCode,\r\n          experimentNote: this.addExperimentForm.experimentNote || '',\r\n          remark: this.addExperimentForm.remark\r\n        };\r\n\r\n        await addExperimentToBatch(experimentRecord);\r\n\r\n        this.$message.success('实验记录添加成功');\r\n        this.addExperimentOpen = false;\r\n\r\n        // 重新加载当前批次的实验记录\r\n        await this.loadBatchExperiments(this.currentBatchForExperiment);\r\n      } catch (error) {\r\n        this.$message.error('添加实验记录失败: ' + (error.message || error));\r\n      }\r\n    },\r\n\r\n    /** 删除实验记录 */\r\n    async removeExperiment(batch, experiment) {\r\n      try {\r\n        await this.$confirm('确认删除该实验记录吗？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        await removeExperimentFromBatch(experiment.id);\r\n        this.$message.success('实验记录删除成功');\r\n\r\n        // 重新加载当前批次的实验记录\r\n        await this.loadBatchExperiments(batch);\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除实验记录失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 获取批次状态文本 */\r\n    getBatchStatusText(status) {\r\n      const statusMap = {\r\n        0: '未开始',\r\n        1: '进行中',\r\n        2: '已完成',\r\n        3: '已取消'\r\n      };\r\n      return statusMap[status] || '未知';\r\n    },\r\n\r\n    /** 获取批次状态类型 */\r\n    getBatchStatusType(status) {\r\n      const typeMap = {\r\n        0: 'info',\r\n        1: 'success',\r\n        2: 'primary',\r\n        3: 'danger'\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    /** 获取质量评价类型 */\r\n    getQualityEvaluationType(evaluation) {\r\n      if (!evaluation) return '';\r\n\r\n      const lowerEval = evaluation.toLowerCase();\r\n      if (lowerEval.includes('优秀') || lowerEval.includes('良好') || lowerEval.includes('好')) {\r\n        return 'success';\r\n      } else if (lowerEval.includes('一般') || lowerEval.includes('中等')) {\r\n        return 'warning';\r\n      } else if (lowerEval.includes('差') || lowerEval.includes('不合格') || lowerEval.includes('失败')) {\r\n        return 'danger';\r\n      } else {\r\n        return 'info';\r\n      }\r\n    },\r\n\r\n    /** 关闭批次管理对话框并清空数据 */\r\n    closeBatchManagement() {\r\n      // 清空批次管理相关的数据\r\n      this.activeBatches = [];\r\n      this.allBatches = [];\r\n      this.selectedOrderForBatch = null;\r\n      this.currentBatchRow = null; // 清空当前行数据\r\n      this.batchDetailData = null;\r\n\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 关闭所有相关的子对话框\r\n      this.batchDetailOpen = false;\r\n      this.addExperimentOpen = false;\r\n      this.finishBatchOpen = false;\r\n    },\r\n\r\n    /** 关闭批次详情对话框并清空数据 */\r\n    closeBatchDetail() {\r\n      // 清空批次详情数据\r\n      this.batchDetailData = null;\r\n    },\r\n\r\n    /** 关闭结束批次对话框并清空数据 */\r\n    closeFinishBatch() {\r\n      // 清空结束批次表单数据\r\n      this.finishBatchOpen = false;\r\n      this.finishBatchMode = 'all';\r\n      this.currentFinishBatch = null;\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n      // 清除表单验证状态\r\n      if (this.$refs.finishBatchForm) {\r\n        this.$refs.finishBatchForm.clearValidate();\r\n      }\r\n    },\r\n\r\n    /** 刷新批次数据 */\r\n    async refreshBatchData() {\r\n      if (this.selectedOrderForBatch) {\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n        this.$message.success('批次数据已刷新');\r\n      }\r\n    },\r\n\r\n    /** 加载实验室编码列表 */\r\n    async loadExperimentCodeList(engineerSampleOrderId) {\r\n      try {\r\n        const response = await getBatchExperimentCodeList(engineerSampleOrderId);\r\n        this.experimentCodeList = response.data || [];\r\n      } catch (error) {\r\n        console.error('加载实验室编码列表失败:', error);\r\n        this.experimentCodeList = [];\r\n        this.$message.error('加载实验室编码列表失败');\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.stats-row {\r\n  margin-bottom: 20px;\r\n}\r\n.stats-card {\r\n  border: none;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* 批次详情样式 */\r\n.batch-detail-container {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.batch-info-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-value {\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.remark-content {\r\n  background-color: #F5F7FA;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409EFF;\r\n  color: #606266;\r\n  line-height: 1.5;\r\n  margin-top: 8px;\r\n  min-height: 40px;\r\n}\r\n\r\n.experiments-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.empty-state, .empty-experiments {\r\n  background-color: #FAFAFA;\r\n  border-radius: 8px;\r\n  margin: 10px 0;\r\n}\r\n\r\n/* 历史批次表格样式优化 */\r\n.history-batches-card .el-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-batches-card .el-table th {\r\n  background-color: #F5F7FA;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.history-batches-card .el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n.stats-card:not(.total) {\r\n  cursor: pointer;\r\n}\r\n\r\n.stats-card:not(.total):hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stats-card.active {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  position: relative;\r\n}\r\n\r\n.stats-card.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 3px;\r\n  background: currentColor;\r\n}\r\n\r\n.stats-card.completed.active::after {\r\n  background: #67C23A;\r\n}\r\n\r\n.stats-card.in-progress.active::after {\r\n  background: #409EFF;\r\n}\r\n\r\n.stats-card.overdue.active::after {\r\n  background: #F56C6C;\r\n}\r\n\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n\r\n.stats-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n.stats-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n}\r\n.stats-icon i {\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n.stats-info {\r\n  flex: 1;\r\n}\r\n.stats-title {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-bottom: 8px;\r\n}\r\n.stats-number {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n.stats-card.total .stats-icon {\r\n  background: linear-gradient(135deg, #3a7bd5, #3a6073);\r\n}\r\n.stats-card.completed .stats-icon {\r\n  background: linear-gradient(135deg, #E6A23C, #F0C78A);\r\n}\r\n.stats-card.in-progress .stats-icon {\r\n  background: linear-gradient(135deg, #409EFF, #66B1FF);\r\n}\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n@media (max-width: 768px) {\r\n  .stats-card .stats-content {\r\n    padding: 15px;\r\n  }\r\n  .stats-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n    margin-right: 15px;\r\n  }\r\n  .stats-icon i {\r\n    font-size: 20px;\r\n  }\r\n  .stats-number {\r\n    font-size: 24px;\r\n  }\r\n}\r\n.other-engineer-select {\r\n  margin-top: 10px;\r\n}\r\n.select-item {\r\n  width: 100%;\r\n}\r\n\r\n/* 未分配打样单告警样式 */\r\n.alert-card {\r\n  margin-bottom: 20px;\r\n}\r\n.alert-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.alert-title {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n.alert-title span {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #FF1414;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-cards {\r\n  margin-top: 20px;\r\n}\r\n.alert-item {\r\n  margin-bottom: 20px;\r\n  transition: all 0.3s;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n  height: 225px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n.alert-item:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);\r\n  border-color: #409EFF;\r\n}\r\n.alert-item-header {\r\n  margin-bottom: 12px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #f0f2f5;\r\n  background: rgba(64, 158, 255, 0.02);\r\n  margin: -16px -16px 12px -16px;\r\n  padding: 12px 16px;\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n.order-code-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.order-code {\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n.urgency-tag {\r\n  margin-left: 8px;\r\n}\r\n.alert-item-content {\r\n  padding: 0;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  min-height: 0;\r\n}\r\n.info-section {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n}\r\n.info-row {\r\n  margin-bottom: 8px;\r\n}\r\n.info-row-double {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 12px;\r\n}\r\n.info-row-double .info-item {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-row-double .info-item.date-time-item {\r\n  flex: 1.5;\r\n}\r\n.info-row-double .info-item.standard-item {\r\n  flex: 1;\r\n}\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n.info-label {\r\n  margin-left: 6px;\r\n  margin-right: 4px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n.info-value {\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n.difficulty-text {\r\n  cursor: pointer;\r\n  border-bottom: 1px dashed #ccc;\r\n}\r\n.info-reason {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  color: #f56c6c;\r\n  margin-top: 8px;\r\n  padding: 6px 8px;\r\n  background-color: #fef0f0;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #f56c6c;\r\n  font-size: 12px;\r\n}\r\n.reason-text {\r\n  margin-left: 4px;\r\n  line-height: 1.4;\r\n  word-break: break-word;\r\n}\r\n.text-overflow {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-reason span {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.info-item i,\r\n.info-date i,\r\n.info-reason i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n.alert-item-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: auto;\r\n  padding: 12px 0 0 0;\r\n  border-top: 1px solid #f0f2f5;\r\n  flex-shrink: 0;\r\n  background: rgba(250, 251, 252, 0.5);\r\n  border-radius: 0 0 6px 6px;\r\n  margin-left: -16px;\r\n  margin-right: -16px;\r\n  padding-left: 16px;\r\n  padding-right: 16px;\r\n}\r\n.alert-item-footer .el-button {\r\n  border-radius: 4px;\r\n  font-weight: 500;\r\n}\r\n.alert-item-footer .el-button--primary {\r\n  background: #409EFF;\r\n  border-color: #409EFF;\r\n  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);\r\n}\r\n.alert-item-footer .el-button--primary:hover {\r\n  background: #66b1ff;\r\n  border-color: #66b1ff;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);\r\n}\r\n.alert-item-footer .el-button--text {\r\n  color: #909399;\r\n  transition: all 0.3s;\r\n}\r\n.alert-item-footer .el-button--text:hover {\r\n  color: #f56c6c;\r\n  background: rgba(245, 108, 108, 0.1);\r\n}\r\n.delete-btn:hover {\r\n  color: #f56c6c !important;\r\n}\r\n\r\n/* 确保卡片内容区域的统一布局 */\r\n.alert-item .el-card__body {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 16px;\r\n  position: relative;\r\n}\r\n\r\n/* 确保内容区域占据剩余空间 */\r\n.alert-item .alert-item-header {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.alert-item .alert-item-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n}\r\n\r\n.alert-item .alert-item-footer {\r\n  flex-shrink: 0;\r\n  margin-top: auto;\r\n}\r\n\r\n.alert-item .info-group {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.alert-item .info-date {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .alert-item {\r\n    height: auto;\r\n    min-height: 200px;\r\n  }\r\n\r\n  .info-row-double {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .alert-item-footer {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .alert-item-footer .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 批次管理相关样式 */\r\n.batch-management-container {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.current-batch-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-info-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.batch-info-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.experiment-section {\r\n  border-top: 1px solid #ebeef5;\r\n  padding-top: 15px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.section-header span {\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.history-batches-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.new-batch-section {\r\n  padding: 40px 0;\r\n  text-align: center;\r\n  background-color: #fafafa;\r\n  border: 2px dashed #dcdfe6;\r\n  border-radius: 6px;\r\n}\r\n\r\n.new-batch-section .el-button {\r\n  padding: 12px 30px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .batch-management-container {\r\n    max-height: 500px;\r\n  }\r\n\r\n  .batch-info-item {\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .section-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .section-header .el-button {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n\r\n/* 备注文本省略样式 */\r\n.remark-text-ellipsis {\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  line-clamp: 2;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  line-height: 1.4;\r\n  max-height: 2.8em; /* 2行的高度，基于line-height */\r\n  word-break: break-word;\r\n  white-space: normal;\r\n  cursor: pointer;\r\n}\r\n\r\n.remark-text-ellipsis:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 导出选择对话框样式 */\r\n.export-options {\r\n  padding: 10px 0;\r\n}\r\n\r\n.export-options .el-checkbox {\r\n  width: 100%;\r\n  margin-right: 0;\r\n  padding: 15px;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.export-options .el-checkbox:hover {\r\n  border-color: #409eff;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.export-options .el-checkbox.is-checked {\r\n  border-color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.export-options .el-checkbox__label {\r\n  width: 100%;\r\n  padding-left: 10px;\r\n}\r\n\r\n/* 实验记录相关样式 */\r\n.experiment-codes {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 4px;\r\n  min-height: 32px;\r\n}\r\n\r\n.experiment-codes .el-tag {\r\n  margin: 2px;\r\n  max-width: 120px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.experiment-codes .el-button {\r\n  margin: 2px;\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAg9CA,IAAAA,oBAAA,GAAAC,OAAA;AAyBA,IAAAC,iBAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAC,sBAAA,CAAAH,OAAA;AACAA,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,mBAAA,GAAAH,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,sBAAA;IAAAC,kBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,YAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,uBAAA;MACA;MACAC,iBAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,aAAA;QACAC,SAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,MAAA;QACAC,OAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,WAAA;QACAC,WAAA;QACAC,SAAA;QAAA;QACAC,UAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAjB,MAAA,GACA;UAAAkB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,eAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjB,gBAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,aAAA;MACAC,kBAAA;MACAC,SAAA;MAAA;MACAC,kBAAA;MACAC,cAAA;MACAC,oBAAA;MACAC,qBAAA;MACA;MACAC,iBAAA;QACAC,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAC,KAAA,OAAAC,IAAA;YACAF,MAAA,CAAAG,KAAA,UAAAF,KAAA,EAAAA,KAAA;UACA;QACA;UACAH,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAI,SAAA,OAAAF,IAAA;YACAE,SAAA,CAAAC,OAAA,CAAAD,SAAA,CAAAE,OAAA;YACAN,MAAA,CAAAG,KAAA,UAAAC,SAAA,EAAAA,SAAA;UACA;QACA;UACAN,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAO,GAAA,OAAAL,IAAA;YACA,IAAAM,KAAA,OAAAN,IAAA;YACAM,KAAA,CAAAH,OAAA,CAAAG,KAAA,CAAAF,OAAA;YACAN,MAAA,CAAAG,KAAA,UAAAK,KAAA,EAAAD,GAAA;UACA;QACA;UACAT,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAO,GAAA,OAAAL,IAAA;YACA,IAAAM,KAAA,OAAAN,IAAA;YACAM,KAAA,CAAAH,OAAA,CAAAG,KAAA,CAAAF,OAAA;YACAN,MAAA,CAAAG,KAAA,UAAAK,KAAA,EAAAD,GAAA;UACA;QACA;MACA;MACA;MACAE,UAAA;MACAC,cAAA;QACA;QACA;QACA;QACA;MACA;MACA;MACAC,kBAAA;MACA;MACAC,kBAAA;QACAC,EAAA;QACA3C,eAAA;QACA4C,mBAAA;QACAC,aAAA;QACAC,aAAA;QACA5C,aAAA;QACA6C,kBAAA;MACA;MACA;MACAC,mBAAA;QACAF,aAAA,GACA;UAAA9B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,aAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACA+B,eAAA;MACA;MACAC,mBAAA;MACAC,aAAA;MAAA;MACAC,UAAA;MAAA;MACAC,qBAAA;MACAC,eAAA;MAAA;MACA;MACAC,eAAA;MACAC,eAAA;MACA;MACAC,eAAA;MACAC,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;MACAC,kBAAA;MAAA;MACAC,eAAA;MAAA;MACA;MACAC,iBAAA;MACAC,iBAAA;QACAC,cAAA;QACAC,cAAA;QACAN,MAAA;MACA;MACAO,kBAAA;QACAF,cAAA,GACA;UAAAjD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAkD,yBAAA;MACA;MACAC,uBAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAnC,OAAA,KAAAJ,IAAA,CAAAwC,GAAA;QACA;MACA;MACAC,kBAAA;MACAC,iBAAA;MACA;MACAC,gBAAA;MACAC,qBAAA;QACAhF,OAAA;QACAC,QAAA;MACA;MACAgF,eAAA;MACA;MACAC,0BAAA;MACA;MACAC,cAAA;MACAC,iBAAA;MACAC,cAAA;QACAC,cAAA;QACAtB,MAAA;MACA;MACAuB,kBAAA;MAAA;MACAC,eAAA;QACAF,cAAA,GACA;UAAAlE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAmE,gBAAA;MACA;MACAC,wBAAA;MACAC,2BAAA;MACAC,wBAAA;QACAN,cAAA;QACAtB,MAAA;MACA;MACA6B,yBAAA;QACAP,cAAA,GACA;UAAAlE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAwE,8BAAA;MACA;MACAC,gBAAA;MACAC,aAAA;MACAC,aAAA;MACAC,QAAA;MACA;MACAC,kBAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,SAAA;MACA;MACAC,gBAAA;MACAC,aAAA;MACAC,UAAA;QACArG,eAAA;QACAsG,YAAA;MACA;MACA;MACAC,oBAAA;MACAC,uBAAA;MACAC,oBAAA;QACAzG,eAAA;QACA0G,kBAAA;QACAC,iBAAA;QACAC,QAAA;MACA;MACAC,iBAAA;MACAC,WAAA;QACAR,YAAA,GACA;UAAAtF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA6F,gBAAA;MACA;MACAC,kBAAA;MACAC,qBAAA;MACAC,kBAAA;QACAC,KAAA;QACAC,YAAA;QACAC,IAAA;QACAzD,MAAA;MACA;MACA0D,mBAAA;QACAH,KAAA,GACA;UAAAnG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAkG,YAAA,GACA;UAAApG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAmG,IAAA,GACA;UAAArG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAqG,YAAA;IACA;EACA;EACAC,QAAA;IACA,0CACAC,YAAA,WAAAA,aAAA;MACA,YAAApE,qBAAA,SAAAA,qBAAA,CAAApD,gBAAA;IACA;EACA;EACAyH,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,IAAA5F,KAAA,OAAAC,IAAA;IACA,IAAA4F,QAAA,GAAA7F,KAAA,CAAA8F,WAAA,WAAAC,MAAA,CAAA/F,KAAA,CAAAgG,QAAA,QAAAC,QAAA,iBAAAF,MAAA,CAAA/F,KAAA,CAAAkG,OAAA,IAAAD,QAAA;IACA,KAAA3G,SAAA,IAAAuG,QAAA,EAAAA,QAAA;IAEA,KAAAM,OAAA;IACA,IAAAC,yBAAA,IAAAC,IAAA,WAAAC,GAAA;MAAA,OAAAV,KAAA,CAAA1B,eAAA,GAAAoC,GAAA;IAAA;IAEA,KAAAC,QAAA,cAAAF,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAAxG,aAAA,GAAAoH,QAAA,CAAAzJ,IAAA;IACA;IACA,KAAAwJ,QAAA,0BAAAF,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAAvG,kBAAA,GAAAmH,QAAA,CAAAzJ,IAAA;IACA;IACA,KAAAwJ,QAAA,qBAAAF,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAAnI,WAAA,GAAA+I,QAAA,CAAAzJ,IAAA;IACA;IACA,KAAAwJ,QAAA,iBAAAF,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAAJ,YAAA,GAAAgB,QAAA,CAAAzJ,IAAA;IACA;IACA,KAAA0J,kBAAA;IACA;IACA,IAAAC,2CAAA,IAAAL,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAApI,iBAAA,GAAAoI,KAAA,CAAAe,UAAA,CAAAH,QAAA,CAAAzJ,IAAA;IACA;IACA;IACA,KAAA6J,sBAAA;EACA;EACAC,OAAA;IACA,gBACAC,eAAA,WAAAA,gBAAAC,GAAA;MACA,IAAAC,aAAA,GAAAD,GAAA,CAAAC,aAAA;MACA,IAAAC,YAAA,GAAAF,GAAA,CAAAE,YAAA;;MAEA;MACA,IAAAA,YAAA,WAAAA,YAAA;QACA,OAAAD,aAAA;MACA;MAEA,OAAAA,aAAA,GAAAC,YAAA;IACA;IACA,mBACAd,OAAA,WAAAA,QAAA;MAAA,IAAAe,MAAA;MACA,IAAAC,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAzJ,WAAA;MACAuJ,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA5H,kBAAA;MACA4H,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA3H,cAAA;MACA2H,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA1H,oBAAA;MACA0H,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAzH,qBAAA;MACA;MACA,OAAAyH,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAAlI,SAAA,SAAAA,SAAA,CAAAmI,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAjI,SAAA;QACA6H,MAAA,CAAAK,YAAA,QAAAlI,SAAA;MACA;MACA,KAAAtC,OAAA;MACAmK,MAAA,CAAA1I,iBAAA;MACA,IAAAiJ,4CAAA,EAAAP,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAU,MAAA,CAAA3J,uBAAA,GAAAiJ,QAAA,CAAAmB,IAAA;QACAT,MAAA,CAAA5J,KAAA,GAAAkJ,QAAA,CAAAlJ,KAAA;QACA4J,MAAA,CAAAlK,OAAA;MACA;IACA;IACA;IACA4K,MAAA,WAAAA,OAAA;MACA,KAAAjK,IAAA;MACA,KAAAkK,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA9I,IAAA;QACA6B,EAAA;QACA7C,MAAA;QACAE,eAAA;QACA6J,WAAA;QACAC,iBAAA;QACA7J,gBAAA;QACAC,aAAA;QACAE,eAAA;QACAC,gBAAA;QACA0J,cAAA;QACAC,iBAAA;QACAC,gBAAA;QACAtG,iBAAA;QACAuG,QAAA;QACA/J,SAAA;QACAgK,OAAA;QACAC,SAAA;QACAC,iBAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACA7G,MAAA;MACA;MACA,KAAA8G,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAhL,WAAA,CAAAC,OAAA;MACA,KAAAgF,qBAAA,CAAAhF,OAAA;MACA,KAAAsI,OAAA;MACA,KAAAM,kBAAA;MACA,KAAAG,sBAAA;IACA;IACA,aACAiC,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAArJ,SAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,cAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,qBAAA;MACA,KAAAkJ,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA9L,GAAA,GAAA8L,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAArI,EAAA;MAAA;MACA,KAAA1D,YAAA,GAAA6L,SAAA;MACA,KAAA5L,MAAA,GAAA4L,SAAA,CAAAtB,MAAA;MACA,KAAArK,QAAA,IAAA2L,SAAA,CAAAtB,MAAA;IACA;IACA,aACAyB,SAAA,WAAAA,UAAA;MACA,KAAArB,KAAA;MACA,KAAAlK,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAyL,YAAA,WAAAA,aAAApC,GAAA;MAAA,IAAAqC,MAAA;MACA,KAAAvB,KAAA;MACA,IAAAjH,EAAA,GAAAmG,GAAA,CAAAnG,EAAA,SAAA3D,GAAA;MACA,IAAAoM,2CAAA,EAAAzI,EAAA,EAAAyF,IAAA,WAAAG,QAAA;QACA4C,MAAA,CAAArK,IAAA,GAAAyH,QAAA,CAAAzJ,IAAA;QACAqM,MAAA,CAAAzL,IAAA;QACAyL,MAAA,CAAA1L,KAAA;QAAA4L,2CAAA;MACA;IACA;IACA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAzK,IAAA,CAAA6B,EAAA;YACA,IAAAgJ,8CAAA,EAAAJ,MAAA,CAAAzK,IAAA,EAAAsH,IAAA,WAAAG,QAAA;cACAgD,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAA7L,IAAA;cACA6L,MAAA,CAAArD,OAAA;cACAqD,MAAA,CAAA/C,kBAAA;YACA;UACA;YACA,IAAA6C,2CAAA,EAAAE,MAAA,CAAAzK,IAAA,EAAAsH,IAAA,WAAAG,QAAA;cACAgD,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAA7L,IAAA;cACA6L,MAAA,CAAArD,OAAA;cACAqD,MAAA,CAAA/C,kBAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAqD,YAAA,WAAAA,aAAA/C,GAAA;MAAA,IAAAgD,MAAA;MACA,IAAA9M,GAAA,GAAA8J,GAAA,CAAAnG,EAAA,SAAA3D,GAAA;MACA,KAAA+M,QAAA,wBAAAjD,GAAA,CAAA9I,eAAA,aAAAoI,IAAA;QACA,WAAA4D,2CAAA,EAAAhN,GAAA;MACA,GAAAoJ,IAAA;QACA0D,MAAA,CAAA5D,OAAA;QACA4D,MAAA,CAAAF,UAAA;QACAE,MAAA,CAAAtD,kBAAA;MACA,GAAAyD,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA;MACA,KAAAtG,aAAA;MACA,KAAAD,gBAAA;IACA;IACA,aACAwG,aAAA,WAAAA,cAAA;MACA,SAAAvG,aAAA,CAAA4D,MAAA;QACA,KAAA4C,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAxG,aAAA;MACA,KAAAyG,cAAA;IACA;IAEA,eACAA,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,KAAA,SAAA3G,aAAA,CAAA4D,MAAA;QACA;QACA,KAAA3D,aAAA;QACA,KAAAF,gBAAA;QACA,KAAAyG,QAAA,CAAAK,OAAA,oDAAAC,MAAA,MAAA9G,aAAA,CAAA4D,MAAA;QACA;MACA;MAEA,IAAAmD,MAAA,QAAA/G,aAAA,CAAA2G,KAAA;MACA,IAAA/L,iBAAA,GAAAmM,MAAA;MACA,IAAAC,QAAA,GAAAD,MAAA;MAEA,KAAAE,QAAA,CAAArM,iBAAA,EAAAmM,MAAA,EAAAvE,IAAA;QACAoE,MAAA,CAAAJ,QAAA,CAAAK,OAAA,IAAAC,MAAA,CAAAE,QAAA;QACA;QACAJ,MAAA,CAAAF,cAAA,CAAAC,KAAA;MACA,GAAAN,KAAA,WAAAa,KAAA;QACAN,MAAA,CAAA3G,aAAA;QACA2G,MAAA,CAAAJ,QAAA,CAAAU,KAAA,IAAAJ,MAAA,CAAAE,QAAA,kDAAAF,MAAA,CAAAI,KAAA,CAAA7L,OAAA,IAAA6L,KAAA;MACA;IACA;IACA,aACAD,QAAA,WAAAA,SAAArM,iBAAA,EAAAuM,UAAA;MAAA,IAAAC,MAAA;MACA,IAAA9D,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAzJ,WAAA;MACAuJ,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA5H,kBAAA;MACA4H,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA3H,cAAA;MACA2H,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA1H,oBAAA;MACA0H,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAzH,qBAAA;MACA;MACA,OAAAyH,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAAlI,SAAA,SAAAA,SAAA,CAAAmI,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAjI,SAAA;QACA6H,MAAA,CAAAK,YAAA,QAAAlI,SAAA;MACA;MACA6H,MAAA,CAAA1I,iBAAA,GAAAA,iBAAA;MACA0I,MAAA,CAAA6D,UAAA,GAAAA,UAAA;MACA,WAAAE,8CAAA,EAAA/D,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAyE,MAAA,CAAAE,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;MACA;IACA;IACA,uBACAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,IAAAnE,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAzJ,WAAA;MACAuJ,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA5H,kBAAA;MACA4H,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA3H,cAAA;MACA2H,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA1H,oBAAA;MACA0H,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAzH,qBAAA;MACA;MACA,OAAAyH,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAAlI,SAAA,SAAAA,SAAA,CAAAmI,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAjI,SAAA;QACA6H,MAAA,CAAAK,YAAA,QAAAlI,SAAA;MACA;MACA,KAAA0K,QAAA;QACAuB,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACAiF,MAAA,CAAAxH,aAAA;QACA,WAAAoH,8CAAA,EAAA/D,MAAA;MACA,GAAAd,IAAA,WAAAG,QAAA;QACA8E,MAAA,CAAAH,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;QACAE,MAAA,CAAAxH,aAAA;QACAwH,MAAA,CAAAjB,QAAA,CAAAK,OAAA;MACA,GAAAR,KAAA;QACAoB,MAAA,CAAAxH,aAAA;MACA;IACA;IACA,iBACA4H,WAAA,WAAAA,YAAA3E,GAAA;MAAA,IAAA4E,MAAA;MACA,KAAA3B,QAAA,cAAAjD,GAAA,CAAA9I,eAAA;QACAsN,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA,IAAAuF,4CAAA;UACAhL,EAAA,EAAAmG,GAAA,CAAAnG,EAAA;UACAiL,MAAA;QACA,GAAAxF,IAAA,WAAAG,QAAA;UACAmF,MAAA,CAAA9B,UAAA;UACA8B,MAAA,CAAAxF,OAAA;UACAwF,MAAA,CAAAlF,kBAAA;QACA;MACA;IACA;IACA,cACAqF,yBAAA,WAAAA,0BAAA/E,GAAA;MAAA,IAAAgF,OAAA;MACA,KAAA/B,QAAA;QACAuB,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA0F,OAAA,CAAAjI,aAAA;QACA,WAAAkI,+BAAA;UAAAC,MAAA,EAAAlF,GAAA,CAAAkF,MAAA;UAAAC,SAAA,EAAAnF,GAAA,CAAAmF;QAAA;MACA,GAAA7F,IAAA,WAAAG,QAAA;QACAuF,OAAA,CAAAZ,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;QACAW,OAAA,CAAAjI,aAAA;MACA,GAAAoG,KAAA;IACA;IACA,kBACAiC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,SAAAlP,YAAA,CAAAuK,MAAA;QACA,KAAA4E,MAAA,CAAAC,QAAA;QACA;MACA;;MAEA;MACA,IAAAC,UAAA,QAAArP,YAAA,CAAA8L,GAAA,WAAAjC,GAAA;QAAA;UACAkF,MAAA,EAAAlF,GAAA,CAAAkF,MAAA;UACAC,SAAA,EAAAnF,GAAA,CAAAmF;QACA;MAAA;MAEA,KAAAlC,QAAA;QACAuB,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA+F,OAAA,CAAAtI,aAAA;QACA,WAAA0I,mCAAA,EAAAD,UAAA;MACA,GAAAlG,IAAA,WAAAG,QAAA;QACA4F,OAAA,CAAAjB,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;QACAgB,OAAA,CAAAtI,aAAA;QACAsI,OAAA,CAAA/B,QAAA,CAAAK,OAAA;MACA,GAAAR,KAAA;QACAkC,OAAA,CAAAtI,aAAA;MACA;IACA;IACA,iBACA2I,YAAA,WAAAA,aAAA1F,GAAA;MAAA,IAAA2F,OAAA;MACA,KAAApJ,gBAAA,GAAAyD,GAAA;MACA,KAAA7D,cAAA,CAAAC,cAAA;MACA,KAAAD,cAAA,CAAArB,MAAA;MACA,KAAAmB,cAAA;MACA;MACA,KAAA2J,sBAAA,CAAA5F,GAAA,CAAAnG,EAAA;MACA,KAAAgM,SAAA;QACAF,OAAA,CAAAjD,KAAA,CAAAvG,cAAA,CAAA2J,aAAA;MACA;IACA;IACA,aACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MACA,KAAAtD,KAAA,CAAAvG,cAAA,CAAAwG,QAAA,WAAAC,KAAA;QACA;QACA,IAAAxG,cAAA,GAAA4J,OAAA,CAAA7J,cAAA,CAAAC,cAAA;;QAEA;QACA,IAAA6J,KAAA,CAAAC,OAAA,CAAA9J,cAAA;UACAA,cAAA,GAAAA,cAAA,CAAA+J,IAAA;QACA;QACA,IAAAvD,KAAA;UACAoD,OAAA,CAAA9J,iBAAA;UACA,IAAA2I,4CAAA;YACAhL,EAAA,EAAAmM,OAAA,CAAAzJ,gBAAA,CAAA1C,EAAA;YACAiL,MAAA;YACA1I,cAAA,EAAAA,cAAA;YACAtB,MAAA,EAAAkL,OAAA,CAAA7J,cAAA,CAAArB;UACA,GAAAwE,IAAA,WAAAG,QAAA;YACAuG,OAAA,CAAA9J,iBAAA;YACA8J,OAAA,CAAA/J,cAAA;YACA+J,OAAA,CAAAlD,UAAA;YACAkD,OAAA,CAAA5G,OAAA;YACA4G,OAAA,CAAAtG,kBAAA;UACA,GAAAyD,KAAA;YACA6C,OAAA,CAAA9J,iBAAA;UACA;QACA;MACA;IACA;IACA,mBACAkK,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MACA,KAAA9J,gBAAA,QAAA/B,eAAA;MACA,KAAA2B,cAAA,CAAAC,cAAA;MACA,KAAAD,cAAA,CAAArB,MAAA;MACA,KAAAmB,cAAA;MACA;MACA,KAAA2J,sBAAA,MAAApL,eAAA,CAAAX,EAAA;MACA,KAAAgM,SAAA;QACAQ,OAAA,CAAA3D,KAAA,CAAAvG,cAAA,CAAA2J,aAAA;MACA;IACA;IACA,mBACAQ,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,IAAAvG,GAAA,QAAAxF,eAAA;MACA,KAAAyI,QAAA,cAAAjD,GAAA,CAAA9I,eAAA;QACAsN,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA,IAAAuF,4CAAA;UACAhL,EAAA,EAAAmG,GAAA,CAAAnG,EAAA;UACAiL,MAAA;QACA,GAAAxF,IAAA,WAAAG,QAAA;UACA8G,OAAA,CAAAzD,UAAA;UACAyD,OAAA,CAAAnH,OAAA;UACAmH,OAAA,CAAA7G,kBAAA;UACA;UACA6G,OAAA,CAAA/L,eAAA,CAAArD,gBAAA;UACAoP,OAAA,CAAAhM,qBAAA,CAAApD,gBAAA;UACA,IAAAoP,OAAA,CAAAhM,qBAAA;YACAgM,OAAA,CAAAC,aAAA,CAAAD,OAAA,CAAAhM,qBAAA,CAAAV,EAAA;UACA;QACA;MACA;IACA;IACA,sBACA4M,0BAAA,WAAAA,2BAAAzG,GAAA;MAAA,IAAA0G,OAAA;MACA,KAAA9J,8BAAA,GAAAoD,GAAA;MACA;MACA,KAAAtD,wBAAA,CAAAN,cAAA,GAAA4D,GAAA,CAAA5D,cAAA;MACA,KAAAM,wBAAA,CAAA5B,MAAA,GAAAkF,GAAA,CAAAlF,MAAA;MACA,KAAA0B,wBAAA;MACA,KAAAqJ,SAAA;QACAa,OAAA,CAAAhE,KAAA,CAAAhG,wBAAA,CAAAoJ,aAAA;MACA;IACA;IACA,gBACAa,2BAAA,WAAAA,4BAAA;MAAA,IAAAC,OAAA;MACA,KAAAlE,KAAA,CAAAhG,wBAAA,CAAAiG,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAgE,OAAA,CAAAnK,2BAAA;UACA,IAAAoI,4CAAA;YACAhL,EAAA,EAAA+M,OAAA,CAAAhK,8BAAA,CAAA/C,EAAA;YACAiL,MAAA,EAAA8B,OAAA,CAAAhK,8BAAA,CAAAzF,gBAAA;YAAA;YACAiF,cAAA,EAAAwK,OAAA,CAAAlK,wBAAA,CAAAN,cAAA;YACAtB,MAAA,EAAA8L,OAAA,CAAAlK,wBAAA,CAAA5B;UACA,GAAAwE,IAAA,WAAAG,QAAA;YACAmH,OAAA,CAAAnK,2BAAA;YACAmK,OAAA,CAAApK,wBAAA;YACAoK,OAAA,CAAA9D,UAAA;YACA8D,OAAA,CAAAxH,OAAA;YACAwH,OAAA,CAAAlH,kBAAA;UACA,GAAAyD,KAAA;YACAyD,OAAA,CAAAnK,2BAAA;UACA;QACA;MACA;IACA;IACA,iBACAoK,YAAA,WAAAA,aAAA7G,GAAA;MAAA,IAAA8G,OAAA;MACA,KAAA7I,gBAAA,GAAA+B,GAAA;MACA,KAAAzC,UAAA,CAAArG,eAAA,GAAA8I,GAAA,CAAA9I,eAAA;MACA,KAAAqG,UAAA,CAAAC,YAAA;MACA,KAAAH,gBAAA;MACA,KAAAwI,SAAA;QACAiB,OAAA,CAAApE,KAAA,CAAAnF,UAAA,CAAAuI,aAAA;MACA;IACA;IACA,WACAiB,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,KAAAtE,KAAA,CAAAnF,UAAA,CAAAoF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAoE,OAAA,CAAA1J,aAAA;UACA,IAAAuH,4CAAA;YACAhL,EAAA,EAAAmN,OAAA,CAAA/I,gBAAA,CAAApE,EAAA;YACAiL,MAAA;YACAtH,YAAA,EAAAwJ,OAAA,CAAAzJ,UAAA,CAAAC;UACA,GAAA8B,IAAA,WAAAG,QAAA;YACAuH,OAAA,CAAA1J,aAAA;YACA0J,OAAA,CAAA3J,gBAAA;YACA2J,OAAA,CAAAlE,UAAA;YACAkE,OAAA,CAAA5H,OAAA;YACA4H,OAAA,CAAAtH,kBAAA;YACA;YACAsH,OAAA,CAAAnH,sBAAA;UACA,GAAAsD,KAAA;YACA6D,OAAA,CAAA1J,aAAA;UACA;QACA;MACA;IACA;IACA,mBACA2J,sBAAA,WAAAA,uBAAAjH,GAAA;MAAA,IAAAkH,OAAA;MACA,KAAAnJ,iBAAA,GAAAiC,GAAA;MACA,KAAArC,oBAAA,CAAAzG,eAAA,GAAA8I,GAAA,CAAA9I,eAAA;MACA,KAAAyG,oBAAA,CAAAC,kBAAA,GAAAoC,GAAA,CAAApC,kBAAA;MACA,KAAAD,oBAAA,CAAAE,iBAAA,GAAAmC,GAAA,CAAAnC,iBAAA;MACA,KAAAF,oBAAA,CAAAG,QAAA,GAAAkC,GAAA,CAAAlC,QAAA;MACA,KAAAL,oBAAA;MACA,KAAAoI,SAAA;QACAqB,OAAA,CAAAxE,KAAA,CAAA/E,oBAAA,CAAAmI,aAAA;MACA;IACA;IACA,aACAqB,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,OAAA;MACA,KAAA1E,KAAA,CAAA/E,oBAAA,CAAAgF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAwE,OAAA,CAAA1J,uBAAA;UACA,IAAAmH,4CAAA;YACAhL,EAAA,EAAAuN,OAAA,CAAArJ,iBAAA,CAAAlE,EAAA;YACAiL,MAAA;YAAA;YACAlH,kBAAA,EAAAwJ,OAAA,CAAAzJ,oBAAA,CAAAC,kBAAA;YACAC,iBAAA,EAAAuJ,OAAA,CAAAzJ,oBAAA,CAAAE,iBAAA;YACAC,QAAA,EAAAsJ,OAAA,CAAAzJ,oBAAA,CAAAG;UACA,GAAAwB,IAAA,WAAAG,QAAA;YACA2H,OAAA,CAAA1J,uBAAA;YACA0J,OAAA,CAAA3J,oBAAA;YACA2J,OAAA,CAAAtE,UAAA;YACAsE,OAAA,CAAAhI,OAAA;YACAgI,OAAA,CAAA1H,kBAAA;UACA,GAAAyD,KAAA;YACAiE,OAAA,CAAA1J,uBAAA;UACA;QACA;MACA;IACA;IACA,cACA2J,WAAA,WAAAA,YAAArH,GAAA;MAAA,IAAAsH,OAAA;MAAA,WAAAC,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAC,QAAA;QAAA,IAAAC,OAAA,EAAA3R,IAAA,EAAA6D,EAAA;QAAA,WAAA2N,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEA;cACAJ,OAAA,GAAA3H,GAAA,CAAAiI,cAAA;cAAAH,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAE,4CAAA,EAAAP,OAAA;YAAA;cAAA3R,IAAA,GAAA8R,QAAA,CAAAK,IAAA;cAAA,MACAnS,IAAA,iBAAAoS,gBAAA,EAAApS,IAAA,CAAA6D,EAAA;gBAAAiO,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAnO,EAAA,GAAA7D,IAAA,CAAA6D,EAAA;cACAyN,OAAA,CAAA5E,KAAA,CAAA3M,kBAAA,CAAAa,IAAA;cAAAkR,QAAA,CAAAE,IAAA;cAAA,OACAV,OAAA,CAAAzB,SAAA;YAAA;cACAyB,OAAA,CAAA5E,KAAA,CAAA3M,kBAAA,CAAA+K,KAAA;cACAwG,OAAA,CAAA5E,KAAA,CAAA3M,kBAAA,CAAAsS,IAAA,CAAAxO,EAAA;cAAAiO,QAAA,CAAAE,IAAA;cAAA;YAAA;cAEAV,OAAA,CAAAhE,QAAA,CAAAU,KAAA;YAAA;cAAA8D,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAGAS,OAAA,CAAAvE,KAAA,cAAA8D,QAAA,CAAAQ,EAAA;cACAhB,OAAA,CAAAhE,QAAA,CAAAU,KAAA,iBAAA8D,QAAA,CAAAQ,EAAA,CAAAnQ,OAAA;YAAA;YAAA;cAAA,OAAA2P,QAAA,CAAAU,IAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IAEA;IAEA,eACAe,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,OAAA;MACA,IAAAD,KAAA;QACA,KAAAzF,QAAA;UACAuB,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAApF,IAAA;UACAqJ,OAAA,CAAA3Q,IAAA,CAAAoJ,QAAA;QACA,GAAA+B,KAAA;UACAwF,OAAA,CAAA3Q,IAAA,CAAAoJ,QAAA;QACA;MACA;IACA;IACA,kBACAwH,qBAAA,WAAAA,sBAAAF,KAAA,EAAA1I,GAAA;MAAA,IAAA6I,OAAA;MACA,IAAAH,KAAA;QACA,KAAAzF,QAAA;UACAuB,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAApF,IAAA;UACA;UACA,IAAAuD,8CAAA;YACAhJ,EAAA,EAAAmG,GAAA,CAAAnG,EAAA;YACAuH,QAAA;UACA,GAAA9B,IAAA,WAAAG,QAAA;YACAoJ,OAAA,CAAA/F,UAAA;YACA+F,OAAA,CAAAzJ,OAAA;YACAyJ,OAAA,CAAAnJ,kBAAA;UACA;QACA,GAAAyD,KAAA;UACA;UACAnD,GAAA,CAAAoB,QAAA;QACA;MACA;QACA;QACA,IAAAyB,8CAAA;UACAhJ,EAAA,EAAAmG,GAAA,CAAAnG,EAAA;UACAuH,QAAA;QACA,GAAA9B,IAAA,WAAAG,QAAA;UACAoJ,OAAA,CAAA/F,UAAA;UACA+F,OAAA,CAAAzJ,OAAA;UACAyJ,OAAA,CAAAnJ,kBAAA;QACA;MACA;IACA;IACA,eACAA,kBAAA,WAAAA,mBAAA;MAAA,IAAAoJ,OAAA;MACA,IAAA1I,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAzJ,WAAA;MACA;MACA,SAAA0B,SAAA,SAAAA,SAAA,CAAAmI,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAjI,SAAA;QACA6H,MAAA,CAAAK,YAAA,QAAAlI,SAAA;MACA;MACA,IAAAwQ,sCAAA,EAAA3I,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAqJ,OAAA,CAAApP,cAAA,GAAA+F,QAAA,CAAAzJ,IAAA;MACA;IACA;IACA,gBACAgT,oBAAA,WAAAA,qBAAAhJ,GAAA;MAAA,IAAAiJ,OAAA;MACA,KAAArP,kBAAA;QACAC,EAAA,EAAAmG,GAAA,CAAAnG,EAAA;QACA3C,eAAA,EAAA8I,GAAA,CAAA9I,eAAA;QACA4C,mBAAA,EAAAkG,GAAA,CAAA/I,QAAA;QACA8C,aAAA,EAAAiG,GAAA,CAAAhJ,MAAA;QACAgD,aAAA;QACA5C,aAAA;QACA6C,kBAAA;MACA;;MAEA;MACA,IAAAiP,kDAAA;QACAlI,iBAAA,EAAAhB,GAAA,CAAAgB,iBAAA;QACAmI,UAAA,EAAAnJ,GAAA,CAAAmJ;MACA,GAAA7J,IAAA,WAAAG,QAAA;QACAwJ,OAAA,CAAA9O,eAAA,GAAAsF,QAAA,CAAAzJ,IAAA;MACA;MACA;MACA,IAAAoT,+CAAA,IAAA9J,IAAA,WAAAG,QAAA;QACAwJ,OAAA,CAAArN,iBAAA,GAAA6D,QAAA,CAAAzJ,IAAA;MACA;MACA,KAAA2D,kBAAA;IACA;IACA,cACA0P,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,KAAA5G,KAAA,uBAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAA5M,IAAA;YACAuT,aAAA,EAAAD,OAAA,CAAA1P,kBAAA,CAAAC,EAAA;YACAE,aAAA,EAAAuP,OAAA,CAAA1P,kBAAA,CAAAG,aAAA;YACAC,aAAA,EAAAsP,OAAA,CAAA1P,kBAAA,CAAAI,aAAA;YACA5C,aAAA,EAAAkS,OAAA,CAAA1P,kBAAA,CAAAxC,aAAA;YACA6C,kBAAA,EAAAqP,OAAA,CAAA1P,kBAAA,CAAAK;UACA;;UAEA;UACA,IAAAuP,mCAAA,EAAAxT,IAAA,EAAAsJ,IAAA,WAAAG,QAAA;YACA6J,OAAA,CAAAxG,UAAA;YACAwG,OAAA,CAAA3P,kBAAA;YACA2P,OAAA,CAAAlK,OAAA;YACA;YACAkK,OAAA,CAAAzJ,sBAAA;YACAyJ,OAAA,CAAA5J,kBAAA;UACA,GAAAyD,KAAA;YACAmG,OAAA,CAAA/D,QAAA;UACA;QACA;MACA;IACA;IACA,iBACA1F,sBAAA,WAAAA,uBAAA;MAAA,IAAA4J,OAAA;MACA,IAAArJ,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAzJ,WAAA;MACAuJ,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA5H,kBAAA;MACA4H,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA3H,cAAA;MACA2H,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA1H,oBAAA;MACA0H,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAzH,qBAAA;MACA;MACA,OAAAyH,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAAlI,SAAA,SAAAA,SAAA,CAAAmI,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAjI,SAAA;QACA6H,MAAA,CAAAK,YAAA,QAAAlI,SAAA;MACA;MACA6H,MAAA,CAAA1I,iBAAA;MACA0I,MAAA,CAAAtJ,OAAA,QAAAgF,qBAAA,CAAAhF,OAAA;MACAsJ,MAAA,CAAArJ,QAAA,QAAA+E,qBAAA,CAAA/E,QAAA;MACA,IAAA4J,4CAAA,EAAAP,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAgK,OAAA,CAAA5N,gBAAA,GAAA4D,QAAA,CAAAmB,IAAA;QACA6I,OAAA,CAAA1N,eAAA,GAAA0D,QAAA,CAAAlJ,KAAA;MACA;IACA;IACA,cACAmT,oBAAA,WAAAA,qBAAA1J,GAAA;MAAA,IAAA2J,OAAA;MACA,KAAA/P,kBAAA;QACAC,EAAA,EAAAmG,GAAA,CAAAnG,EAAA;QACA3C,eAAA,EAAA8I,GAAA,CAAA9I,eAAA;QACA4C,mBAAA;QACAC,aAAA;QACAC,aAAA;QACA5C,aAAA;QACA6C,kBAAA;MACA;;MAEA;MACA,IAAAiP,kDAAA;QACAlI,iBAAA,EAAAhB,GAAA,CAAAgB,iBAAA;QACAmI,UAAA,EAAAnJ,GAAA,CAAAmJ;MACA,GAAA7J,IAAA,WAAAG,QAAA;QACAkK,OAAA,CAAAxP,eAAA,GAAAsF,QAAA,CAAAzJ,IAAA;MACA;MACA;MACA,IAAAoT,+CAAA,IAAA9J,IAAA,WAAAG,QAAA;QACAkK,OAAA,CAAA/N,iBAAA,GAAA6D,QAAA,CAAAzJ,IAAA;MACA;MACA,KAAA2D,kBAAA;IACA;IACA,eACAiQ,sBAAA,WAAAA,uBAAA5J,GAAA;MAAA,IAAA6J,OAAA;MACA,KAAA5G,QAAA,mBAAAjD,GAAA,CAAA9I,eAAA,aAAAoI,IAAA;QACA,WAAA4D,2CAAA,EAAAlD,GAAA,CAAAnG,EAAA;MACA,GAAAyF,IAAA;QACAuK,OAAA,CAAAhK,sBAAA;QACAgK,OAAA,CAAA/G,UAAA;MACA,GAAAK,KAAA;IACA;IACA,eACA2G,sBAAA,WAAAA,uBAAA9J,GAAA;MAAA,IAAA+J,OAAA;MACA,KAAA9L,gBAAA,GAAA+B,GAAA;MACA,KAAAzC,UAAA,CAAArG,eAAA,GAAA8I,GAAA,CAAA9I,eAAA;MACA,KAAAqG,UAAA,CAAAC,YAAA;MACA,KAAAH,gBAAA;MACA,KAAAwI,SAAA;QACAkE,OAAA,CAAArH,KAAA,CAAAnF,UAAA,CAAAuI,aAAA;MACA;IACA;IACA,mBACAkE,0BAAA,WAAAA,2BAAAC,OAAA;MACA,KAAAnO,qBAAA,CAAA/E,QAAA,GAAAkT,OAAA;MACA,KAAApK,sBAAA;IACA;IACA,iBACAqK,6BAAA,WAAAA,8BAAAC,OAAA;MACA,KAAArO,qBAAA,CAAAhF,OAAA,GAAAqT,OAAA;MACA,KAAAtK,sBAAA;IACA;IACA,kBACAuK,qBAAA,WAAAA,sBAAA;MACA,KAAApO,0BAAA,SAAAA,0BAAA;IACA;IACA,aACAqO,kBAAA,WAAAA,mBAAAvF,MAAA;MACA;MACA,KAAAjO,WAAA,CAAAiB,SAAA;MACA,KAAAjB,WAAA,CAAAM,gBAAA,GAAA2N,MAAA;MACA,KAAAjD,WAAA;IACA;IAEA,eACAyI,mBAAA,WAAAA,oBAAA;MACA;MACA,KAAAzT,WAAA,CAAAM,gBAAA;MACA;MACA,KAAAN,WAAA,CAAAiB,SAAA,QAAAjB,WAAA,CAAAiB,SAAA;MACA,KAAA+J,WAAA;MAEA,SAAAhL,WAAA,CAAAiB,SAAA;QACA,KAAAwL,QAAA,CAAAiH,IAAA;MACA;QACA,KAAAjH,QAAA,CAAAiH,IAAA;MACA;IACA;IACA,eACAC,cAAA,WAAAA,eAAAnJ,OAAA,EAAAoJ,eAAA;MACA,IAAA/O,GAAA,OAAAxC,IAAA;MACA,IAAAK,GAAA,OAAAL,IAAA,CAAAmI,OAAA;MACA,IAAAqJ,MAAA,GAAAD,eAAA,OAAAvR,IAAA,CAAAuR,eAAA;;MAEA;MACA,IAAAE,SAAA,GAAAC,IAAA,CAAAC,IAAA,EAAAtR,GAAA,GAAAmC,GAAA;;MAEA;MACA,IAAAgP,MAAA,IAAAhP,GAAA,GAAAgP,MAAA;QACA;MACA;MAEA,IAAAC,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA;QACA;MACA;IACA;IACA,eACAG,cAAA,WAAAA,eAAAzJ,OAAA,EAAAoJ,eAAA;MACA,IAAA/O,GAAA,OAAAxC,IAAA;MACA,IAAAK,GAAA,OAAAL,IAAA,CAAAmI,OAAA;MACA,IAAAqJ,MAAA,GAAAD,eAAA,OAAAvR,IAAA,CAAAuR,eAAA;MACA;MACA,IAAAE,SAAA,GAAAC,IAAA,CAAAC,IAAA,EAAAtR,GAAA,GAAAmC,GAAA;MACA;MACA,IAAAgP,MAAA,IAAAhP,GAAA,GAAAgP,MAAA;QACA;MACA;MAEA,IAAAC,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA;QACA;MACA;IACA;IACA,iBACAI,uBAAA,WAAAA,wBAAA;MACA,KAAAlL,sBAAA;MACA,KAAAyD,QAAA,CAAAK,OAAA;IACA;IACA,aACAqH,cAAA,WAAAA,eAAAhL,GAAA;MACA,IAAAtE,GAAA,OAAAxC,IAAA;MACA,IAAAmI,OAAA,OAAAnI,IAAA,CAAA8G,GAAA,CAAAqB,OAAA;MACA,IAAAvJ,SAAA;MACA,IAAAmT,WAAA;MACA;MACA,IAAAjL,GAAA,CAAA7I,gBAAA;QACA;QACA,IAAA6I,GAAA,CAAAzI,gBAAA;UACA,IAAAA,gBAAA,OAAA2B,IAAA,CAAA8G,GAAA,CAAAzI,gBAAA;UACA,IAAAA,gBAAA,GAAA8J,OAAA;YACAvJ,SAAA;YACAmT,WAAA,GAAAL,IAAA,CAAAC,IAAA,EAAAtT,gBAAA,GAAA8J,OAAA;UACA;QACA;MACA,WAAArB,GAAA,CAAA7I,gBAAA;QACA;QACA,IAAAuE,GAAA,GAAA2F,OAAA;UACAvJ,SAAA;UACAmT,WAAA,GAAAL,IAAA,CAAAC,IAAA,EAAAnP,GAAA,GAAA2F,OAAA;QACA;MACA,WAAArB,GAAA,CAAA7I,gBAAA;QACA;QACA,IAAAuE,GAAA,GAAA2F,OAAA;UACAvJ,SAAA;UACAmT,WAAA,GAAAL,IAAA,CAAAC,IAAA,EAAAnP,GAAA,GAAA2F,OAAA;QACA;MACA;MAEA;QACAvJ,SAAA,EAAAA,SAAA;QACAmT,WAAA,EAAAA;MACA;IACA;IAEA;IAEA;IACAC,qBAAA,WAAAA,sBAAAlL,GAAA;MACA,KAAAzF,qBAAA,GAAAyF,GAAA;MACA,KAAAxF,eAAA,GAAAwF,GAAA;MACA,KAAA5F,mBAAA;MACA,KAAAoM,aAAA,CAAAxG,GAAA,CAAAnG,EAAA;IACA;IAEA,aACA2M,aAAA,WAAAA,cAAA2E,qBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7D,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAA4D,SAAA;QAAA,IAAAC,qBAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,eAAA;QAAA,WAAAlE,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA+D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7D,IAAA,GAAA6D,SAAA,CAAA5D,IAAA;YAAA;cAAA4D,SAAA,CAAA7D,IAAA;cAAA6D,SAAA,CAAA5D,IAAA;cAAA,OAGAoD,OAAA,CAAAS,oBAAA,CAAAV,qBAAA;YAAA;cAAAG,qBAAA,GAAAM,SAAA,CAAAzD,IAAA;cACAiD,OAAA,CAAA/Q,aAAA,GAAAiR,qBAAA,CAAAtV,IAAA;;cAEA;cAAAuV,SAAA,OAAAO,2BAAA,CAAAxL,OAAA,EACA8K,OAAA,CAAA/Q,aAAA;cAAAuR,SAAA,CAAA7D,IAAA;cAAAwD,SAAA,CAAAQ,CAAA;YAAA;cAAA,KAAAP,KAAA,GAAAD,SAAA,CAAAS,CAAA,IAAAC,IAAA;gBAAAL,SAAA,CAAA5D,IAAA;gBAAA;cAAA;cAAAyD,KAAA,GAAAD,KAAA,CAAA9C,KAAA;cAAAkD,SAAA,CAAA5D,IAAA;cAAA,OACAoD,OAAA,CAAAc,oBAAA,CAAAT,KAAA;YAAA;cAAAG,SAAA,CAAA5D,IAAA;cAAA;YAAA;cAAA4D,SAAA,CAAA5D,IAAA;cAAA;YAAA;cAAA4D,SAAA,CAAA7D,IAAA;cAAA6D,SAAA,CAAAtD,EAAA,GAAAsD,SAAA;cAAAL,SAAA,CAAAY,CAAA,CAAAP,SAAA,CAAAtD,EAAA;YAAA;cAAAsD,SAAA,CAAA7D,IAAA;cAAAwD,SAAA,CAAAa,CAAA;cAAA,OAAAR,SAAA,CAAAS,MAAA;YAAA;cAAAT,SAAA,CAAA5D,IAAA;cAAA,OAIA,IAAAsE,wCAAA,EAAAnB,qBAAA;YAAA;cAAAO,eAAA,GAAAE,SAAA,CAAAzD,IAAA;cACAiD,OAAA,CAAA9Q,UAAA,GAAAoR,eAAA,CAAA1V,IAAA;cAAA4V,SAAA,CAAA5D,IAAA;cAAA;YAAA;cAAA4D,SAAA,CAAA7D,IAAA;cAAA6D,SAAA,CAAAW,EAAA,GAAAX,SAAA;cAEArD,OAAA,CAAAvE,KAAA,cAAA4H,SAAA,CAAAW,EAAA;cACAnB,OAAA,CAAA9H,QAAA,CAAAU,KAAA;YAAA;YAAA;cAAA,OAAA4H,SAAA,CAAApD,IAAA;UAAA;QAAA,GAAA6C,QAAA;MAAA;IAEA;IAEA,gBACAa,oBAAA,WAAAA,qBAAAT,KAAA;MAAA,IAAAe,OAAA;MAAA,WAAAjF,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAgF,SAAA;QAAA,IAAAhN,QAAA;QAAA,WAAA+H,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA8E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5E,IAAA,GAAA4E,SAAA,CAAA3E,IAAA;YAAA;cAAA2E,SAAA,CAAA5E,IAAA;cAAA4E,SAAA,CAAA3E,IAAA;cAAA,OAEA,IAAA4E,4CAAA,EAAAnB,KAAA,CAAA5R,EAAA;YAAA;cAAA4F,QAAA,GAAAkN,SAAA,CAAAxE,IAAA;cACAqE,OAAA,CAAAK,IAAA,CAAApB,KAAA,iBAAAhM,QAAA,CAAAzJ,IAAA;cAAA2W,SAAA,CAAA3E,IAAA;cAAA;YAAA;cAAA2E,SAAA,CAAA5E,IAAA;cAAA4E,SAAA,CAAArE,EAAA,GAAAqE,SAAA;cAEApE,OAAA,CAAAvE,KAAA,gBAAA2I,SAAA,CAAArE,EAAA;cACAkE,OAAA,CAAAK,IAAA,CAAApB,KAAA;YAAA;YAAA;cAAA,OAAAkB,SAAA,CAAAnE,IAAA;UAAA;QAAA,GAAAiE,QAAA;MAAA;IAEA;IAEA,eACAZ,oBAAA,WAAAA,qBAAAV,qBAAA;MAAA,WAAA5D,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAqF,SAAA;QAAA,IAAArN,QAAA;QAAA,WAAA+H,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAAmF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjF,IAAA,GAAAiF,SAAA,CAAAhF,IAAA;YAAA;cAAAgF,SAAA,CAAAjF,IAAA;cAAAiF,SAAA,CAAAhF,IAAA;cAAA,OAGA,IAAAiF,qCAAA,EAAA9B,qBAAA;YAAA;cAAA1L,QAAA,GAAAuN,SAAA,CAAA7E,IAAA;cAAA,OAAA6E,SAAA,CAAAE,MAAA,WACAzN,QAAA;YAAA;cAAAuN,SAAA,CAAAjF,IAAA;cAAAiF,SAAA,CAAA1E,EAAA,GAAA0E,SAAA;cAEAzE,OAAA,CAAAvE,KAAA,eAAAgJ,SAAA,CAAA1E,EAAA;cAAA,OAAA0E,SAAA,CAAAE,MAAA,WACA;gBAAAlX,IAAA;cAAA;YAAA;YAAA;cAAA,OAAAgX,SAAA,CAAAxE,IAAA;UAAA;QAAA,GAAAsE,QAAA;MAAA;IAEA;IAEA,YACAK,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7F,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAA4F,SAAA;QAAA,IAAA5N,QAAA;QAAA,WAAA+H,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA0F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxF,IAAA,GAAAwF,SAAA,CAAAvF,IAAA;YAAA;cAAA,IAEAoF,OAAA,CAAAzO,YAAA;gBAAA4O,SAAA,CAAAvF,IAAA;gBAAA;cAAA;cACAoF,OAAA,CAAA9J,QAAA,CAAAC,OAAA;cAAA,OAAAgK,SAAA,CAAAL,MAAA;YAAA;cAAAK,SAAA,CAAAxF,IAAA;cAAAwF,SAAA,CAAAvF,IAAA;cAAA,OAKAoF,OAAA,CAAAnK,QAAA;gBACAuB,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;cACA;YAAA;cAAA6I,SAAA,CAAAvF,IAAA;cAAA,OAEA,IAAAmF,kCAAA,EAAAC,OAAA,CAAA7S,qBAAA,CAAAV,EAAA;YAAA;cAAA4F,QAAA,GAAA8N,SAAA,CAAApF,IAAA;cACAiF,OAAA,CAAA9J,QAAA,CAAAK,OAAA;;cAEA;cAAA4J,SAAA,CAAAvF,IAAA;cAAA,OACAoF,OAAA,CAAA5G,aAAA,CAAA4G,OAAA,CAAA7S,qBAAA,CAAAV,EAAA;YAAA;cAEA;cACAuT,OAAA,CAAAhO,OAAA;cAAAmO,SAAA,CAAAvF,IAAA;cAAA;YAAA;cAAAuF,SAAA,CAAAxF,IAAA;cAAAwF,SAAA,CAAAjF,EAAA,GAAAiF,SAAA;cAEA,IAAAA,SAAA,CAAAjF,EAAA;gBACA8E,OAAA,CAAA9J,QAAA,CAAAU,KAAA,gBAAAuJ,SAAA,CAAAjF,EAAA,CAAAnQ,OAAA,IAAAoV,SAAA,CAAAjF,EAAA;cACA;YAAA;YAAA;cAAA,OAAAiF,SAAA,CAAA/E,IAAA;UAAA;QAAA,GAAA6E,QAAA;MAAA;IAEA;IAEA,aACAG,gBAAA,WAAAA,iBAAA/B,KAAA;MAAA,IAAAgC,OAAA;MAAA,WAAAlG,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAiG,SAAA;QAAA,WAAAlG,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA+F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7F,IAAA,GAAA6F,SAAA,CAAA5F,IAAA;YAAA;cAAA,IACAyF,OAAA,CAAA9O,YAAA;gBAAAiP,SAAA,CAAA5F,IAAA;gBAAA;cAAA;cACAyF,OAAA,CAAAnK,QAAA,CAAAC,OAAA;cAAA,OAAAqK,SAAA,CAAAV,MAAA;YAAA;cAAAU,SAAA,CAAA7F,IAAA;cAAA6F,SAAA,CAAA5F,IAAA;cAAA,OAKAyF,OAAA,CAAAxK,QAAA,kCAAAW,MAAA,CAAA6H,KAAA,CAAAoC,UAAA;gBACArJ,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;cACA;YAAA;cAAAkJ,SAAA,CAAA5F,IAAA;cAAA,OAEA,IAAAwF,qCAAA,EAAA/B,KAAA,CAAA5R,EAAA;YAAA;cACA4T,OAAA,CAAAnK,QAAA,CAAAK,OAAA;;cAEA;cAAAiK,SAAA,CAAA5F,IAAA;cAAA,OACAyF,OAAA,CAAAjH,aAAA,CAAAiH,OAAA,CAAAlT,qBAAA,CAAAV,EAAA;YAAA;cACA4T,OAAA,CAAArO,OAAA;cAAAwO,SAAA,CAAA5F,IAAA;cAAA;YAAA;cAAA4F,SAAA,CAAA7F,IAAA;cAAA6F,SAAA,CAAAtF,EAAA,GAAAsF,SAAA;cAEA,IAAAA,SAAA,CAAAtF,EAAA;gBACAmF,OAAA,CAAAnK,QAAA,CAAAU,KAAA,eAAA4J,SAAA,CAAAtF,EAAA,CAAAnQ,OAAA,IAAAyV,SAAA,CAAAtF,EAAA;cACA;YAAA;YAAA;cAAA,OAAAsF,SAAA,CAAApF,IAAA;UAAA;QAAA,GAAAkF,QAAA;MAAA;IAEA;IAEA,aACAI,iBAAA,WAAAA,kBAAArC,KAAA;MACA,UAAA9M,YAAA;QACA,KAAA2E,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAvI,eAAA;MACA,KAAAD,kBAAA,GAAA0Q,KAAA;;MAEA;MACA,KAAA7Q,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;;MAEA;MACA,KAAAH,eAAA;IACA;IAEA,gBACAoT,sBAAA,WAAAA,uBAAA;MACA,UAAApP,YAAA;QACA,KAAA2E,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAvI,eAAA;MACA,KAAAD,kBAAA;;MAEA;MACA,KAAAH,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;;MAEA;MACA,KAAAH,eAAA;IACA;IAEA,aACAqT,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA1G,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAyG,SAAA;QAAA,WAAA1G,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAAuG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArG,IAAA,GAAAqG,SAAA,CAAApG,IAAA;YAAA;cAAAoG,SAAA,CAAArG,IAAA;cAAA,MAEAkG,OAAA,CAAAjT,eAAA;gBAAAoT,SAAA,CAAApG,IAAA;gBAAA;cAAA;cAAAoG,SAAA,CAAApG,IAAA;cAAA,OAEA,IAAA8F,sCAAA,EACAG,OAAA,CAAAlT,kBAAA,CAAAlB,EAAA,EACAoU,OAAA,CAAArT,eAAA,CAAAC,iBAAA,QACAoT,OAAA,CAAArT,eAAA,CAAAE,MAAA,QACAmT,OAAA,CAAAlT,kBAAA,CAAAqB,cAAA,MACA;YAAA;cACA6R,OAAA,CAAA3K,QAAA,CAAAK,OAAA;cAAAyK,SAAA,CAAApG,IAAA;cAAA;YAAA;cAAAoG,SAAA,CAAApG,IAAA;cAAA,OAGA,IAAA+F,2CAAA,EAAAE,OAAA,CAAA1T,qBAAA,CAAAV,EAAA;YAAA;cACAoU,OAAA,CAAA3K,QAAA,CAAAK,OAAA;YAAA;cAGAsK,OAAA,CAAAtT,eAAA;;cAEA;cAAAyT,SAAA,CAAApG,IAAA;cAAA,OACAiG,OAAA,CAAAzH,aAAA,CAAAyH,OAAA,CAAA1T,qBAAA,CAAAV,EAAA;YAAA;cAEA;cACAoU,OAAA,CAAA7O,OAAA;cAAAgP,SAAA,CAAApG,IAAA;cAAA;YAAA;cAAAoG,SAAA,CAAArG,IAAA;cAAAqG,SAAA,CAAA9F,EAAA,GAAA8F,SAAA;cAEA7F,OAAA,CAAAvE,KAAA,YAAAoK,SAAA,CAAA9F,EAAA;cACA2F,OAAA,CAAA3K,QAAA,CAAAU,KAAA,eAAAoK,SAAA,CAAA9F,EAAA,CAAAnQ,OAAA,IAAAiW,SAAA,CAAA9F,EAAA;YAAA;YAAA;cAAA,OAAA8F,SAAA,CAAA5F,IAAA;UAAA;QAAA,GAAA0F,QAAA;MAAA;IAEA;IAEA,aACAG,eAAA,WAAAA,gBAAA5C,KAAA;MAAA,IAAA6C,OAAA;MAAA,WAAA/G,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAA8G,SAAA;QAAA,IAAA9O,QAAA,EAAA+O,WAAA;QAAA,WAAAhH,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA6G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3G,IAAA,GAAA2G,SAAA,CAAA1G,IAAA;YAAA;cAAA0G,SAAA,CAAA3G,IAAA;cAAA2G,SAAA,CAAA1G,IAAA;cAAA,OAEA,IAAA4E,4CAAA,EAAAnB,KAAA,CAAA5R,EAAA;YAAA;cAAA4F,QAAA,GAAAiP,SAAA,CAAAvG,IAAA;cACAqG,WAAA,GAAA/O,QAAA,CAAAzJ,IAAA,QAEA;cACAsY,OAAA,CAAA5T,eAAA,OAAA2F,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAmL,KAAA;gBACA+C,WAAA,EAAAA;cAAA,EACA;cAEAF,OAAA,CAAA7T,eAAA;cAAAiU,SAAA,CAAA1G,IAAA;cAAA;YAAA;cAAA0G,SAAA,CAAA3G,IAAA;cAAA2G,SAAA,CAAApG,EAAA,GAAAoG,SAAA;cAEAnG,OAAA,CAAAvE,KAAA,cAAA0K,SAAA,CAAApG,EAAA;cACAgG,OAAA,CAAAhL,QAAA,CAAAU,KAAA;YAAA;YAAA;cAAA,OAAA0K,SAAA,CAAAlG,IAAA;UAAA;QAAA,GAAA+F,QAAA;MAAA;IAEA;IAEA,aACAI,iBAAA,WAAAA,kBAAAC,SAAA;MACA,KAAAA,SAAA;MAEA,IAAApV,KAAA,OAAAN,IAAA,CAAA0V,SAAA;MACA,IAAAlT,GAAA,OAAAxC,IAAA;MACA,IAAA2V,MAAA,GAAAnT,GAAA,GAAAlC,KAAA;MACA,IAAAsV,SAAA,GAAAlE,IAAA,CAAAmE,KAAA,CAAAF,MAAA;MACA,IAAAG,WAAA,GAAApE,IAAA,CAAAmE,KAAA,CAAAF,MAAA;MAEA,IAAAC,SAAA;QACA,UAAAlL,MAAA,CAAAkL,SAAA,kBAAAlL,MAAA,CAAAoL,WAAA;MACA;QACA,UAAApL,MAAA,CAAAoL,WAAA;MACA;IACA;IAEA,cACAC,kBAAA,WAAAA,mBAAAxD,KAAA;MAAA,IAAAyD,OAAA;MACA,KAAArC,IAAA,CAAApB,KAAA;MACA,KAAA5F,SAAA;QACA;QACA,IAAAsJ,KAAA,GAAAD,OAAA,CAAAE,GAAA,CAAAC,aAAA,kBAAAzL,MAAA,CAAA6H,KAAA,CAAArP,cAAA;QACA,IAAA+S,KAAA,EAAAA,KAAA,CAAAG,KAAA;MACA;IACA;IAEA,kBACAC,uBAAA,WAAAA,wBAAA9D,KAAA;MACA,KAAAnQ,yBAAA,GAAAmQ,KAAA;MACA,KAAAvQ,iBAAA;QACAC,cAAA;QACAC,cAAA;QACAN,MAAA;MACA;MACA,KAAAG,iBAAA;IACA;IAEA,kBACAuU,kBAAA,WAAAA,mBAAA;MACA,KAAAvU,iBAAA;MACA,KAAAK,yBAAA;MACA,KAAAoH,KAAA,CAAAxH,iBAAA,SAAAwH,KAAA,CAAAxH,iBAAA,CAAAuU,WAAA;IACA;IAEA,eACAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MAAA,WAAApI,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAmI,SAAA;QAAA,IAAAC,gBAAA;QAAA,WAAArI,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAAkI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhI,IAAA,GAAAgI,SAAA,CAAA/H,IAAA;YAAA;cAAA+H,SAAA,CAAAhI,IAAA;cAAAgI,SAAA,CAAA/H,IAAA;cAAA,OAEA2H,OAAA,CAAAjN,KAAA,CAAAxH,iBAAA,CAAAyH,QAAA;YAAA;cAEAkN,gBAAA;gBACAG,OAAA,EAAAL,OAAA,CAAArU,yBAAA,CAAAzB,EAAA;gBACAsB,cAAA,EAAAwU,OAAA,CAAAzU,iBAAA,CAAAC,cAAA;gBACAC,cAAA,EAAAuU,OAAA,CAAAzU,iBAAA,CAAAE,cAAA;gBACAN,MAAA,EAAA6U,OAAA,CAAAzU,iBAAA,CAAAJ;cACA;cAAAiV,SAAA,CAAA/H,IAAA;cAAA,OAEA,IAAAiI,yCAAA,EAAAJ,gBAAA;YAAA;cAEAF,OAAA,CAAArM,QAAA,CAAAK,OAAA;cACAgM,OAAA,CAAA1U,iBAAA;;cAEA;cAAA8U,SAAA,CAAA/H,IAAA;cAAA,OACA2H,OAAA,CAAAzD,oBAAA,CAAAyD,OAAA,CAAArU,yBAAA;YAAA;cAAAyU,SAAA,CAAA/H,IAAA;cAAA;YAAA;cAAA+H,SAAA,CAAAhI,IAAA;cAAAgI,SAAA,CAAAzH,EAAA,GAAAyH,SAAA;cAEAJ,OAAA,CAAArM,QAAA,CAAAU,KAAA,iBAAA+L,SAAA,CAAAzH,EAAA,CAAAnQ,OAAA,IAAA4X,SAAA,CAAAzH,EAAA;YAAA;YAAA;cAAA,OAAAyH,SAAA,CAAAvH,IAAA;UAAA;QAAA,GAAAoH,QAAA;MAAA;IAEA;IAEA,aACAM,gBAAA,WAAAA,iBAAAzE,KAAA,EAAA0E,UAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7I,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAA4I,UAAA;QAAA,WAAA7I,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA0I,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxI,IAAA,GAAAwI,UAAA,CAAAvI,IAAA;YAAA;cAAAuI,UAAA,CAAAxI,IAAA;cAAAwI,UAAA,CAAAvI,IAAA;cAAA,OAEAoI,OAAA,CAAAnN,QAAA;gBACAuB,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;cACA;YAAA;cAAA6L,UAAA,CAAAvI,IAAA;cAAA,OAEA,IAAAwI,8CAAA,EAAAL,UAAA,CAAAtW,EAAA;YAAA;cACAuW,OAAA,CAAA9M,QAAA,CAAAK,OAAA;;cAEA;cAAA4M,UAAA,CAAAvI,IAAA;cAAA,OACAoI,OAAA,CAAAlE,oBAAA,CAAAT,KAAA;YAAA;cAAA8E,UAAA,CAAAvI,IAAA;cAAA;YAAA;cAAAuI,UAAA,CAAAxI,IAAA;cAAAwI,UAAA,CAAAjI,EAAA,GAAAiI,UAAA;cAEA,IAAAA,UAAA,CAAAjI,EAAA;gBACA8H,OAAA,CAAA9M,QAAA,CAAAU,KAAA,iBAAAuM,UAAA,CAAAjI,EAAA,CAAAnQ,OAAA,IAAAoY,UAAA,CAAAjI,EAAA;cACA;YAAA;YAAA;cAAA,OAAAiI,UAAA,CAAA/H,IAAA;UAAA;QAAA,GAAA6H,SAAA;MAAA;IAEA;IAEA,eACAI,kBAAA,WAAAA,mBAAA3L,MAAA;MACA,IAAA4L,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAA5L,MAAA;IACA;IAEA,eACA6L,kBAAA,WAAAA,mBAAA7L,MAAA;MACA,IAAA8L,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA9L,MAAA;IACA;IAEA,eACA+L,wBAAA,WAAAA,yBAAAC,UAAA;MACA,KAAAA,UAAA;MAEA,IAAAC,SAAA,GAAAD,UAAA,CAAAE,WAAA;MACA,IAAAD,SAAA,CAAAE,QAAA,UAAAF,SAAA,CAAAE,QAAA,UAAAF,SAAA,CAAAE,QAAA;QACA;MACA,WAAAF,SAAA,CAAAE,QAAA,UAAAF,SAAA,CAAAE,QAAA;QACA;MACA,WAAAF,SAAA,CAAAE,QAAA,SAAAF,SAAA,CAAAE,QAAA,WAAAF,SAAA,CAAAE,QAAA;QACA;MACA;QACA;MACA;IACA;IAEA,qBACAC,oBAAA,WAAAA,qBAAA;MACA;MACA,KAAA7W,aAAA;MACA,KAAAC,UAAA;MACA,KAAAC,qBAAA;MACA,KAAAC,eAAA;MACA,KAAAE,eAAA;MAEA,KAAAE,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;;MAEA;MACA,KAAAL,eAAA;MACA,KAAAQ,iBAAA;MACA,KAAAN,eAAA;IACA;IAEA,qBACAwW,gBAAA,WAAAA,iBAAA;MACA;MACA,KAAAzW,eAAA;IACA;IAEA,qBACA0W,gBAAA,WAAAA,iBAAA;MACA;MACA,KAAAzW,eAAA;MACA,KAAAK,eAAA;MACA,KAAAD,kBAAA;MACA,KAAAH,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;MACA;MACA,SAAA4H,KAAA,CAAA9H,eAAA;QACA,KAAA8H,KAAA,CAAA9H,eAAA,CAAAkL,aAAA;MACA;IACA;IAEA,aACAuL,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA/J,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAA8J,UAAA;QAAA,WAAA/J,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA4J,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1J,IAAA,GAAA0J,UAAA,CAAAzJ,IAAA;YAAA;cAAA,KACAsJ,OAAA,CAAA/W,qBAAA;gBAAAkX,UAAA,CAAAzJ,IAAA;gBAAA;cAAA;cAAAyJ,UAAA,CAAAzJ,IAAA;cAAA,OACAsJ,OAAA,CAAA9K,aAAA,CAAA8K,OAAA,CAAA/W,qBAAA,CAAAV,EAAA;YAAA;cACAyX,OAAA,CAAAhO,QAAA,CAAAK,OAAA;YAAA;YAAA;cAAA,OAAA8N,UAAA,CAAAjJ,IAAA;UAAA;QAAA,GAAA+I,SAAA;MAAA;IAEA;IAEA,gBACA3L,sBAAA,WAAAA,uBAAAuF,qBAAA;MAAA,IAAAuG,OAAA;MAAA,WAAAnK,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAkK,UAAA;QAAA,IAAAlS,QAAA;QAAA,WAAA+H,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAAgK,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9J,IAAA,GAAA8J,UAAA,CAAA7J,IAAA;YAAA;cAAA6J,UAAA,CAAA9J,IAAA;cAAA8J,UAAA,CAAA7J,IAAA;cAAA,OAEA,IAAA8J,+CAAA,EAAA3G,qBAAA;YAAA;cAAA1L,QAAA,GAAAoS,UAAA,CAAA1J,IAAA;cACAuJ,OAAA,CAAArV,kBAAA,GAAAoD,QAAA,CAAAzJ,IAAA;cAAA6b,UAAA,CAAA7J,IAAA;cAAA;YAAA;cAAA6J,UAAA,CAAA9J,IAAA;cAAA8J,UAAA,CAAAvJ,EAAA,GAAAuJ,UAAA;cAEAtJ,OAAA,CAAAvE,KAAA,iBAAA6N,UAAA,CAAAvJ,EAAA;cACAoJ,OAAA,CAAArV,kBAAA;cACAqV,OAAA,CAAApO,QAAA,CAAAU,KAAA;YAAA;YAAA;cAAA,OAAA6N,UAAA,CAAArJ,IAAA;UAAA;QAAA,GAAAmJ,SAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}]}