{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue", "mtime": 1754284747781}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_engineer<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "_projectItemOrder", "_vueTreeselect", "_interopRequireDefault", "_customer", "_validate", "_executionAddOrEdit", "name", "components", "Treeselect", "executionAddOrEdit", "data", "loading", "ids", "selectedRows", "single", "multiple", "showSearch", "total", "engineer<PERSON>ampleOrderList", "researchDeptDatas", "dylbOptions", "title", "open", "queryParams", "pageNum", "pageSize", "userId", "nick<PERSON><PERSON>", "sampleOrderCode", "completionStatus", "scheduledDate", "startDate", "actualStartTime", "actualFinishTime", "deptId", "deptIds", "associationStatus", "customerId", "productName", "confirmCode", "isOverdue", "laboratory", "form", "rules", "required", "message", "trigger", "statusOptions", "serviceModeOptions", "date<PERSON><PERSON><PERSON>", "scheduledDateRange", "startDateRange", "actualStartTimeRange", "actualFinishTimeRange", "dataPickerOptions", "shortcuts", "text", "onClick", "picker", "today", "Date", "$emit", "yesterday", "setTime", "getTime", "end", "start", "statusOpen", "dashboardStats", "changeEngineerOpen", "changeEngineerForm", "id", "currentEngineerName", "oldEngineerId", "newEngineerId", "adjustWorkSchedule", "changeEngineerRules", "engineerOptions", "batchManagementOpen", "activeBatches", "allBatches", "selectedOrderForBatch", "currentBatchRow", "batchDetailOpen", "batchDetailData", "finishBatchOpen", "finishBatchForm", "qualityEvaluation", "remark", "currentFinishBatch", "finishBatchMode", "addExperimentOpen", "addExperimentForm", "experimentCode", "experimentNote", "addExperimentRules", "currentBatchForExperiment", "futureDatePickerOptions", "disabledDate", "time", "now", "engineerSelectType", "searchedEngineers", "unassignedOrders", "unassigned<PERSON>ueryP<PERSON><PERSON>", "unassignedTotal", "isUnassignedPanelCollapsed", "finishTaskOpen", "finishTaskLoading", "finishTaskForm", "laboratoryCode", "experimentCodeList", "finishTaskRules", "currentFinishRow", "updateLaboratoryCodeOpen", "updateLaboratoryCodeLoading", "updateLaboratoryCodeForm", "updateLaboratoryCodeRules", "currentUpdateLaboratoryCodeRow", "exportDialogOpen", "exportOptions", "exportLoading", "readonly", "currentProjectType", "confirmItemCodes", "customerOptions", "itemNames", "rejectDialogOpen", "rejectLoading", "rejectForm", "rejectReason", "overdueOperationOpen", "overdueOperationLoading", "overdueOperationForm", "expectedSampleTime", "reasonForNoSample", "solution", "currentOverdueRow", "rejectRules", "currentRejectRow", "computed", "canEditBatch", "created", "_this", "todayStr", "getFullYear", "String", "getMonth", "padStart", "getDate", "getList", "customerBaseAll", "then", "res", "getDicts", "response", "loadDashboardStats", "getResearchDepartments", "handleTree", "handleUnassignedOrders", "methods", "getProjectLevel", "row", "customerLevel", "projectLevel", "_this2", "params", "_objectSpread2", "default", "addDateRange", "beginDateRange", "endDateRange", "length", "listEngineerSampleOrder", "rows", "cancel", "reset", "serviceMode", "difficultyLevelId", "actualManHours", "estimatedManHours", "standardManHours", "isLocked", "endDate", "checkType", "sampleOrderRemark", "createBy", "createTime", "updateBy", "updateTime", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "handleAdd", "handleUpdate", "_this3", "getEngineerSampleOrder", "addEngineerSampleOrder", "submitForm", "_this4", "$refs", "validate", "valid", "updateEngineerSampleOrder", "msgSuccess", "handleDelete", "_this5", "$confirm", "delEngineerSampleOrder", "catch", "handleExport", "confirmExport", "$message", "warning", "executeExports", "index", "_this6", "success", "concat", "option", "typeName", "doExport", "error", "exportType", "_this7", "exportEngineerSampleOrder", "download", "msg", "doExportSampleOrder", "_this8", "confirmButtonText", "cancelButtonText", "type", "handleStart", "_this9", "updateSampleOrderStatus", "status", "handleDownloadSampleOrder", "_this10", "exportNrwItem", "itemId", "projectId", "handleBatchExportNrw", "_this11", "$modal", "msgError", "exportData", "exportMultipleNrw", "handleFinish", "_this12", "loadExperimentCodeList", "$nextTick", "clearValidate", "confirmFinishTask", "_this13", "Array", "isArray", "join", "handleFinishFromBatch", "_this14", "handleStartFromBatch", "_this15", "loadBatchData", "handleUpdateLaboratoryCode", "_this16", "confirmUpdateLaboratoryCode", "_this17", "handleReject", "_this18", "confirmReject", "_this19", "handleOverdueOperation", "_this20", "confirmOverdueOperation", "_this21", "orderDetail", "_this22", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "orderId", "wrap", "_callee$", "_context", "prev", "next", "projectOrderId", "getExecutionByOrderInfo", "sent", "isNull", "show", "t0", "console", "stop", "handleLockChange", "value", "_this23", "handleTableLockChange", "_this24", "_this25", "getDashboardStats", "handleChangeEngineer", "_this26", "getEngineersByDifficultyLevel", "categoryId", "getResearchDepartmentsUser", "submitChangeEngineer", "_this27", "sampleOrderId", "changeEngineer", "_this28", "handleAssignEngineer", "_this29", "handleDeleteUnassigned", "_this30", "handleRejectUnassigned", "_this31", "handleUnassignedSizeChange", "newSize", "handleUnassignedCurrentChange", "newPage", "toggleUnassignedPanel", "handleStatusFilter", "handleOverdueFilter", "info", "getUrgencyType", "latestStartTime", "latest", "daysToEnd", "Math", "ceil", "getUrgencyText", "handleRefreshUnassigned", "getOverdueInfo", "overdueDays", "handleBatchManagement", "engineerSampleOrderId", "_this32", "_callee2", "activeBatchesResponse", "_iterator", "_step", "batch", "batchesResponse", "_callee2$", "_context2", "getActiveBatchesData", "_createForOfIteratorHelper2", "s", "n", "done", "loadBatchExperiments", "e", "f", "finish", "getBatchesByOrderId", "t1", "_this33", "_callee3", "_callee3$", "_context3", "getExperimentsByBatchId", "$set", "_callee4", "_callee4$", "_context4", "getActiveBatches", "abrupt", "startNewBatch", "_this34", "_callee5", "_callee5$", "_context5", "startSingleBatch", "_this35", "_callee6", "_callee6$", "_context6", "batchIndex", "finishSingleBatch", "finishAllActiveBatches", "submitFinishBatch", "_this36", "_callee7", "_callee7$", "_context7", "viewBatchDetail", "_this37", "_callee8", "experiments", "_callee8$", "_context8", "calculateDuration", "startTime", "diffMs", "diffHours", "floor", "diffMinutes", "editLaboratoryCode", "_this38", "input", "$el", "querySelector", "focus", "showAddExperimentDialog", "closeAddExperiment", "resetFields", "submitAddExperiment", "_this39", "_callee9", "experimentRecord", "_callee9$", "_context9", "batchId", "addExperimentToBatch", "removeExperiment", "experiment", "_this40", "_callee10", "_callee10$", "_context10", "removeExperimentFromBatch", "getBatchStatusText", "statusMap", "getBatchStatusType", "typeMap", "getQualityEvaluationType", "evaluation", "lowerEval", "toLowerCase", "includes", "closeBatchManagement", "closeBatchDetail", "closeFinishBatch", "refreshBatchData", "_this41", "_callee11", "_callee11$", "_context11", "_this42", "_callee12", "_callee12$", "_context12", "getBatchExperimentCodeList"], "sources": ["src/views/software/engineerSampleOrder/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 统计概览 -->\r\n    <el-row :gutter=\"20\" class=\"stats-row\" style=\"margin-bottom: 20px;\">\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card total\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-s-order\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">总任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.total || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card completed\" @click.native=\"handleStatusFilter('2')\" :class=\"{'active': queryParams.completionStatus === '2'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-check\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">已完成</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.completed || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card in-progress\" @click.native=\"handleStatusFilter('1')\" :class=\"{'active': queryParams.completionStatus === '1'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-loading\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">进行中</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.inProgress || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card overdue\" @click.native=\"handleOverdueFilter\" :class=\"{'active': queryParams.isOverdue === 1}\" style=\"cursor: pointer;\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-warning\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">逾期任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.overdue || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 未分配工程师的打样单告警 -->\r\n    <el-card class=\"alert-card\">\r\n      <div slot=\"header\" class=\"alert-header\">\r\n        <div class=\"alert-title\" @click=\"toggleUnassignedPanel\">\r\n          <span><i class=\"el-icon-warning-outline\"></i>待分配打样单({{ unassignedTotal }}个)</span>\r\n        </div>\r\n        <div class=\"alert-actions\">\r\n          <el-button\r\n            type=\"text\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"handleRefreshUnassigned\"\r\n            style=\"margin-right: 10px\"\r\n            title=\"刷新列表\">\r\n            刷新\r\n          </el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            @click=\"toggleUnassignedPanel\"\r\n            style=\"margin-right: 10px\">\r\n            {{ isUnassignedPanelCollapsed ? '展开' : '收起' }}\r\n            <i :class=\"isUnassignedPanelCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\r\n          </el-button>\r\n          <el-pagination\r\n            @size-change=\"handleUnassignedSizeChange\"\r\n            @current-change=\"handleUnassignedCurrentChange\"\r\n            :current-page=\"unassignedQueryParams.pageNum\"\r\n            :page-sizes=\"[8, 16, 24, 32]\"\r\n            :page-size=\"unassignedQueryParams.pageSize\"\r\n            layout=\"total, sizes, prev, pager, next\"\r\n            :total=\"unassignedTotal\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n      <el-collapse-transition>\r\n        <div v-show=\"!isUnassignedPanelCollapsed\">\r\n          <el-row :gutter=\"20\" class=\"alert-cards\">\r\n            <el-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\" v-for=\"item in unassignedOrders\" :key=\"item.id\">\r\n              <el-card shadow=\"hover\" class=\"alert-item\">\r\n                <div class=\"alert-item-header\">\r\n                  <div class=\"order-code-section\">\r\n                    <span style=\"color: #00afff;cursor: pointer\"  @click=\"orderDetail(item)\" class=\"order-code\">{{ item.sampleOrderCode }}</span>\r\n                    <el-tag\r\n                      :type=\"getUrgencyType(item.endDate, item.latestStartTime)\"\r\n                      size=\"mini\"\r\n                      class=\"urgency-tag\">\r\n                      {{ getUrgencyText(item.endDate, item.latestStartTime) }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n                <div class=\"alert-item-content\">\r\n                  <div class=\"info-section\">\r\n                    <!-- 预估工时和难度等级 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\" v-if=\"item.latestStartTime\">\r\n                        <i class=\"el-icon-alarm-clock\" style=\"color: #909399;\"></i>\r\n                        <span class=\"info-label\">最晚开始:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-time\" style=\"color: #409EFF;\"></i>\r\n                        <span class=\"info-label\">预估工时:</span>\r\n                        <span class=\"info-value\">{{ item.estimatedManHours || '-' }}H</span>\r\n                      </div>\r\n                    </div>\r\n                    <!-- 最晚开始日期和截止日期排 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\">\r\n                        <i class=\"el-icon-date\" style=\"color: #F56C6C;\"></i>\r\n                        <span class=\"info-label\">截止日期:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.endDate, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-star-on\" style=\"color: #E6A23C;\"></i>\r\n                        <span class=\"info-label\">难度等级:</span>\r\n                        <el-tooltip :content=\"selectDictLabel(dylbOptions, item.difficultyLevelId)\" placement=\"top\">\r\n                          <span class=\"info-value difficulty-text\">{{selectDictLabel(dylbOptions,item.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n                        </el-tooltip>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <el-tooltip v-if=\"item.failureReason\" :content=\"item.failureReason\" placement=\"top\">\r\n                    <div class=\"info-reason\">\r\n                      <i class=\"el-icon-warning-outline\"></i>\r\n                      <span class=\"reason-text\">{{ item.failureReason }}</span>\r\n                    </div>\r\n                  </el-tooltip>\r\n                </div>\r\n                <div class=\"alert-item-footer\">\r\n                  <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-user-solid\" @click=\"handleAssignEngineer(item)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\">\r\n                    分配工程师\r\n                  </el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-close\" @click=\"handleRejectUnassigned(item)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" class=\"reject-btn\" style=\"color: #F56C6C;\">\r\n                    驳回\r\n                  </el-button>\r\n                </div>\r\n              </el-card>\r\n           </el-col>\r\n          </el-row>\r\n         </div>\r\n      </el-collapse-transition>\r\n    </el-card>\r\n\r\n    <!-- 筛选条件 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"客户\" prop=\"customerId\">\r\n        <el-select v-model=\"queryParams.customerId\" filterable placeholder=\"客户\" clearable size=\"small\">\r\n          <el-option\r\n            v-for=\"dict in customerOptions\"\r\n            :key=\"dict.id\"\r\n            :label=\"dict.name\"\r\n            :value=\"dict.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"日期范围\" prop=\"dateRange\" label-width=\"80px\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"\r\n          style=\"width: 240px\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n        <el-date-picker\r\n          v-model=\"scheduledDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"申请日期\" prop=\"startDate\">\r\n        <el-date-picker\r\n          v-model=\"startDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"研发组别\" prop=\"deptIds\">\r\n        <el-select v-model=\"queryParams.deptIds\" multiple filterable placeholder=\"请选择对应研发组别\">\r\n          <el-option\r\n            v-for=\"dept in researchDeptDatas\"\r\n            :key=\"dept.deptId\"\r\n            :label=\"dept.deptName\"\r\n            :value=\"dept.deptId\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"服务模式\" prop=\"serviceMode\">\r\n        <el-select v-model=\"queryParams.serviceMode\" placeholder=\"请选择客户服务模式\" clearable>\r\n          <el-option\r\n            v-for=\"dict in serviceModeOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"实验室\" prop=\"laboratory\">\r\n        <el-select v-model=\"queryParams.laboratory\" placeholder=\"请选择实验室\" clearable>\r\n          <el-option label=\"宜侬\" value=\"0\" />\r\n          <el-option label=\"瀛彩\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"确认编码\" prop=\"confirmCode\">\r\n        <el-input\r\n          v-model.trim=\"queryParams.confirmCode\"\r\n          placeholder=\"请输入确认编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"产品名称\" prop=\"productName\">\r\n        <el-input\r\n          v-model.trim=\"queryParams.productName\"\r\n          placeholder=\"请输入产品名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"姓名\" prop=\"nickName\" label-width=\"50px\">\r\n        <el-input v-model.trim=\"queryParams.nickName\" placeholder=\"请输入工程师姓名\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"打样单编号\" label-width=\"90px\" prop=\"sampleOrderCode\">\r\n        <el-input v-model.trim=\"queryParams.sampleOrderCode\" placeholder=\"请输入打样单编号\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n        <el-select v-model=\"queryParams.completionStatus\" placeholder=\"请选择完成情况\" clearable>\r\n          <el-option\r\n            v-for=\"dict in statusOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualStartTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualFinishTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:add']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:edit']\">修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:remove']\">删除</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"doExportSampleOrder\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleBatchExportNrw\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出打样单任务</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAddSampleOrder\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:add']\">新增打样单</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 主要内容区域 -->\r\n     <!-- 工单列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"engineerSampleOrderList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"主键ID\" align=\"center\" prop=\"id\" /> -->\r\n<!--      <el-table-column label=\"工程师ID\" align=\"center\" prop=\"userId\" />-->\r\n      <el-table-column align=\"center\" prop=\"sampleOrderCode\" width=\"160\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          打样单编号\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              点击编号可查看详细信息\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"orderDetail(scope.row)\">{{ scope.row.sampleOrderCode }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"startDate\" width=\"140\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          申请日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              客户提交打样申请的日期时间\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"scheduledDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          排单日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              工程师被安排处理此打样单的日期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.scheduledDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"latestStartTime\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚开始日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              必须在此日期前开始工作，否则可能影响交期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"endDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚截至日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作必须在此日期前完成\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\" prop=\"completionStatus\">\r\n        <template slot=\"header\">\r\n          完成情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              当前打样单的状态\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"scope.row.completionStatus == '0' ? 'info' :\r\n                   scope.row.completionStatus == '1' ? 'primary' :\r\n                   scope.row.completionStatus == '2' ? 'success' : 'info'\"\r\n            size=\"mini\"\r\n          >\r\n            {{selectDictLabel(statusOptions,scope.row.completionStatus)}}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          批次信息\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              显示当前批次总数\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.totalBatches > 0\">\r\n            <el-tag size=\"mini\" type=\"primary\">\r\n              {{ scope.row.totalBatches || 0 }}\r\n            </el-tag>\r\n          </div>\r\n          <div v-else>\r\n            -\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          逾期情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              根据截止日期判断是否逾期及逾期天数\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"getOverdueInfo(scope.row).isOverdue\">\r\n            <el-tag type=\"danger\" size=\"mini\" style=\"margin-bottom: 2px;\">\r\n              逾期 {{ getOverdueInfo(scope.row).overdueDays }}天\r\n            </el-tag>\r\n          </div>\r\n          <el-tag v-else type=\"success\" size=\"mini\">\r\n            正常\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"nickName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          跟进人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的工程师姓名及职级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.nickName }}{{ scope.row.rank ? '-' + scope.row.rank : '' }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"assistantName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          协助人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              此打样单难度高于工程师能力范围\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.assistantName }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"isLocked\" width=\"90\">\r\n        <template slot=\"header\">\r\n          锁定状态\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              锁定后不允许更换工程师，防止误操作\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.isLocked\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            @change=\"(val) => handleTableLockChange(val, scope.row)\">\r\n          </el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratoryCode\" width=\"150\">\r\n        <template slot=\"header\">\r\n          实验编码\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              实验室分配的唯一编码标识\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"difficultyLevelId\" width=\"80\">\r\n        <template slot=\"header\">\r\n          难度\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作的技术难度等级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"selectDictLabel(dylbOptions,scope.row.difficultyLevelId)\" placement=\"top\">\r\n            <span>{{selectDictLabel(dylbOptions,scope.row.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"标准工时\" align=\"center\" prop=\"standardManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.standardManHours\">{{ scope.row.standardManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratory\" width=\"90\">\r\n        <template slot=\"header\">\r\n          实验室\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的实验室分支\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{\r\n            scope.row.laboratory === '0' ? '宜侬' :\r\n            scope.row.laboratory === '1' ? '瀛彩' :\r\n            ''\r\n          }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"categoryName\" width=\"70\">\r\n        <template slot=\"header\">\r\n          品类\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              产品所属的分类类别\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"SKU数\" align=\"center\" prop=\"sku\" width=\"70\" />\r\n      <el-table-column align=\"center\" prop=\"applicant\" width=\"90\">\r\n        <template slot=\"header\">\r\n          申请人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              提交此打样申请的人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"sales\" width=\"70\">\r\n        <template slot=\"header\">\r\n          销售\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此项目的销售人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"customerName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          客户名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              委托打样的客户公司名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"产品/项目等级\" align=\"center\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getProjectLevel(scope.row) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"productName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          产品名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              需要打样的产品名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工作时间\" align=\"center\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div v-if=\"scope.row.actualStartTime\">开始: {{ parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <div v-if=\"scope.row.actualFinishTime\">完成: {{ parseTime(scope.row.actualFinishTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <span v-if=\"!scope.row.actualStartTime && !scope.row.actualFinishTime\">-</span>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工时\" align=\"center\" prop=\"actualManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.actualManHours\">{{ scope.row.actualManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预估工时\" align=\"center\" prop=\"estimatedManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.estimatedManHours\">{{ scope.row.estimatedManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"服务模式\" align=\"center\" prop=\"serviceMode\" width=\"150\" />\r\n      <el-table-column label=\"计算类型\" align=\"center\" prop=\"checkType\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.checkType == 1\">客户等级</span>\r\n          <span v-else>打样单难度</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"完成质量\" align=\"center\" prop=\"qualityEvaluation\" />\r\n<!--      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"140\">-->\r\n<!--        <template slot-scope=\"scope\">-->\r\n<!--          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n      <el-table-column label=\"打样单备注\" align=\"center\" prop=\"sampleOrderRemark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.sampleOrderRemark\" placement=\"top\" :disabled=\"!scope.row.sampleOrderRemark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.sampleOrderRemark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.remark\" placement=\"top\" :disabled=\"!scope.row.remark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.remark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"未出样原因\" align=\"center\" prop=\"reasonForNoSample\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.reasonForNoSample\" placement=\"top\" :disabled=\"!scope.row.reasonForNoSample\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.reasonForNoSample || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"解决方案\" align=\"center\" prop=\"solution\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.solution\" placement=\"top\" :disabled=\"!scope.row.solution\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.solution || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"驳回原因\" align=\"center\" prop=\"rejectReason\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.rejectReason\" placement=\"top\" :disabled=\"!scope.row.rejectReason\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.rejectReason || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"fixed-width\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- <el-tooltip content=\"编辑\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:edit']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"删除\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:remove']\" />\r\n          </el-tooltip> -->\r\n          <el-tooltip  v-if=\"scope.row.completionStatus==0\" content=\"更换工程师或更改日期\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-user\" v-if=\"scope.row.isLocked === 0\" @click=\"handleChangeEngineer(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\" />\r\n          </el-tooltip>\r\n\r\n\r\n          <el-tooltip content=\"批次管理\" v-if=\"[0,1,2].includes(scope.row.completionStatus)\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-collection\" @click=\"handleBatchManagement(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:batchManagement']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"逾期操作\" v-if=\"getOverdueInfo(scope.row).isOverdue\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-warning\" @click=\"handleOverdueOperation(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:overdueOperation']\" style=\"color: #E6A23C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"驳回\" v-if=\"scope.row.completionStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-close\" @click=\"handleReject(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" style=\"color: #F56C6C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"更新实验室编码\" v-if=\"scope.row.completionStatus == '2' && scope.row.itemStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit-outline\" @click=\"handleUpdateLaboratoryCode(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:editSampleOrderCode']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"下载打样单\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-download\" @click=\"handleDownloadSampleOrder(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:export']\" />\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      :auto-scroll=\"false\" @pagination=\"getList\" />\r\n\r\n    <!-- 添加或修改工程师打样单关联对话框 -->\r\n    <!-- <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"650px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程师ID\" prop=\"userId\">\r\n              <el-input v-model=\"form.userId\" placeholder=\"请输入工程师ID\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"打样单编号\" prop=\"sampleOrderCode\">\r\n              <el-input v-model=\"form.sampleOrderCode\" placeholder=\"请输入打样单编号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"难度\" prop=\"difficultyLevelId\">\r\n              <el-select v-model=\"form.difficultyLevelId\" placeholder=\"请选择打样难度等级\">\r\n                  <el-option\r\n                    v-for=\"dict in dylbOptions\"\r\n                    :key=\"dict.dictValue\"\r\n                    :label=\"dict.dictLabel\"\r\n                    :value=\"dict.dictValue\"\r\n                  ></el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n              <el-select v-model=\"form.completionStatus\" placeholder=\"请选择完成情况\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in statusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n              <el-date-picker\r\n                v-model=\"form.scheduledDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择排单日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"最晚截至日期\" prop=\"endDate\">\r\n              <el-date-picker\r\n                v-model=\"form.endDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择最晚截至日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"锁定状态\" prop=\"isLocked\">\r\n              <el-switch\r\n                v-model=\"form.isLocked\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n                active-text=\"已锁定\"\r\n                inactive-text=\"未锁定\"\r\n                @change=\"handleLockChange\">\r\n              </el-switch>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualStartTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择开始时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualFinishTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择完成时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际工时\" prop=\"actualManHours\">\r\n              <el-input-number v-model=\"form.actualManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入实际工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"预估工时\" prop=\"estimatedManHours\">\r\n              <el-input-number v-model=\"form.estimatedManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入预估工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"完成质量\" prop=\"qualityEvaluation\">\r\n              <el-input v-model=\"form.qualityEvaluation\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入完成质量情况\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"打样单备注\" prop=\"sampleOrderRemark\">\r\n              <el-input v-model=\"form.sampleOrderRemark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入打样单备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n\r\n    <!-- 新增打样单对话框 -->\r\n    <el-dialog title=\"新增打样单\" :visible.sync=\"addSampleOrderOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"addSampleOrderForm\" :model=\"addSampleOrderForm\" :rules=\"addSampleOrderRules\" label-width=\"100px\">\r\n        <el-form-item label=\"实验室\" prop=\"labNo\">\r\n          <el-select v-model=\"addSampleOrderForm.labNo\" placeholder=\"请选择实验室\" style=\"width: 100%\">\r\n            <el-option label=\"宜侬\" value=\"0\" />\r\n            <el-option label=\"瀛彩\" value=\"1\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"类别\" prop=\"categoryText\">\r\n          <el-select v-model=\"addSampleOrderForm.categoryText\" filterable placeholder=\"请选择类别\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in sckxxpgCplbs\"\r\n              :key=\"dict.dictValue\"\r\n              :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"难度\" prop=\"dylb\">\r\n          <el-select v-model=\"addSampleOrderForm.dylb\" placeholder=\"请选择难度\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in dylbOptions\"\r\n              :key=\"dict.dictValue\"\r\n              :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"addSampleOrderForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAddSampleOrder\" :loading=\"addSampleOrderLoading\">确 定</el-button>\r\n        <el-button @click=\"cancelAddSampleOrder\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 更改工程师对话框 -->\r\n    <el-dialog title=\"更改工程师\" :visible.sync=\"changeEngineerOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"changeEngineerForm\" :model=\"changeEngineerForm\" :rules=\"changeEngineerRules\" label-width=\"150px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"changeEngineerForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"当前工程师\">\r\n          <el-input v-model=\"changeEngineerForm.currentEngineerName\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"选择工程师\" prop=\"newEngineerId\">\r\n          <el-radio-group v-model=\"engineerSelectType\" style=\"margin-bottom: 15px;\">\r\n            <el-radio label=\"specified\">指定部门工程师</el-radio>\r\n            <el-radio label=\"other\">其他部门工程师</el-radio>\r\n          </el-radio-group>\r\n\r\n          <!-- 指定工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'specified'\">\r\n            <el-select\r\n              v-model=\"changeEngineerForm.newEngineerId\"\r\n              placeholder=\"请选择工程师\"\r\n              style=\"width: 100%\"\r\n              filterable>\r\n              <el-option\r\n                v-for=\"engineer in engineerOptions\"\r\n                :key=\"engineer.userId\"\r\n                :label=\"engineer.nickName\"\r\n                :value=\"engineer.userId\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n\r\n          <!-- 其他工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'other'\" class=\"other-engineer-select\">\r\n            <div class=\"select-item\" style=\"margin-top: 10px;\">\r\n              <el-select\r\n                v-model=\"changeEngineerForm.newEngineerId\"\r\n                placeholder=\"请选择工程师\"\r\n                style=\"width: 100%\"\r\n                filterable\r\n                remote>\r\n                <el-option\r\n                  v-for=\"engineer in searchedEngineers\"\r\n                  :key=\"engineer.userId\"\r\n                  :label=\"engineer.nickName\"\r\n                  :value=\"engineer.userId\"\r\n                />\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"选择指定日期\" prop=\"scheduledDate\">\r\n          <el-date-picker\r\n            v-model=\"changeEngineerForm.scheduledDate\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"重新调整工作安排\">\r\n          <el-switch\r\n            v-model=\"changeEngineerForm.adjustWorkSchedule\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            active-text=\"是\"\r\n            inactive-text=\"否\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitChangeEngineer\">确 定</el-button>\r\n        <el-button @click=\"changeEngineerOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次管理对话框 -->\r\n    <el-dialog title=\"批次管理\" :visible.sync=\"batchManagementOpen\" width=\"900px\" append-to-body @close=\"closeBatchManagement\">\r\n      <div class=\"batch-management-container\">\r\n        <!-- 状态提示信息 -->\r\n        <el-alert\r\n          v-if=\"!canEditBatch\"\r\n          title=\"当前打样单状态不允许编辑批次信息\"\r\n          type=\"warning\"\r\n          description=\"只有状态为'进行中'的打样单才可以编辑和添加测试批次内容，其他状态只能查看批次信息。\"\r\n          show-icon\r\n          :closable=\"false\"\r\n          style=\"margin-bottom: 20px;\">\r\n        </el-alert>\r\n\r\n        <!-- 进行中批次信息 -->\r\n        <el-card class=\"active-batches-card\" v-if=\"activeBatches && activeBatches.length > 0\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>进行中批次 ({{ activeBatches.length }}个)</span>\r\n            <el-button\r\n              v-if=\"canEditBatch\"\r\n              style=\"float: right; padding: 3px 0\"\r\n              type=\"text\"\r\n              @click=\"finishAllActiveBatches\">\r\n              结束所有批次\r\n            </el-button>\r\n            <el-tag\r\n              v-else\r\n              style=\"float: right;\"\r\n              type=\"info\"\r\n              size=\"mini\">\r\n              只读模式\r\n            </el-tag>\r\n          </div>\r\n          <!-- 进行中批次列表 -->\r\n          <el-table :data=\"activeBatches\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"batchIndex\" label=\"批次序号\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"primary\">第{{ scope.row.batchIndex }}批次</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"已用时长\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ calculateDuration(scope.row.startTime) }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"实验室编号\" width=\"200\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"experiment-codes\">\r\n                  <el-tag\r\n                    v-for=\"experiment in scope.row.experiments || []\"\r\n                    :key=\"experiment.id\"\r\n                    size=\"mini\"\r\n                    closable\r\n                    @close=\"removeExperiment(scope.row, experiment)\"\r\n                    style=\"margin: 2px;\"\r\n                  >\r\n                    {{ experiment.experimentCode }}\r\n                  </el-tag>\r\n                  <el-button\r\n                    v-if=\"canEditBatch\"\r\n                    type=\"text\"\r\n                    size=\"mini\"\r\n                    @click=\"showAddExperimentDialog(scope.row)\"\r\n                    icon=\"el-icon-plus\"\r\n                  >\r\n                    添加编号\r\n                  </el-button>\r\n                  <span v-if=\"(!scope.row.experiments || scope.row.experiments.length === 0) && !canEditBatch\" style=\"color: #C0C4CC;\">未设置</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"状态\" width=\"80\" align=\"center\">\r\n              <template>\r\n                <el-tag type=\"success\" size=\"mini\">进行中</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"150\" align=\"center\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"canEditBatch\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"finishSingleBatch(scope.row)\">\r\n                  结束批次\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"viewBatchDetail(scope.row)\" icon=\"el-icon-view\">\r\n                  详情\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n        </el-card>\r\n\r\n        <!-- 开始新批次按钮 -->\r\n        <div class=\"new-batch-section\" style=\"text-align: center; margin: 20px 0;\">\r\n          <el-button\r\n            v-if=\"canEditBatch\"\r\n            type=\"primary\"\r\n            size=\"medium\"\r\n            @click=\"startNewBatch\">\r\n            开始新批次\r\n          </el-button>\r\n          <div v-else style=\"text-align: center; color: #909399;\">\r\n            <i class=\"el-icon-info\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"margin-top: 16px;\">当前状态不允许开始新批次</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 所有批次列表 -->\r\n        <el-card class=\"all-batches-card\" style=\"margin-top: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>所有批次 ({{ allBatches.length }}个)</span>\r\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshBatchData\">刷新</el-button>\r\n          </div>\r\n          <el-table :data=\"allBatches\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"batchIndex\" label=\"批次序号\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"primary\">第{{ scope.row.batchIndex }}批次</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.endTime\">{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n                <span v-else style=\"color: #909399;\">未结束</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"batchStatus\" label=\"状态\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag :type=\"getBatchStatusType(scope.row.batchStatus)\" size=\"mini\">\r\n                  {{ getBatchStatusText(scope.row.batchStatus) }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"laboratoryCode\" label=\"实验室编号\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.laboratoryCode\" style=\"color: #606266;\">{{ scope.row.laboratoryCode }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">未设置</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"actualManHours\" label=\"实际工时\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" :type=\"scope.row.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ scope.row.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"qualityEvaluation\" label=\"质量评价\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag v-if=\"scope.row.qualityEvaluation\" size=\"mini\"\r\n                        :type=\"getQualityEvaluationType(scope.row.qualityEvaluation)\">\r\n                  {{ scope.row.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else style=\"color: #909399;\">未评价</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remark\" label=\"批次备注\" min-width=\"150\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.remark\" style=\"color: #606266;\">{{ scope.row.remark }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"canEditBatch && scope.row.batchStatus === 0\"\r\n                  size=\"mini\"\r\n                  type=\"success\"\r\n                  @click=\"startSingleBatch(scope.row)\">\r\n                  开始\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"canEditBatch && scope.row.batchStatus === 1\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"finishSingleBatch(scope.row)\">\r\n                  结束\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"viewBatchDetail(scope.row)\" icon=\"el-icon-view\">\r\n                  详情\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 空状态 -->\r\n          <div v-if=\"!allBatches || allBatches.length === 0\" class=\"empty-state\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-document\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">暂无批次记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button\r\n          v-if=\"currentBatchRow && currentBatchRow.completionStatus == '0'\"\r\n          type=\"success\"\r\n          icon=\"el-icon-video-play\"\r\n          @click=\"handleStartFromBatch\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:startRask']\">\r\n          开始任务\r\n        </el-button>\r\n        <el-button\r\n          v-if=\"currentBatchRow && currentBatchRow.completionStatus == '1'\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-check\"\r\n          @click=\"handleFinishFromBatch\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:endRask']\">\r\n          完成任务\r\n        </el-button>\r\n        <el-button @click=\"batchManagementOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次详情对话框 -->\r\n    <el-dialog title=\"批次详情\" :visible.sync=\"batchDetailOpen\" width=\"800px\" append-to-body @close=\"closeBatchDetail\">\r\n      <div v-if=\"batchDetailData\" class=\"batch-detail-container\">\r\n        <!-- 批次基本信息 -->\r\n        <el-card class=\"batch-info-card\" style=\"margin-bottom: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>批次基本信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>批次序号：</label>\r\n                <el-tag type=\"primary\">第{{ batchDetailData.batchIndex }}批次</el-tag>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>开始时间：</label>\r\n                <span class=\"info-value\">{{ parseTime(batchDetailData.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>结束时间：</label>\r\n                <span class=\"info-value\">\r\n                  {{ batchDetailData.endTime ? parseTime(batchDetailData.endTime, '{y}-{m}-{d} {h}:{i}:{s}') : '未结束' }}\r\n                </span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>实际工时：</label>\r\n                <el-tag :type=\"batchDetailData.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ batchDetailData.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>质量评价：</label>\r\n                <el-tag v-if=\"batchDetailData.qualityEvaluation\"\r\n                        :type=\"getQualityEvaluationType(batchDetailData.qualityEvaluation)\">\r\n                  {{ batchDetailData.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else class=\"info-value\">未评价</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row style=\"margin-top: 15px;\">\r\n            <el-col :span=\"24\">\r\n              <div class=\"info-item\">\r\n                <label>批次备注：</label>\r\n                <div class=\"remark-content\">\r\n                  {{ batchDetailData.remark || '无备注' }}\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n\r\n        <!-- 实验记录详情 -->\r\n        <el-card class=\"experiments-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>实验记录详情</span>\r\n            <el-tag size=\"mini\" style=\"margin-left: 10px;\">\r\n              共{{ batchDetailData.experiments ? batchDetailData.experiments.length : 0 }}条记录\r\n            </el-tag>\r\n          </div>\r\n          <el-table :data=\"batchDetailData.experiments\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"experimentCode\" label=\"实验编号\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span style=\"color: #409EFF; font-weight: bold;\">{{ scope.row.experimentCode }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"experimentNote\" label=\"实验备注\" min-width=\"200\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.experimentNote\">{{ scope.row.experimentNote }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"info\">{{ scope.row.createBy || '系统' }}</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 实验记录空状态 -->\r\n          <div v-if=\"!batchDetailData.experiments || batchDetailData.experiments.length === 0\"\r\n               class=\"empty-experiments\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-data-line\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">该批次暂无实验记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"batchDetailOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加实验记录对话框 -->\r\n    <el-dialog title=\"添加实验室编号\" :visible.sync=\"addExperimentOpen\" width=\"500px\" append-to-body @close=\"closeAddExperiment\">\r\n      <el-form :model=\"addExperimentForm\" ref=\"addExperimentForm\" label-width=\"100px\" :rules=\"addExperimentRules\">\r\n        <el-form-item label=\"实验编号\" prop=\"experimentCode\">\r\n          <el-input v-model=\"addExperimentForm.experimentCode\" placeholder=\"请输入实验编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"实验备注\">\r\n          <el-input v-model=\"addExperimentForm.experimentNote\" type=\"textarea\" placeholder=\"请输入实验备注\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"addExperimentForm.remark\" type=\"textarea\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAddExperiment\">确 定</el-button>\r\n        <el-button @click=\"addExperimentOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 结束批次对话框 -->\r\n    <el-dialog :title=\"finishBatchMode === 'single' ? `结束第${currentFinishBatch && currentFinishBatch.batchIndex || ''}批次` : '结束当前批次'\" :visible.sync=\"finishBatchOpen\" width=\"500px\" append-to-body @close=\"closeFinishBatch\">\r\n      <el-form :model=\"finishBatchForm\" ref=\"finishBatchForm\" label-width=\"100px\">\r\n        <el-form-item label=\"质量评价\">\r\n          <el-input v-model=\"finishBatchForm.qualityEvaluation\" type=\"textarea\" placeholder=\"请输入质量评价\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishBatchForm.remark\" type=\"textarea\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFinishBatch\">确 定</el-button>\r\n        <el-button @click=\"finishBatchOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 完成任务对话框 -->\r\n    <el-dialog title=\"完成任务\" :visible.sync=\"finishTaskOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"finishTaskForm\" :model=\"finishTaskForm\" :rules=\"finishTaskRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-select v-model=\"finishTaskForm.laboratoryCode\" multiple filterable placeholder=\"请选择实验室编码\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"item in experimentCodeList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.experimentCode\"\r\n              :value=\"item.experimentCode\">\r\n              <span style=\"float: left\">{{ item.experimentCode }}</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 13px\" v-if=\"item.experimentNote\">{{ item.experimentNote }}</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishTaskForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"finishTaskOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmFinishTask\" :loading=\"finishTaskLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 更新实验室编码对话框 -->\r\n    <el-dialog title=\"更新实验室编码\" :visible.sync=\"updateLaboratoryCodeOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"updateLaboratoryCodeForm\" :model=\"updateLaboratoryCodeForm\" :rules=\"updateLaboratoryCodeRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.laboratoryCode\" placeholder=\"请输入实验室编码(多个使用,分割)\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"updateLaboratoryCodeOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmUpdateLaboratoryCode\" :loading=\"updateLaboratoryCodeLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导出选择对话框 -->\r\n    <el-dialog title=\"选择导出内容\" :visible.sync=\"exportDialogOpen\" width=\"400px\" append-to-body>\r\n      <div class=\"export-options\">\r\n        <p style=\"margin-bottom: 20px; color: #606266;\">请选择要导出的打样单类型：</p>\r\n        <el-checkbox-group v-model=\"exportOptions\" style=\"display: flex; flex-direction: column;\">\r\n          <el-checkbox label=\"assigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">已分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出已分配给工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n          <el-checkbox label=\"unassigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">未分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出尚未分配工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n        </el-checkbox-group>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"exportDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmExport\" :disabled=\"exportOptions.length === 0\" :loading=\"exportLoading\">\r\n          确认导出\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 驳回对话框 -->\r\n    <el-dialog title=\"驳回打样单\" :visible.sync=\"rejectDialogOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" :rules=\"rejectRules\" label-width=\"100px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"rejectForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"驳回理由\" prop=\"rejectReason\">\r\n          <el-input v-model=\"rejectForm.rejectReason\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入驳回理由\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"rejectDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmReject\" :loading=\"rejectLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 逾期操作对话框 -->\r\n    <el-dialog title=\"逾期操作\" :visible.sync=\"overdueOperationOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"overdueOperationForm\" :model=\"overdueOperationForm\" label-width=\"120px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"overdueOperationForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"预计出样时间\">\r\n          <el-date-picker\r\n            v-model=\"overdueOperationForm.expectedSampleTime\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"未出样原因\">\r\n          <el-input v-model=\"overdueOperationForm.reasonForNoSample\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入未出样原因（非必填）\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"解决方案\">\r\n          <el-input v-model=\"overdueOperationForm.solution\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入解决方案（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"overdueOperationOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOverdueOperation\" :loading=\"overdueOperationLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n    <!-- 添加或修改项目流程进行对话框 -->\r\n    <executionAddOrEdit ref=\"executionAddOrEdit\"></executionAddOrEdit>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listEngineerSampleOrder,\r\n  getEngineerSampleOrder,\r\n  delEngineerSampleOrder,\r\n  addEngineerSampleOrder,\r\n  updateEngineerSampleOrder,\r\n  updateSampleOrderStatus,\r\n  getDashboardStats,\r\n  getResearchDepartments,\r\n  getEngineersByDifficultyLevel,\r\n  changeEngineer,\r\n  getResearchDepartmentsUser,\r\n  getBatchesByOrderId,\r\n  startNewBatch,\r\n  addExperimentToBatch,\r\n  getExperimentsByBatchId,\r\n  exportEngineerSampleOrder,\r\n  getExecutionByOrderInfo,\r\n  getBatchExperimentCodeList,\r\n  getActiveBatches,\r\n  startSingleBatch,\r\n  finishSingleBatch,\r\n  finishAllActiveBatches,\r\n  removeExperimentFromBatch\r\n} from \"@/api/software/engineerSampleOrder\";\r\nimport {exportNrwItem, exportMultipleNrw} from \"@/api/project/projectItemOrder\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\nimport {customerBaseAll} from \"@/api/customer/customer\";\r\nimport {isNull} from \"@/utils/validate\";\r\nimport executionAddOrEdit from \"@/components/Project/components/executionAddOrEdit.vue\";\r\n\r\nexport default {\r\n  name: \"EngineerSampleOrder\",\r\n  components: {\r\n     Treeselect,executionAddOrEdit\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中的完整行数据\r\n      selectedRows: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 工程师打样单关联表格数据\r\n      engineerSampleOrderList: [],\r\n      // 研发部部门树列表\r\n      researchDeptDatas:[],\r\n      // 打样类别字典\r\n      dylbOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userId: null,\r\n        nickName: null,\r\n        sampleOrderCode: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        startDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        deptId: null,\r\n        deptIds: [],\r\n        associationStatus: null,\r\n        customerId: null,\r\n        productName: null,\r\n        confirmCode: null,\r\n        isOverdue: null,  // 增逾期任务过滤参数\r\n        laboratory: null  // 实验室筛选参数\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        userId: [\r\n          { required: true, message: \"工程师ID不能为空\", trigger: \"blur\" }\r\n        ],\r\n        sampleOrderCode: [\r\n          { required: true, message: \"打样单编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        completionStatus: [\r\n          { required: true, message: \"完成情况不能为空\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      statusOptions: [],\r\n      serviceModeOptions: [],\r\n      dateRange: [], // 新增的日期范围筛选\r\n      scheduledDateRange: [],\r\n      startDateRange: [],\r\n      actualStartTimeRange: [],\r\n      actualFinishTimeRange: [],\r\n      // 日期选择器配置\r\n      dataPickerOptions: {\r\n        shortcuts: [{\r\n          text: '今天',\r\n          onClick(picker) {\r\n            const today = new Date();\r\n            picker.$emit('pick', [today, today]);\r\n          }\r\n        }, {\r\n          text: '昨天',\r\n          onClick(picker) {\r\n            const yesterday = new Date();\r\n            yesterday.setTime(yesterday.getTime() - 3600 * 1000 * 24);\r\n            picker.$emit('pick', [yesterday, yesterday]);\r\n          }\r\n        }, {\r\n          text: '最近一周',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近一个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }]\r\n      },\r\n      // 状态更新对话框\r\n      statusOpen: false,\r\n      dashboardStats: {\r\n        \"total\": 0,\r\n        \"completed\": 0,\r\n        \"inProgress\": 0,\r\n        \"overdue\": 0\r\n      },\r\n      // 更改工程师对话框\r\n      changeEngineerOpen: false,\r\n      // 更改工程师表单\r\n      changeEngineerForm: {\r\n        id: null,\r\n        sampleOrderCode: null,\r\n        currentEngineerName: null,\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      },\r\n      // 更改工程师表单校验\r\n      changeEngineerRules: {\r\n        newEngineerId: [\r\n          { required: true, message: \"请选择工程师\", trigger: \"change\" }\r\n        ],\r\n        scheduledDate: [\r\n          { required: true, message: \"请选择日期\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 工程师选项\r\n      engineerOptions: [],\r\n      // 批次管理相关\r\n      batchManagementOpen: false,\r\n      activeBatches: [], // 进行中的批次列表\r\n      allBatches: [], // 所有批次列表\r\n      selectedOrderForBatch: null,\r\n      currentBatchRow: null, // 当前批次管理的行数据\r\n      // 批次详情对话框\r\n      batchDetailOpen: false,\r\n      batchDetailData: null,\r\n      // 结束批次对话框\r\n      finishBatchOpen: false,\r\n      finishBatchForm: {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      },\r\n      currentFinishBatch: null, // 当前要结束的批次（单个批次时使用）\r\n      finishBatchMode: 'all', // 'all' 表示结束所有批次，'single' 表示结束单个批次\r\n      // 添加实验记录对话框\r\n      addExperimentOpen: false,\r\n      addExperimentForm: {\r\n        experimentCode: '',\r\n        experimentNote: '',\r\n        remark: ''\r\n      },\r\n      addExperimentRules: {\r\n        experimentCode: [\r\n          { required: true, message: '实验编号不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      currentBatchForExperiment: null,\r\n      // 未来日期选择器配置\r\n      futureDatePickerOptions: {\r\n        disabledDate(time) {\r\n          return time.getTime() < Date.now() - 8.64e7; // 禁用今天之前的日期\r\n        }\r\n      },\r\n      engineerSelectType: 'specified',\r\n      searchedEngineers: [],\r\n      // 未分配打样单告警\r\n      unassignedOrders: [],\r\n      unassignedQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 8\r\n      },\r\n      unassignedTotal: 0,\r\n      // 未分配面板折叠状态\r\n      isUnassignedPanelCollapsed: true,\r\n      // 完成任务对话框\r\n      finishTaskOpen: false,\r\n      finishTaskLoading: false,\r\n      finishTaskForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      experimentCodeList: [], // 实验室编码列表\r\n      finishTaskRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentFinishRow: null,\r\n      // 更新实验室编码对话框\r\n      updateLaboratoryCodeOpen: false,\r\n      updateLaboratoryCodeLoading: false,\r\n      updateLaboratoryCodeForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      updateLaboratoryCodeRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentUpdateLaboratoryCodeRow: null,\r\n      // 导出选择对话框\r\n      exportDialogOpen: false,\r\n      exportOptions: [],\r\n      exportLoading: false,\r\n      readonly: true,\r\n      // 项目详情相关\r\n      currentProjectType: null,\r\n      confirmItemCodes: [],\r\n      customerOptions: [],\r\n      itemNames: [],\r\n      // 驳回对话框\r\n      rejectDialogOpen: false,\r\n      rejectLoading: false,\r\n      rejectForm: {\r\n        sampleOrderCode: '',\r\n        rejectReason: ''\r\n      },\r\n      // 逾期操作对话框\r\n      overdueOperationOpen: false,\r\n      overdueOperationLoading: false,\r\n      overdueOperationForm: {\r\n        sampleOrderCode: '',\r\n        expectedSampleTime: '',\r\n        reasonForNoSample: '',\r\n        solution: ''\r\n      },\r\n      currentOverdueRow: null,\r\n      rejectRules: {\r\n        rejectReason: [\r\n          { required: true, message: \"驳回理由不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentRejectRow: null\r\n    };\r\n  },\r\n  computed: {\r\n    /** 计算是否可以编辑批次 - 只有状态为'1'(进行中)的打样单才可以编辑 */\r\n    canEditBatch() {\r\n      return this.selectedOrderForBatch && this.selectedOrderForBatch.completionStatus === 1;\r\n    }\r\n  },\r\n  created() {\r\n    // 设置默认日期范围为当天\r\n    const today = new Date();\r\n    const todayStr = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');\r\n    this.dateRange = [todayStr, todayStr];\r\n\r\n    this.getList();\r\n    customerBaseAll().then(res=> this.customerOptions = res)\r\n\r\n    this.getDicts(\"DYD_GCSZT\").then(response => {\r\n        this.statusOptions = response.data;\r\n    });\r\n    this.getDicts(\"CUSTOMER_SERVICE_MODE\").then(response => {\r\n      this.serviceModeOptions = response.data;\r\n    });\r\n    this.getDicts(\"project_nrw_dylb\").then(response => {\r\n      this.dylbOptions = response.data;\r\n    });\r\n    this.loadDashboardStats();\r\n    // 获取研发部部门列表\r\n    getResearchDepartments().then(response => {\r\n      this.researchDeptDatas = this.handleTree(response.data, \"deptId\");\r\n    });\r\n    // 获取未分配打样单\r\n    this.handleUnassignedOrders();\r\n  },\r\n  methods: {\r\n    /** 计算产品/项目等级 */\r\n    getProjectLevel(row) {\r\n      const customerLevel = row.customerLevel || '';\r\n      const projectLevel = row.projectLevel || '';\r\n\r\n      // 如果 projectLevel 是空字符串或 \"/\" 就不相加\r\n      if (projectLevel === '' || projectLevel === '/') {\r\n        return customerLevel;\r\n      }\r\n\r\n      return customerLevel + projectLevel;\r\n    },\r\n    /** 查询工程师打样单关联列表 */\r\n    getList() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.loading = true;\r\n      params.associationStatus = 1\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.engineerSampleOrderList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        userId: null,\r\n        sampleOrderCode: null,\r\n        serviceMode: null,\r\n        difficultyLevelId: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        actualManHours: null,\r\n        estimatedManHours: null,\r\n        standardManHours: null,\r\n        qualityEvaluation: null,\r\n        isLocked: 0,\r\n        startDate: null,\r\n        endDate: null,\r\n        checkType: null,\r\n        sampleOrderRemark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.unassignedQueryParams.pageNum = 1;\r\n      this.getList();\r\n      this.loadDashboardStats();\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.dateRange = [] // 重置日期范围\r\n      this.scheduledDateRange = []\r\n      this.startDateRange = []\r\n      this.actualStartTimeRange = []\r\n      this.actualFinishTimeRange = []\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.selectedRows = selection\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加工程师打样单关联\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getEngineerSampleOrder(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改工程师打样单关联\";addEngineerSampleOrder\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          } else {\r\n            addEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除工程师打样单关联编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function () {\r\n        return delEngineerSampleOrder(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.msgSuccess(\"删除成功\");\r\n        this.loadDashboardStats();\r\n      }).catch(() => { });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 重置导出选项并显示选择对话框\r\n      this.exportOptions = [];\r\n      this.exportDialogOpen = true;\r\n    },\r\n    /** 确认导出操作 */\r\n    confirmExport() {\r\n      if (this.exportOptions.length === 0) {\r\n        this.$message.warning('请至少选择一种导出类型');\r\n        return;\r\n      }\r\n      this.exportLoading = true;\r\n      this.executeExports(0);\r\n    },\r\n\r\n    /** 串行执行导出操作 */\r\n    executeExports(index) {\r\n      if (index >= this.exportOptions.length) {\r\n        // 所有导出完成\r\n        this.exportLoading = false;\r\n        this.exportDialogOpen = false;\r\n        this.$message.success(`导出完成，共导出${this.exportOptions.length}个文件`);\r\n        return;\r\n      }\r\n\r\n      const option = this.exportOptions[index];\r\n      const associationStatus = option === 'assigned' ? 1 : 0;\r\n      const typeName = option === 'assigned' ? '已分配' : '未分配';\r\n\r\n      this.doExport(associationStatus, option).then(() => {\r\n        this.$message.success(`${typeName}打样单导出成功`);\r\n        // 继续导出下一个\r\n        this.executeExports(index + 1);\r\n      }).catch((error) => {\r\n        this.exportLoading = false;\r\n        this.$message.error(`${typeName}打样单导出失败: ${error.message || error}`);\r\n      });\r\n    },\r\n    /** 执行导出操作 */\r\n    doExport(associationStatus, exportType) {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = associationStatus;\r\n      params.exportType = exportType;\r\n      return exportEngineerSampleOrder(params).then(response => {\r\n        this.download(response.msg);\r\n      });\r\n    },\r\n    /** 执行导出打样单（已分配/未分配） */\r\n    doExportSampleOrder() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.$confirm('是否确认导出所有（已分配/未分配）打样单关联数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportEngineerSampleOrder(params);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"开始\"按钮操作 */\r\n    handleStart(row) {\r\n      this.$confirm('确定要将打样单：' + row.sampleOrderCode + '标记为进行中吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        updateSampleOrderStatus({\r\n          id: row.id,\r\n          status: '1'\r\n        }).then(response => {\r\n          this.msgSuccess('打样单已设置为已开始');\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      });\r\n    },\r\n    /** 下载打样单操作 */\r\n    handleDownloadSampleOrder(row) {\r\n      this.$confirm('是否确认导出打样单?', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportNrwItem({itemId: row.itemId,projectId: row.projectId})\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n      }).catch(() => {});\r\n    },\r\n    /** 批量导出打样单任务操作 */\r\n    handleBatchExportNrw() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$modal.msgError(\"请选择要导出的打样单任务\");\r\n        return;\r\n      }\r\n\r\n      // 构造批量导出数据，传递itemId和projectId\r\n      const exportData = this.selectedRows.map(row => ({\r\n        itemId: row.itemId,\r\n        projectId: row.projectId\r\n      }));\r\n\r\n      this.$confirm('是否确认导出所选打样单任务的内容物数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportMultipleNrw(exportData);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"完成\"按钮操作 */\r\n    handleFinish(row) {\r\n      this.currentFinishRow = row;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      // 加载实验室编码列表\r\n      this.loadExperimentCodeList(row.id);\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认完成任务 */\r\n    confirmFinishTask() {\r\n      this.$refs.finishTaskForm.validate(valid => {\r\n        // 处理实验室编号字段\r\n        let laboratoryCode = this.finishTaskForm.laboratoryCode;\r\n\r\n        // 如果是数组类型，使用逗号拼接\r\n        if (Array.isArray(laboratoryCode)) {\r\n          laboratoryCode = laboratoryCode.join(\",\");\r\n        }\r\n        if (valid) {\r\n          this.finishTaskLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentFinishRow.id,\r\n            status: '2',\r\n            laboratoryCode: laboratoryCode,\r\n            remark: this.finishTaskForm.remark\r\n          }).then(response => {\r\n            this.finishTaskLoading = false;\r\n            this.finishTaskOpen = false;\r\n            this.msgSuccess('打样单已设置为已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.finishTaskLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 从批次管理对话框完成任务 */\r\n    handleFinishFromBatch() {\r\n      this.currentFinishRow = this.currentBatchRow;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      // 加载实验室编码列表\r\n      this.loadExperimentCodeList(this.currentBatchRow.id);\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 从批次管理对话框开始任务 */\r\n    handleStartFromBatch() {\r\n      const row = this.currentBatchRow;\r\n      this.$confirm('确定要将打样单：' + row.sampleOrderCode + '标记为进行中吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        updateSampleOrderStatus({\r\n          id: row.id,\r\n          status: '1'\r\n        }).then(response => {\r\n          this.msgSuccess('打样单已设置为已开始');\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n          // 更新当前批次行数据的状态\r\n          this.currentBatchRow.completionStatus = '1';\r\n          this.selectedOrderForBatch.completionStatus = 1;\r\n          if (this.selectedOrderForBatch) {\r\n            this.loadBatchData(this.selectedOrderForBatch.id);\r\n          }\r\n        });\r\n      });\r\n    },\r\n    /** 点击\"更新实验室编码\"按钮操作 */\r\n    handleUpdateLaboratoryCode(row) {\r\n      this.currentUpdateLaboratoryCodeRow = row;\r\n      // 回显当前的实验室编码和备注\r\n      this.updateLaboratoryCodeForm.laboratoryCode = row.laboratoryCode || '';\r\n      this.updateLaboratoryCodeForm.remark = row.remark || '';\r\n      this.updateLaboratoryCodeOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.updateLaboratoryCodeForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认更新实验室编码 */\r\n    confirmUpdateLaboratoryCode() {\r\n      this.$refs.updateLaboratoryCodeForm.validate(valid => {\r\n        if (valid) {\r\n          this.updateLaboratoryCodeLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentUpdateLaboratoryCodeRow.id,\r\n            status: this.currentUpdateLaboratoryCodeRow.completionStatus, // 保持当前状态\r\n            laboratoryCode: this.updateLaboratoryCodeForm.laboratoryCode,\r\n            remark: this.updateLaboratoryCodeForm.remark\r\n          }).then(response => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n            this.updateLaboratoryCodeOpen = false;\r\n            this.msgSuccess('实验室编码更新成功');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"驳回\"按钮操作 */\r\n    handleReject(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认驳回 */\r\n    confirmReject() {\r\n      this.$refs.rejectForm.validate(valid => {\r\n        if (valid) {\r\n          this.rejectLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentRejectRow.id,\r\n            status: '3',\r\n            rejectReason: this.rejectForm.rejectReason\r\n          }).then(response => {\r\n            this.rejectLoading = false;\r\n            this.rejectDialogOpen = false;\r\n            this.msgSuccess('打样单已驳回');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n            // 如果是未分配打样单的驳回，也需要刷新未分配列表\r\n            this.handleUnassignedOrders();\r\n          }).catch(() => {\r\n            this.rejectLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"逾期操作\"按钮操作 */\r\n    handleOverdueOperation(row) {\r\n      this.currentOverdueRow = row;\r\n      this.overdueOperationForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.overdueOperationForm.expectedSampleTime = row.expectedSampleTime;\r\n      this.overdueOperationForm.reasonForNoSample = row.reasonForNoSample;\r\n      this.overdueOperationForm.solution = row.solution;\r\n      this.overdueOperationOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.overdueOperationForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认逾期操作 */\r\n    confirmOverdueOperation() {\r\n      this.$refs.overdueOperationForm.validate(valid => {\r\n        if (valid) {\r\n          this.overdueOperationLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentOverdueRow.id,\r\n            status: \"11\", // 特殊处理，只更新“逾期情况”填写的字段\r\n            expectedSampleTime: this.overdueOperationForm.expectedSampleTime,\r\n            reasonForNoSample: this.overdueOperationForm.reasonForNoSample,\r\n            solution: this.overdueOperationForm.solution\r\n          }).then(response => {\r\n            this.overdueOperationLoading = false;\r\n            this.overdueOperationOpen = false;\r\n            this.msgSuccess('逾期操作已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.overdueOperationLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 查看打样单详情 */\r\n    async orderDetail(row) {\r\n      try {\r\n        //获取执行ID\r\n        const orderId = row.projectOrderId;\r\n        let data = await getExecutionByOrderInfo(orderId);\r\n        if(data!=null && !isNull(data.id)){\r\n            let id = data.id;\r\n            this.$refs.executionAddOrEdit.open = true;\r\n            await this.$nextTick()\r\n            this.$refs.executionAddOrEdit.reset()\r\n            this.$refs.executionAddOrEdit.show(id, 1)\r\n        }else{\r\n          this.$message.error('获取数据失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('查看项目详情失败:', error);\r\n        this.$message.error('查看项目详情失败: ' + (error.message || '未知错误'));\r\n      }\r\n    },\r\n\r\n    /** 锁定状态改变处理 */\r\n    handleLockChange(value) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.form.isLocked = 1;\r\n        }).catch(() => {\r\n          this.form.isLocked = 0;\r\n        });\r\n      }\r\n    },\r\n    /** 表格中锁定状态改变处理 */\r\n    handleTableLockChange(value, row) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          // 调用更新接口\r\n          updateEngineerSampleOrder({\r\n            id: row.id,\r\n            isLocked: 1\r\n          }).then(response => {\r\n            this.msgSuccess(\"锁定成功\");\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          });\r\n        }).catch(() => {\r\n          // 取消操作，恢复原值\r\n          row.isLocked = 0;\r\n        });\r\n      } else {\r\n        // 解锁操作\r\n        updateEngineerSampleOrder({\r\n          id: row.id,\r\n          isLocked: 0\r\n        }).then(response => {\r\n          this.msgSuccess(\"解锁成功\");\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      }\r\n    },\r\n    /** 加载统计概览数据 */\r\n    loadDashboardStats() {\r\n      let params = { ...this.queryParams };\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      getDashboardStats(params).then(response => {\r\n        this.dashboardStats = response.data || {};\r\n      });\r\n    },\r\n    /** 更改工程师按钮操作 */\r\n    handleChangeEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: row.nickName,\r\n        oldEngineerId: row.userId,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 提交更改工程师 */\r\n    submitChangeEngineer() {\r\n      this.$refs[\"changeEngineerForm\"].validate(valid => {\r\n        if (valid) {\r\n          const data = {\r\n            sampleOrderId: this.changeEngineerForm.id,\r\n            oldEngineerId: this.changeEngineerForm.oldEngineerId,\r\n            newEngineerId: this.changeEngineerForm.newEngineerId,\r\n            scheduledDate: this.changeEngineerForm.scheduledDate,\r\n            adjustWorkSchedule: this.changeEngineerForm.adjustWorkSchedule\r\n          };\r\n\r\n          // 调用更改工程师的API接口\r\n          changeEngineer(data).then(response => {\r\n            this.msgSuccess(\"更改工程师成功\");\r\n            this.changeEngineerOpen = false;\r\n            this.getList();\r\n            // 获取未分配打样单\r\n            this.handleUnassignedOrders();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.msgError(\"更改工程师失败\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 获取未分配打样单列表 */\r\n    handleUnassignedOrders() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = 0\r\n      params.pageNum = this.unassignedQueryParams.pageNum;\r\n      params.pageSize = this.unassignedQueryParams.pageSize;\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.unassignedOrders = response.rows;\r\n        this.unassignedTotal = response.total;\r\n      });\r\n    },\r\n    /** 分配工程师操作 */\r\n    handleAssignEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: '',\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 删除未分配打样单 */\r\n    handleDeleteUnassigned(row) {\r\n      this.$confirm('是否确认删除打样单编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function() {\r\n        return delEngineerSampleOrder(row.id);\r\n      }).then(() => {\r\n        this.handleUnassignedOrders();\r\n        this.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 驳回未分配打样单 */\r\n    handleRejectUnassigned(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 未分配打样单分页大小改变 */\r\n    handleUnassignedSizeChange(newSize) {\r\n      this.unassignedQueryParams.pageSize = newSize;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 未分配打样单页码改变 */\r\n    handleUnassignedCurrentChange(newPage) {\r\n      this.unassignedQueryParams.pageNum = newPage;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 切换未分配面板显示状态 */\r\n    toggleUnassignedPanel() {\r\n      this.isUnassignedPanelCollapsed = !this.isUnassignedPanelCollapsed;\r\n    },\r\n    /** 处理状态过滤 */\r\n    handleStatusFilter(status) {\r\n      // 清除逾期过滤\r\n      this.queryParams.isOverdue = null;\r\n      this.queryParams.completionStatus = status;\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 处理逾期任务过滤 */\r\n    handleOverdueFilter() {\r\n      // 清除完成状态过滤\r\n      this.queryParams.completionStatus = null;\r\n      // 设置逾期过滤\r\n      this.queryParams.isOverdue = this.queryParams.isOverdue === 1 ? null : 1;\r\n      this.handleQuery();\r\n\r\n      if (this.queryParams.isOverdue === 1) {\r\n        this.$message.info('已筛选显示逾期任务');\r\n      } else {\r\n        this.$message.info('已清除逾期任务筛选');\r\n      }\r\n    },\r\n    /** 获取紧急程度类型 */\r\n    getUrgencyType(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return 'danger'; // 已超过最晚开始时间\r\n      }\r\n\r\n      if (daysToEnd <= 1) {\r\n        return 'danger'; // 紧急：1天内截止\r\n      } else if (daysToEnd <= 3) {\r\n        return 'warning'; // 警告：3天内截止\r\n      } else if (daysToEnd <= 7) {\r\n        return 'primary'; // 一般：7天内截止\r\n      } else {\r\n        return 'success'; // 充足时间\r\n      }\r\n    },\r\n    /** 获取紧急程度文本 */\r\n    getUrgencyText(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return '超期未开始';\r\n      }\r\n\r\n      if (daysToEnd <= 0) {\r\n        return '已逾期';\r\n      } else if (daysToEnd <= 1) {\r\n        return '紧急';\r\n      } else if (daysToEnd <= 3) {\r\n        return '较急';\r\n      } else if (daysToEnd <= 7) {\r\n        return '一般';\r\n      } else {\r\n        return '充足';\r\n      }\r\n    },\r\n    /** 刷新未分配打样单列表 */\r\n    handleRefreshUnassigned() {\r\n      this.handleUnassignedOrders();\r\n      this.$message.success('刷新成功');\r\n    },\r\n    /** 获取逾期信息 */\r\n    getOverdueInfo(row) {\r\n      const now = new Date();\r\n      const endDate = new Date(row.endDate);\r\n      let isOverdue = false;\r\n      let overdueDays = 0;\r\n      // 根据后台SQL逻辑判断逾期\r\n      if (row.completionStatus === 2) {\r\n        // 已完成且逾期：实际完成时间 > 截止日期\r\n        if (row.actualFinishTime) {\r\n          const actualFinishTime = new Date(row.actualFinishTime);\r\n          if (actualFinishTime > endDate) {\r\n            isOverdue = true;\r\n            overdueDays = Math.ceil((actualFinishTime - endDate) / (1000 * 60 * 60 * 24));\r\n          }\r\n        }\r\n      } else if (row.completionStatus === 1) {\r\n        // 进行中且逾期：当前时间 > 截止日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      } else if (row.completionStatus === 0) {\r\n        // 未开始且逾期：未开始但当前时间 > 开始日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      }\r\n\r\n      return {\r\n        isOverdue,\r\n        overdueDays\r\n      };\r\n    },\r\n\r\n    // ==================== 批次管理相关方法 ====================\r\n\r\n    /** 打开批次管理对话框 */\r\n    handleBatchManagement(row) {\r\n      this.selectedOrderForBatch = row;\r\n      this.currentBatchRow = row; // 保存当前行数据\r\n      this.batchManagementOpen = true;\r\n      this.loadBatchData(row.id);\r\n    },\r\n\r\n    /** 加载批次数据 */\r\n    async loadBatchData(engineerSampleOrderId) {\r\n      try {\r\n        // 加载进行中的批次\r\n        const activeBatchesResponse = await this.getActiveBatchesData(engineerSampleOrderId);\r\n        this.activeBatches = activeBatchesResponse.data || [];\r\n\r\n        // 为每个进行中的批次加载实验记录\r\n        for (let batch of this.activeBatches) {\r\n          await this.loadBatchExperiments(batch);\r\n        }\r\n\r\n        // 加载所有批次\r\n        const batchesResponse = await getBatchesByOrderId(engineerSampleOrderId);\r\n        this.allBatches = batchesResponse.data || [];\r\n      } catch (error) {\r\n        console.error('加载批次数据失败:', error);\r\n        this.$message.error('加载批次数据失败');\r\n      }\r\n    },\r\n\r\n    /** 加载批次的实验记录 */\r\n    async loadBatchExperiments(batch) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batch.id);\r\n        this.$set(batch, 'experiments', response.data || []);\r\n      } catch (error) {\r\n        console.error('加载批次实验记录失败:', error);\r\n        this.$set(batch, 'experiments', []);\r\n      }\r\n    },\r\n\r\n    /** 获取进行中的批次 */\r\n    async getActiveBatchesData(engineerSampleOrderId) {\r\n      try {\r\n        // 调用新的API获取进行中的批次\r\n        const response = await getActiveBatches(engineerSampleOrderId);\r\n        return response;\r\n      } catch (error) {\r\n        console.error('获取进行中批次失败:', error);\r\n        return { data: [] };\r\n      }\r\n    },\r\n\r\n    /** 开始新批次 */\r\n    async startNewBatch() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许开始新批次，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$confirm('确认开始新的打样批次？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        const response = await startNewBatch(this.selectedOrderForBatch.id, '');\r\n        this.$message.success('新批次开始成功');\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('开始新批次失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 开始单个批次 */\r\n    async startSingleBatch(batch) {\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许开始批次');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$confirm(`确认开始第${batch.batchIndex}批次？`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        await startSingleBatch(batch.id);\r\n        this.$message.success('批次开始成功');\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n        this.getList();\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('开始批次失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 结束单个批次 */\r\n    finishSingleBatch(batch) {\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许结束批次');\r\n        return;\r\n      }\r\n\r\n      // 设置为单个批次结束模式\r\n      this.finishBatchMode = 'single';\r\n      this.currentFinishBatch = batch;\r\n\r\n      // 重置表单\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 显示对话框\r\n      this.finishBatchOpen = true;\r\n    },\r\n\r\n    /** 结束所有进行中批次 */\r\n    finishAllActiveBatches() {\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许结束批次');\r\n        return;\r\n      }\r\n\r\n      // 设置为结束所有批次模式\r\n      this.finishBatchMode = 'all';\r\n      this.currentFinishBatch = null;\r\n\r\n      // 重置表单\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 显示对话框\r\n      this.finishBatchOpen = true;\r\n    },\r\n\r\n    /** 提交结束批次 */\r\n    async submitFinishBatch() {\r\n      try {\r\n        if (this.finishBatchMode === 'single') {\r\n          // 结束单个批次\r\n          await finishSingleBatch(\r\n            this.currentFinishBatch.id,\r\n            this.finishBatchForm.qualityEvaluation || '',\r\n            this.finishBatchForm.remark || '',\r\n            this.currentFinishBatch.laboratoryCode || ''\r\n          );\r\n          this.$message.success('批次结束成功');\r\n        } else {\r\n          // 结束所有进行中的批次\r\n          await finishAllActiveBatches(this.selectedOrderForBatch.id);\r\n          this.$message.success('所有批次结束成功');\r\n        }\r\n\r\n        this.finishBatchOpen = false;\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        console.error('结束批次失败:', error);\r\n        this.$message.error('结束批次失败: ' + (error.message || error));\r\n      }\r\n    },\r\n\r\n    /** 查看批次详情 */\r\n    async viewBatchDetail(batch) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batch.id);\r\n        const experiments = response.data || [];\r\n\r\n        // 设置批次详情数据\r\n        this.batchDetailData = {\r\n          ...batch,\r\n          experiments: experiments\r\n        };\r\n\r\n        this.batchDetailOpen = true;\r\n      } catch (error) {\r\n        console.error('查看批次详情失败:', error);\r\n        this.$message.error('查看批次详情失败');\r\n      }\r\n    },\r\n\r\n    /** 计算持续时间 */\r\n    calculateDuration(startTime) {\r\n      if (!startTime) return '0小时';\r\n\r\n      const start = new Date(startTime);\r\n      const now = new Date();\r\n      const diffMs = now - start;\r\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\r\n      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\r\n\r\n      if (diffHours > 0) {\r\n        return `${diffHours}小时${diffMinutes}分钟`;\r\n      } else {\r\n        return `${diffMinutes}分钟`;\r\n      }\r\n    },\r\n\r\n    /** 编辑实验室编号 */\r\n    editLaboratoryCode(batch) {\r\n      this.$set(batch, 'editingLab', true);\r\n      this.$nextTick(() => {\r\n        // 聚焦到输入框\r\n        const input = this.$el.querySelector(`input[value=\"${batch.laboratoryCode || ''}\"]`);\r\n        if (input) input.focus();\r\n      });\r\n    },\r\n\r\n    /** 显示添加实验记录对话框 */\r\n    showAddExperimentDialog(batch) {\r\n      this.currentBatchForExperiment = batch;\r\n      this.addExperimentForm = {\r\n        experimentCode: '',\r\n        experimentNote: '',\r\n        remark: ''\r\n      };\r\n      this.addExperimentOpen = true;\r\n    },\r\n\r\n    /** 关闭添加实验记录对话框 */\r\n    closeAddExperiment() {\r\n      this.addExperimentOpen = false;\r\n      this.currentBatchForExperiment = null;\r\n      this.$refs.addExperimentForm && this.$refs.addExperimentForm.resetFields();\r\n    },\r\n\r\n    /** 提交添加实验记录 */\r\n    async submitAddExperiment() {\r\n      try {\r\n        await this.$refs.addExperimentForm.validate();\r\n\r\n        const experimentRecord = {\r\n          batchId: this.currentBatchForExperiment.id,\r\n          experimentCode: this.addExperimentForm.experimentCode,\r\n          experimentNote: this.addExperimentForm.experimentNote || '',\r\n          remark: this.addExperimentForm.remark\r\n        };\r\n\r\n        await addExperimentToBatch(experimentRecord);\r\n\r\n        this.$message.success('实验记录添加成功');\r\n        this.addExperimentOpen = false;\r\n\r\n        // 重新加载当前批次的实验记录\r\n        await this.loadBatchExperiments(this.currentBatchForExperiment);\r\n      } catch (error) {\r\n        this.$message.error('添加实验记录失败: ' + (error.message || error));\r\n      }\r\n    },\r\n\r\n    /** 删除实验记录 */\r\n    async removeExperiment(batch, experiment) {\r\n      try {\r\n        await this.$confirm('确认删除该实验记录吗？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        await removeExperimentFromBatch(experiment.id);\r\n        this.$message.success('实验记录删除成功');\r\n\r\n        // 重新加载当前批次的实验记录\r\n        await this.loadBatchExperiments(batch);\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除实验记录失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 获取批次状态文本 */\r\n    getBatchStatusText(status) {\r\n      const statusMap = {\r\n        0: '未开始',\r\n        1: '进行中',\r\n        2: '已完成',\r\n        3: '已取消'\r\n      };\r\n      return statusMap[status] || '未知';\r\n    },\r\n\r\n    /** 获取批次状态类型 */\r\n    getBatchStatusType(status) {\r\n      const typeMap = {\r\n        0: 'info',\r\n        1: 'success',\r\n        2: 'primary',\r\n        3: 'danger'\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    /** 获取质量评价类型 */\r\n    getQualityEvaluationType(evaluation) {\r\n      if (!evaluation) return '';\r\n\r\n      const lowerEval = evaluation.toLowerCase();\r\n      if (lowerEval.includes('优秀') || lowerEval.includes('良好') || lowerEval.includes('好')) {\r\n        return 'success';\r\n      } else if (lowerEval.includes('一般') || lowerEval.includes('中等')) {\r\n        return 'warning';\r\n      } else if (lowerEval.includes('差') || lowerEval.includes('不合格') || lowerEval.includes('失败')) {\r\n        return 'danger';\r\n      } else {\r\n        return 'info';\r\n      }\r\n    },\r\n\r\n    /** 关闭批次管理对话框并清空数据 */\r\n    closeBatchManagement() {\r\n      // 清空批次管理相关的数据\r\n      this.activeBatches = [];\r\n      this.allBatches = [];\r\n      this.selectedOrderForBatch = null;\r\n      this.currentBatchRow = null; // 清空当前行数据\r\n      this.batchDetailData = null;\r\n\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 关闭所有相关的子对话框\r\n      this.batchDetailOpen = false;\r\n      this.addExperimentOpen = false;\r\n      this.finishBatchOpen = false;\r\n    },\r\n\r\n    /** 关闭批次详情对话框并清空数据 */\r\n    closeBatchDetail() {\r\n      // 清空批次详情数据\r\n      this.batchDetailData = null;\r\n    },\r\n\r\n    /** 关闭结束批次对话框并清空数据 */\r\n    closeFinishBatch() {\r\n      // 清空结束批次表单数据\r\n      this.finishBatchOpen = false;\r\n      this.finishBatchMode = 'all';\r\n      this.currentFinishBatch = null;\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n      // 清除表单验证状态\r\n      if (this.$refs.finishBatchForm) {\r\n        this.$refs.finishBatchForm.clearValidate();\r\n      }\r\n    },\r\n\r\n    /** 刷新批次数据 */\r\n    async refreshBatchData() {\r\n      if (this.selectedOrderForBatch) {\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n        this.$message.success('批次数据已刷新');\r\n      }\r\n    },\r\n\r\n    /** 加载实验室编码列表 */\r\n    async loadExperimentCodeList(engineerSampleOrderId) {\r\n      try {\r\n        const response = await getBatchExperimentCodeList(engineerSampleOrderId);\r\n        this.experimentCodeList = response.data || [];\r\n      } catch (error) {\r\n        console.error('加载实验室编码列表失败:', error);\r\n        this.experimentCodeList = [];\r\n        this.$message.error('加载实验室编码列表失败');\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.stats-row {\r\n  margin-bottom: 20px;\r\n}\r\n.stats-card {\r\n  border: none;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* 批次详情样式 */\r\n.batch-detail-container {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.batch-info-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-value {\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.remark-content {\r\n  background-color: #F5F7FA;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409EFF;\r\n  color: #606266;\r\n  line-height: 1.5;\r\n  margin-top: 8px;\r\n  min-height: 40px;\r\n}\r\n\r\n.experiments-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.empty-state, .empty-experiments {\r\n  background-color: #FAFAFA;\r\n  border-radius: 8px;\r\n  margin: 10px 0;\r\n}\r\n\r\n/* 历史批次表格样式优化 */\r\n.history-batches-card .el-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-batches-card .el-table th {\r\n  background-color: #F5F7FA;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.history-batches-card .el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n.stats-card:not(.total) {\r\n  cursor: pointer;\r\n}\r\n\r\n.stats-card:not(.total):hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stats-card.active {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  position: relative;\r\n}\r\n\r\n.stats-card.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 3px;\r\n  background: currentColor;\r\n}\r\n\r\n.stats-card.completed.active::after {\r\n  background: #67C23A;\r\n}\r\n\r\n.stats-card.in-progress.active::after {\r\n  background: #409EFF;\r\n}\r\n\r\n.stats-card.overdue.active::after {\r\n  background: #F56C6C;\r\n}\r\n\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n\r\n.stats-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n.stats-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n}\r\n.stats-icon i {\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n.stats-info {\r\n  flex: 1;\r\n}\r\n.stats-title {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-bottom: 8px;\r\n}\r\n.stats-number {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n.stats-card.total .stats-icon {\r\n  background: linear-gradient(135deg, #3a7bd5, #3a6073);\r\n}\r\n.stats-card.completed .stats-icon {\r\n  background: linear-gradient(135deg, #E6A23C, #F0C78A);\r\n}\r\n.stats-card.in-progress .stats-icon {\r\n  background: linear-gradient(135deg, #409EFF, #66B1FF);\r\n}\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n@media (max-width: 768px) {\r\n  .stats-card .stats-content {\r\n    padding: 15px;\r\n  }\r\n  .stats-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n    margin-right: 15px;\r\n  }\r\n  .stats-icon i {\r\n    font-size: 20px;\r\n  }\r\n  .stats-number {\r\n    font-size: 24px;\r\n  }\r\n}\r\n.other-engineer-select {\r\n  margin-top: 10px;\r\n}\r\n.select-item {\r\n  width: 100%;\r\n}\r\n\r\n/* 未分配打样单告警样式 */\r\n.alert-card {\r\n  margin-bottom: 20px;\r\n}\r\n.alert-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.alert-title {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n.alert-title span {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #FF1414;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-cards {\r\n  margin-top: 20px;\r\n}\r\n.alert-item {\r\n  margin-bottom: 20px;\r\n  transition: all 0.3s;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n  height: 225px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n.alert-item:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);\r\n  border-color: #409EFF;\r\n}\r\n.alert-item-header {\r\n  margin-bottom: 12px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #f0f2f5;\r\n  background: rgba(64, 158, 255, 0.02);\r\n  margin: -16px -16px 12px -16px;\r\n  padding: 12px 16px;\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n.order-code-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.order-code {\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n.urgency-tag {\r\n  margin-left: 8px;\r\n}\r\n.alert-item-content {\r\n  padding: 0;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  min-height: 0;\r\n}\r\n.info-section {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n}\r\n.info-row {\r\n  margin-bottom: 8px;\r\n}\r\n.info-row-double {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 12px;\r\n}\r\n.info-row-double .info-item {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-row-double .info-item.date-time-item {\r\n  flex: 1.5;\r\n}\r\n.info-row-double .info-item.standard-item {\r\n  flex: 1;\r\n}\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n.info-label {\r\n  margin-left: 6px;\r\n  margin-right: 4px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n.info-value {\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n.difficulty-text {\r\n  cursor: pointer;\r\n  border-bottom: 1px dashed #ccc;\r\n}\r\n.info-reason {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  color: #f56c6c;\r\n  margin-top: 8px;\r\n  padding: 6px 8px;\r\n  background-color: #fef0f0;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #f56c6c;\r\n  font-size: 12px;\r\n}\r\n.reason-text {\r\n  margin-left: 4px;\r\n  line-height: 1.4;\r\n  word-break: break-word;\r\n}\r\n.text-overflow {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-reason span {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.info-item i,\r\n.info-date i,\r\n.info-reason i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n.alert-item-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: auto;\r\n  padding: 12px 0 0 0;\r\n  border-top: 1px solid #f0f2f5;\r\n  flex-shrink: 0;\r\n  background: rgba(250, 251, 252, 0.5);\r\n  border-radius: 0 0 6px 6px;\r\n  margin-left: -16px;\r\n  margin-right: -16px;\r\n  padding-left: 16px;\r\n  padding-right: 16px;\r\n}\r\n.alert-item-footer .el-button {\r\n  border-radius: 4px;\r\n  font-weight: 500;\r\n}\r\n.alert-item-footer .el-button--primary {\r\n  background: #409EFF;\r\n  border-color: #409EFF;\r\n  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);\r\n}\r\n.alert-item-footer .el-button--primary:hover {\r\n  background: #66b1ff;\r\n  border-color: #66b1ff;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);\r\n}\r\n.alert-item-footer .el-button--text {\r\n  color: #909399;\r\n  transition: all 0.3s;\r\n}\r\n.alert-item-footer .el-button--text:hover {\r\n  color: #f56c6c;\r\n  background: rgba(245, 108, 108, 0.1);\r\n}\r\n.delete-btn:hover {\r\n  color: #f56c6c !important;\r\n}\r\n\r\n/* 确保卡片内容区域的统一布局 */\r\n.alert-item .el-card__body {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 16px;\r\n  position: relative;\r\n}\r\n\r\n/* 确保内容区域占据剩余空间 */\r\n.alert-item .alert-item-header {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.alert-item .alert-item-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n}\r\n\r\n.alert-item .alert-item-footer {\r\n  flex-shrink: 0;\r\n  margin-top: auto;\r\n}\r\n\r\n.alert-item .info-group {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.alert-item .info-date {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .alert-item {\r\n    height: auto;\r\n    min-height: 200px;\r\n  }\r\n\r\n  .info-row-double {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .alert-item-footer {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .alert-item-footer .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 批次管理相关样式 */\r\n.batch-management-container {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.current-batch-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-info-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.batch-info-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.experiment-section {\r\n  border-top: 1px solid #ebeef5;\r\n  padding-top: 15px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.section-header span {\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.history-batches-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.new-batch-section {\r\n  padding: 40px 0;\r\n  text-align: center;\r\n  background-color: #fafafa;\r\n  border: 2px dashed #dcdfe6;\r\n  border-radius: 6px;\r\n}\r\n\r\n.new-batch-section .el-button {\r\n  padding: 12px 30px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .batch-management-container {\r\n    max-height: 500px;\r\n  }\r\n\r\n  .batch-info-item {\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .section-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .section-header .el-button {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n\r\n/* 备注文本省略样式 */\r\n.remark-text-ellipsis {\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  line-clamp: 2;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  line-height: 1.4;\r\n  max-height: 2.8em; /* 2行的高度，基于line-height */\r\n  word-break: break-word;\r\n  white-space: normal;\r\n  cursor: pointer;\r\n}\r\n\r\n.remark-text-ellipsis:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 导出选择对话框样式 */\r\n.export-options {\r\n  padding: 10px 0;\r\n}\r\n\r\n.export-options .el-checkbox {\r\n  width: 100%;\r\n  margin-right: 0;\r\n  padding: 15px;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.export-options .el-checkbox:hover {\r\n  border-color: #409eff;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.export-options .el-checkbox.is-checked {\r\n  border-color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.export-options .el-checkbox__label {\r\n  width: 100%;\r\n  padding-left: 10px;\r\n}\r\n\r\n/* 实验记录相关样式 */\r\n.experiment-codes {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 4px;\r\n  min-height: 32px;\r\n}\r\n\r\n.experiment-codes .el-tag {\r\n  margin: 2px;\r\n  max-width: 120px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.experiment-codes .el-button {\r\n  margin: 2px;\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAg9CA,IAAAA,oBAAA,GAAAC,OAAA;AAyBA,IAAAC,iBAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAC,sBAAA,CAAAH,OAAA;AACAA,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,mBAAA,GAAAH,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,sBAAA;IAAAC,kBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,YAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,uBAAA;MACA;MACAC,iBAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,aAAA;QACAC,SAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,MAAA;QACAC,OAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,WAAA;QACAC,WAAA;QACAC,SAAA;QAAA;QACAC,UAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAjB,MAAA,GACA;UAAAkB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,eAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjB,gBAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,aAAA;MACAC,kBAAA;MACAC,SAAA;MAAA;MACAC,kBAAA;MACAC,cAAA;MACAC,oBAAA;MACAC,qBAAA;MACA;MACAC,iBAAA;QACAC,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAC,KAAA,OAAAC,IAAA;YACAF,MAAA,CAAAG,KAAA,UAAAF,KAAA,EAAAA,KAAA;UACA;QACA;UACAH,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAI,SAAA,OAAAF,IAAA;YACAE,SAAA,CAAAC,OAAA,CAAAD,SAAA,CAAAE,OAAA;YACAN,MAAA,CAAAG,KAAA,UAAAC,SAAA,EAAAA,SAAA;UACA;QACA;UACAN,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAO,GAAA,OAAAL,IAAA;YACA,IAAAM,KAAA,OAAAN,IAAA;YACAM,KAAA,CAAAH,OAAA,CAAAG,KAAA,CAAAF,OAAA;YACAN,MAAA,CAAAG,KAAA,UAAAK,KAAA,EAAAD,GAAA;UACA;QACA;UACAT,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAO,GAAA,OAAAL,IAAA;YACA,IAAAM,KAAA,OAAAN,IAAA;YACAM,KAAA,CAAAH,OAAA,CAAAG,KAAA,CAAAF,OAAA;YACAN,MAAA,CAAAG,KAAA,UAAAK,KAAA,EAAAD,GAAA;UACA;QACA;MACA;MACA;MACAE,UAAA;MACAC,cAAA;QACA;QACA;QACA;QACA;MACA;MACA;MACAC,kBAAA;MACA;MACAC,kBAAA;QACAC,EAAA;QACA3C,eAAA;QACA4C,mBAAA;QACAC,aAAA;QACAC,aAAA;QACA5C,aAAA;QACA6C,kBAAA;MACA;MACA;MACAC,mBAAA;QACAF,aAAA,GACA;UAAA9B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,aAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACA+B,eAAA;MACA;MACAC,mBAAA;MACAC,aAAA;MAAA;MACAC,UAAA;MAAA;MACAC,qBAAA;MACAC,eAAA;MAAA;MACA;MACAC,eAAA;MACAC,eAAA;MACA;MACAC,eAAA;MACAC,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;MACAC,kBAAA;MAAA;MACAC,eAAA;MAAA;MACA;MACAC,iBAAA;MACAC,iBAAA;QACAC,cAAA;QACAC,cAAA;QACAN,MAAA;MACA;MACAO,kBAAA;QACAF,cAAA,GACA;UAAAjD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAkD,yBAAA;MACA;MACAC,uBAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAnC,OAAA,KAAAJ,IAAA,CAAAwC,GAAA;QACA;MACA;MACAC,kBAAA;MACAC,iBAAA;MACA;MACAC,gBAAA;MACAC,qBAAA;QACAhF,OAAA;QACAC,QAAA;MACA;MACAgF,eAAA;MACA;MACAC,0BAAA;MACA;MACAC,cAAA;MACAC,iBAAA;MACAC,cAAA;QACAC,cAAA;QACAtB,MAAA;MACA;MACAuB,kBAAA;MAAA;MACAC,eAAA;QACAF,cAAA,GACA;UAAAlE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAmE,gBAAA;MACA;MACAC,wBAAA;MACAC,2BAAA;MACAC,wBAAA;QACAN,cAAA;QACAtB,MAAA;MACA;MACA6B,yBAAA;QACAP,cAAA,GACA;UAAAlE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAwE,8BAAA;MACA;MACAC,gBAAA;MACAC,aAAA;MACAC,aAAA;MACAC,QAAA;MACA;MACAC,kBAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,SAAA;MACA;MACAC,gBAAA;MACAC,aAAA;MACAC,UAAA;QACArG,eAAA;QACAsG,YAAA;MACA;MACA;MACAC,oBAAA;MACAC,uBAAA;MACAC,oBAAA;QACAzG,eAAA;QACA0G,kBAAA;QACAC,iBAAA;QACAC,QAAA;MACA;MACAC,iBAAA;MACAC,WAAA;QACAR,YAAA,GACA;UAAAtF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA6F,gBAAA;IACA;EACA;EACAC,QAAA;IACA,0CACAC,YAAA,WAAAA,aAAA;MACA,YAAA5D,qBAAA,SAAAA,qBAAA,CAAApD,gBAAA;IACA;EACA;EACAiH,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,IAAApF,KAAA,OAAAC,IAAA;IACA,IAAAoF,QAAA,GAAArF,KAAA,CAAAsF,WAAA,WAAAC,MAAA,CAAAvF,KAAA,CAAAwF,QAAA,QAAAC,QAAA,iBAAAF,MAAA,CAAAvF,KAAA,CAAA0F,OAAA,IAAAD,QAAA;IACA,KAAAnG,SAAA,IAAA+F,QAAA,EAAAA,QAAA;IAEA,KAAAM,OAAA;IACA,IAAAC,yBAAA,IAAAC,IAAA,WAAAC,GAAA;MAAA,OAAAV,KAAA,CAAAlB,eAAA,GAAA4B,GAAA;IAAA;IAEA,KAAAC,QAAA,cAAAF,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAAhG,aAAA,GAAA4G,QAAA,CAAAjJ,IAAA;IACA;IACA,KAAAgJ,QAAA,0BAAAF,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAA/F,kBAAA,GAAA2G,QAAA,CAAAjJ,IAAA;IACA;IACA,KAAAgJ,QAAA,qBAAAF,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAA3H,WAAA,GAAAuI,QAAA,CAAAjJ,IAAA;IACA;IACA,KAAAkJ,kBAAA;IACA;IACA,IAAAC,2CAAA,IAAAL,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAA5H,iBAAA,GAAA4H,KAAA,CAAAe,UAAA,CAAAH,QAAA,CAAAjJ,IAAA;IACA;IACA;IACA,KAAAqJ,sBAAA;EACA;EACAC,OAAA;IACA,gBACAC,eAAA,WAAAA,gBAAAC,GAAA;MACA,IAAAC,aAAA,GAAAD,GAAA,CAAAC,aAAA;MACA,IAAAC,YAAA,GAAAF,GAAA,CAAAE,YAAA;;MAEA;MACA,IAAAA,YAAA,WAAAA,YAAA;QACA,OAAAD,aAAA;MACA;MAEA,OAAAA,aAAA,GAAAC,YAAA;IACA;IACA,mBACAd,OAAA,WAAAA,QAAA;MAAA,IAAAe,MAAA;MACA,IAAAC,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAjJ,WAAA;MACA+I,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAApH,kBAAA;MACAoH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAnH,cAAA;MACAmH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAlH,oBAAA;MACAkH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAjH,qBAAA;MACA;MACA,OAAAiH,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAA1H,SAAA,SAAAA,SAAA,CAAA2H,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAzH,SAAA;QACAqH,MAAA,CAAAK,YAAA,QAAA1H,SAAA;MACA;MACA,KAAAtC,OAAA;MACA2J,MAAA,CAAAlI,iBAAA;MACA,IAAAyI,4CAAA,EAAAP,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAU,MAAA,CAAAnJ,uBAAA,GAAAyI,QAAA,CAAAmB,IAAA;QACAT,MAAA,CAAApJ,KAAA,GAAA0I,QAAA,CAAA1I,KAAA;QACAoJ,MAAA,CAAA1J,OAAA;MACA;IACA;IACA;IACAoK,MAAA,WAAAA,OAAA;MACA,KAAAzJ,IAAA;MACA,KAAA0J,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAtI,IAAA;QACA6B,EAAA;QACA7C,MAAA;QACAE,eAAA;QACAqJ,WAAA;QACAC,iBAAA;QACArJ,gBAAA;QACAC,aAAA;QACAE,eAAA;QACAC,gBAAA;QACAkJ,cAAA;QACAC,iBAAA;QACAC,gBAAA;QACA9F,iBAAA;QACA+F,QAAA;QACAvJ,SAAA;QACAwJ,OAAA;QACAC,SAAA;QACAC,iBAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACArG,MAAA;MACA;MACA,KAAAsG,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAxK,WAAA,CAAAC,OAAA;MACA,KAAAgF,qBAAA,CAAAhF,OAAA;MACA,KAAA8H,OAAA;MACA,KAAAM,kBAAA;MACA,KAAAG,sBAAA;IACA;IACA,aACAiC,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAA7I,SAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,cAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,qBAAA;MACA,KAAA0I,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAtL,GAAA,GAAAsL,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA7H,EAAA;MAAA;MACA,KAAA1D,YAAA,GAAAqL,SAAA;MACA,KAAApL,MAAA,GAAAoL,SAAA,CAAAtB,MAAA;MACA,KAAA7J,QAAA,IAAAmL,SAAA,CAAAtB,MAAA;IACA;IACA,aACAyB,SAAA,WAAAA,UAAA;MACA,KAAArB,KAAA;MACA,KAAA1J,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAiL,YAAA,WAAAA,aAAApC,GAAA;MAAA,IAAAqC,MAAA;MACA,KAAAvB,KAAA;MACA,IAAAzG,EAAA,GAAA2F,GAAA,CAAA3F,EAAA,SAAA3D,GAAA;MACA,IAAA4L,2CAAA,EAAAjI,EAAA,EAAAiF,IAAA,WAAAG,QAAA;QACA4C,MAAA,CAAA7J,IAAA,GAAAiH,QAAA,CAAAjJ,IAAA;QACA6L,MAAA,CAAAjL,IAAA;QACAiL,MAAA,CAAAlL,KAAA;QAAAoL,2CAAA;MACA;IACA;IACA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAjK,IAAA,CAAA6B,EAAA;YACA,IAAAwI,8CAAA,EAAAJ,MAAA,CAAAjK,IAAA,EAAA8G,IAAA,WAAAG,QAAA;cACAgD,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAArL,IAAA;cACAqL,MAAA,CAAArD,OAAA;cACAqD,MAAA,CAAA/C,kBAAA;YACA;UACA;YACA,IAAA6C,2CAAA,EAAAE,MAAA,CAAAjK,IAAA,EAAA8G,IAAA,WAAAG,QAAA;cACAgD,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAArL,IAAA;cACAqL,MAAA,CAAArD,OAAA;cACAqD,MAAA,CAAA/C,kBAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAqD,YAAA,WAAAA,aAAA/C,GAAA;MAAA,IAAAgD,MAAA;MACA,IAAAtM,GAAA,GAAAsJ,GAAA,CAAA3F,EAAA,SAAA3D,GAAA;MACA,KAAAuM,QAAA,wBAAAjD,GAAA,CAAAtI,eAAA,aAAA4H,IAAA;QACA,WAAA4D,2CAAA,EAAAxM,GAAA;MACA,GAAA4I,IAAA;QACA0D,MAAA,CAAA5D,OAAA;QACA4D,MAAA,CAAAF,UAAA;QACAE,MAAA,CAAAtD,kBAAA;MACA,GAAAyD,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA;MACA,KAAA9F,aAAA;MACA,KAAAD,gBAAA;IACA;IACA,aACAgG,aAAA,WAAAA,cAAA;MACA,SAAA/F,aAAA,CAAAoD,MAAA;QACA,KAAA4C,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAhG,aAAA;MACA,KAAAiG,cAAA;IACA;IAEA,eACAA,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,KAAA,SAAAnG,aAAA,CAAAoD,MAAA;QACA;QACA,KAAAnD,aAAA;QACA,KAAAF,gBAAA;QACA,KAAAiG,QAAA,CAAAK,OAAA,oDAAAC,MAAA,MAAAtG,aAAA,CAAAoD,MAAA;QACA;MACA;MAEA,IAAAmD,MAAA,QAAAvG,aAAA,CAAAmG,KAAA;MACA,IAAAvL,iBAAA,GAAA2L,MAAA;MACA,IAAAC,QAAA,GAAAD,MAAA;MAEA,KAAAE,QAAA,CAAA7L,iBAAA,EAAA2L,MAAA,EAAAvE,IAAA;QACAoE,MAAA,CAAAJ,QAAA,CAAAK,OAAA,IAAAC,MAAA,CAAAE,QAAA;QACA;QACAJ,MAAA,CAAAF,cAAA,CAAAC,KAAA;MACA,GAAAN,KAAA,WAAAa,KAAA;QACAN,MAAA,CAAAnG,aAAA;QACAmG,MAAA,CAAAJ,QAAA,CAAAU,KAAA,IAAAJ,MAAA,CAAAE,QAAA,kDAAAF,MAAA,CAAAI,KAAA,CAAArL,OAAA,IAAAqL,KAAA;MACA;IACA;IACA,aACAD,QAAA,WAAAA,SAAA7L,iBAAA,EAAA+L,UAAA;MAAA,IAAAC,MAAA;MACA,IAAA9D,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAjJ,WAAA;MACA+I,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAApH,kBAAA;MACAoH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAnH,cAAA;MACAmH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAlH,oBAAA;MACAkH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAjH,qBAAA;MACA;MACA,OAAAiH,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAA1H,SAAA,SAAAA,SAAA,CAAA2H,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAzH,SAAA;QACAqH,MAAA,CAAAK,YAAA,QAAA1H,SAAA;MACA;MACAqH,MAAA,CAAAlI,iBAAA,GAAAA,iBAAA;MACAkI,MAAA,CAAA6D,UAAA,GAAAA,UAAA;MACA,WAAAE,8CAAA,EAAA/D,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAyE,MAAA,CAAAE,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;MACA;IACA;IACA,uBACAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,IAAAnE,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAjJ,WAAA;MACA+I,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAApH,kBAAA;MACAoH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAnH,cAAA;MACAmH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAlH,oBAAA;MACAkH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAjH,qBAAA;MACA;MACA,OAAAiH,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAA1H,SAAA,SAAAA,SAAA,CAAA2H,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAzH,SAAA;QACAqH,MAAA,CAAAK,YAAA,QAAA1H,SAAA;MACA;MACA,KAAAkK,QAAA;QACAuB,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACAiF,MAAA,CAAAhH,aAAA;QACA,WAAA4G,8CAAA,EAAA/D,MAAA;MACA,GAAAd,IAAA,WAAAG,QAAA;QACA8E,MAAA,CAAAH,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;QACAE,MAAA,CAAAhH,aAAA;QACAgH,MAAA,CAAAjB,QAAA,CAAAK,OAAA;MACA,GAAAR,KAAA;QACAoB,MAAA,CAAAhH,aAAA;MACA;IACA;IACA,iBACAoH,WAAA,WAAAA,YAAA3E,GAAA;MAAA,IAAA4E,MAAA;MACA,KAAA3B,QAAA,cAAAjD,GAAA,CAAAtI,eAAA;QACA8M,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA,IAAAuF,4CAAA;UACAxK,EAAA,EAAA2F,GAAA,CAAA3F,EAAA;UACAyK,MAAA;QACA,GAAAxF,IAAA,WAAAG,QAAA;UACAmF,MAAA,CAAA9B,UAAA;UACA8B,MAAA,CAAAxF,OAAA;UACAwF,MAAA,CAAAlF,kBAAA;QACA;MACA;IACA;IACA,cACAqF,yBAAA,WAAAA,0BAAA/E,GAAA;MAAA,IAAAgF,OAAA;MACA,KAAA/B,QAAA;QACAuB,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA0F,OAAA,CAAAzH,aAAA;QACA,WAAA0H,+BAAA;UAAAC,MAAA,EAAAlF,GAAA,CAAAkF,MAAA;UAAAC,SAAA,EAAAnF,GAAA,CAAAmF;QAAA;MACA,GAAA7F,IAAA,WAAAG,QAAA;QACAuF,OAAA,CAAAZ,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;QACAW,OAAA,CAAAzH,aAAA;MACA,GAAA4F,KAAA;IACA;IACA,kBACAiC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,SAAA1O,YAAA,CAAA+J,MAAA;QACA,KAAA4E,MAAA,CAAAC,QAAA;QACA;MACA;;MAEA;MACA,IAAAC,UAAA,QAAA7O,YAAA,CAAAsL,GAAA,WAAAjC,GAAA;QAAA;UACAkF,MAAA,EAAAlF,GAAA,CAAAkF,MAAA;UACAC,SAAA,EAAAnF,GAAA,CAAAmF;QACA;MAAA;MAEA,KAAAlC,QAAA;QACAuB,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA+F,OAAA,CAAA9H,aAAA;QACA,WAAAkI,mCAAA,EAAAD,UAAA;MACA,GAAAlG,IAAA,WAAAG,QAAA;QACA4F,OAAA,CAAAjB,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;QACAgB,OAAA,CAAA9H,aAAA;QACA8H,OAAA,CAAA/B,QAAA,CAAAK,OAAA;MACA,GAAAR,KAAA;QACAkC,OAAA,CAAA9H,aAAA;MACA;IACA;IACA,iBACAmI,YAAA,WAAAA,aAAA1F,GAAA;MAAA,IAAA2F,OAAA;MACA,KAAA5I,gBAAA,GAAAiD,GAAA;MACA,KAAArD,cAAA,CAAAC,cAAA;MACA,KAAAD,cAAA,CAAArB,MAAA;MACA,KAAAmB,cAAA;MACA;MACA,KAAAmJ,sBAAA,CAAA5F,GAAA,CAAA3F,EAAA;MACA,KAAAwL,SAAA;QACAF,OAAA,CAAAjD,KAAA,CAAA/F,cAAA,CAAAmJ,aAAA;MACA;IACA;IACA,aACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MACA,KAAAtD,KAAA,CAAA/F,cAAA,CAAAgG,QAAA,WAAAC,KAAA;QACA;QACA,IAAAhG,cAAA,GAAAoJ,OAAA,CAAArJ,cAAA,CAAAC,cAAA;;QAEA;QACA,IAAAqJ,KAAA,CAAAC,OAAA,CAAAtJ,cAAA;UACAA,cAAA,GAAAA,cAAA,CAAAuJ,IAAA;QACA;QACA,IAAAvD,KAAA;UACAoD,OAAA,CAAAtJ,iBAAA;UACA,IAAAmI,4CAAA;YACAxK,EAAA,EAAA2L,OAAA,CAAAjJ,gBAAA,CAAA1C,EAAA;YACAyK,MAAA;YACAlI,cAAA,EAAAA,cAAA;YACAtB,MAAA,EAAA0K,OAAA,CAAArJ,cAAA,CAAArB;UACA,GAAAgE,IAAA,WAAAG,QAAA;YACAuG,OAAA,CAAAtJ,iBAAA;YACAsJ,OAAA,CAAAvJ,cAAA;YACAuJ,OAAA,CAAAlD,UAAA;YACAkD,OAAA,CAAA5G,OAAA;YACA4G,OAAA,CAAAtG,kBAAA;UACA,GAAAyD,KAAA;YACA6C,OAAA,CAAAtJ,iBAAA;UACA;QACA;MACA;IACA;IACA,mBACA0J,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MACA,KAAAtJ,gBAAA,QAAA/B,eAAA;MACA,KAAA2B,cAAA,CAAAC,cAAA;MACA,KAAAD,cAAA,CAAArB,MAAA;MACA,KAAAmB,cAAA;MACA;MACA,KAAAmJ,sBAAA,MAAA5K,eAAA,CAAAX,EAAA;MACA,KAAAwL,SAAA;QACAQ,OAAA,CAAA3D,KAAA,CAAA/F,cAAA,CAAAmJ,aAAA;MACA;IACA;IACA,mBACAQ,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,IAAAvG,GAAA,QAAAhF,eAAA;MACA,KAAAiI,QAAA,cAAAjD,GAAA,CAAAtI,eAAA;QACA8M,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA,IAAAuF,4CAAA;UACAxK,EAAA,EAAA2F,GAAA,CAAA3F,EAAA;UACAyK,MAAA;QACA,GAAAxF,IAAA,WAAAG,QAAA;UACA8G,OAAA,CAAAzD,UAAA;UACAyD,OAAA,CAAAnH,OAAA;UACAmH,OAAA,CAAA7G,kBAAA;UACA;UACA6G,OAAA,CAAAvL,eAAA,CAAArD,gBAAA;UACA4O,OAAA,CAAAxL,qBAAA,CAAApD,gBAAA;UACA,IAAA4O,OAAA,CAAAxL,qBAAA;YACAwL,OAAA,CAAAC,aAAA,CAAAD,OAAA,CAAAxL,qBAAA,CAAAV,EAAA;UACA;QACA;MACA;IACA;IACA,sBACAoM,0BAAA,WAAAA,2BAAAzG,GAAA;MAAA,IAAA0G,OAAA;MACA,KAAAtJ,8BAAA,GAAA4C,GAAA;MACA;MACA,KAAA9C,wBAAA,CAAAN,cAAA,GAAAoD,GAAA,CAAApD,cAAA;MACA,KAAAM,wBAAA,CAAA5B,MAAA,GAAA0E,GAAA,CAAA1E,MAAA;MACA,KAAA0B,wBAAA;MACA,KAAA6I,SAAA;QACAa,OAAA,CAAAhE,KAAA,CAAAxF,wBAAA,CAAA4I,aAAA;MACA;IACA;IACA,gBACAa,2BAAA,WAAAA,4BAAA;MAAA,IAAAC,OAAA;MACA,KAAAlE,KAAA,CAAAxF,wBAAA,CAAAyF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAgE,OAAA,CAAA3J,2BAAA;UACA,IAAA4H,4CAAA;YACAxK,EAAA,EAAAuM,OAAA,CAAAxJ,8BAAA,CAAA/C,EAAA;YACAyK,MAAA,EAAA8B,OAAA,CAAAxJ,8BAAA,CAAAzF,gBAAA;YAAA;YACAiF,cAAA,EAAAgK,OAAA,CAAA1J,wBAAA,CAAAN,cAAA;YACAtB,MAAA,EAAAsL,OAAA,CAAA1J,wBAAA,CAAA5B;UACA,GAAAgE,IAAA,WAAAG,QAAA;YACAmH,OAAA,CAAA3J,2BAAA;YACA2J,OAAA,CAAA5J,wBAAA;YACA4J,OAAA,CAAA9D,UAAA;YACA8D,OAAA,CAAAxH,OAAA;YACAwH,OAAA,CAAAlH,kBAAA;UACA,GAAAyD,KAAA;YACAyD,OAAA,CAAA3J,2BAAA;UACA;QACA;MACA;IACA;IACA,iBACA4J,YAAA,WAAAA,aAAA7G,GAAA;MAAA,IAAA8G,OAAA;MACA,KAAArI,gBAAA,GAAAuB,GAAA;MACA,KAAAjC,UAAA,CAAArG,eAAA,GAAAsI,GAAA,CAAAtI,eAAA;MACA,KAAAqG,UAAA,CAAAC,YAAA;MACA,KAAAH,gBAAA;MACA,KAAAgI,SAAA;QACAiB,OAAA,CAAApE,KAAA,CAAA3E,UAAA,CAAA+H,aAAA;MACA;IACA;IACA,WACAiB,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,KAAAtE,KAAA,CAAA3E,UAAA,CAAA4E,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAoE,OAAA,CAAAlJ,aAAA;UACA,IAAA+G,4CAAA;YACAxK,EAAA,EAAA2M,OAAA,CAAAvI,gBAAA,CAAApE,EAAA;YACAyK,MAAA;YACA9G,YAAA,EAAAgJ,OAAA,CAAAjJ,UAAA,CAAAC;UACA,GAAAsB,IAAA,WAAAG,QAAA;YACAuH,OAAA,CAAAlJ,aAAA;YACAkJ,OAAA,CAAAnJ,gBAAA;YACAmJ,OAAA,CAAAlE,UAAA;YACAkE,OAAA,CAAA5H,OAAA;YACA4H,OAAA,CAAAtH,kBAAA;YACA;YACAsH,OAAA,CAAAnH,sBAAA;UACA,GAAAsD,KAAA;YACA6D,OAAA,CAAAlJ,aAAA;UACA;QACA;MACA;IACA;IACA,mBACAmJ,sBAAA,WAAAA,uBAAAjH,GAAA;MAAA,IAAAkH,OAAA;MACA,KAAA3I,iBAAA,GAAAyB,GAAA;MACA,KAAA7B,oBAAA,CAAAzG,eAAA,GAAAsI,GAAA,CAAAtI,eAAA;MACA,KAAAyG,oBAAA,CAAAC,kBAAA,GAAA4B,GAAA,CAAA5B,kBAAA;MACA,KAAAD,oBAAA,CAAAE,iBAAA,GAAA2B,GAAA,CAAA3B,iBAAA;MACA,KAAAF,oBAAA,CAAAG,QAAA,GAAA0B,GAAA,CAAA1B,QAAA;MACA,KAAAL,oBAAA;MACA,KAAA4H,SAAA;QACAqB,OAAA,CAAAxE,KAAA,CAAAvE,oBAAA,CAAA2H,aAAA;MACA;IACA;IACA,aACAqB,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,OAAA;MACA,KAAA1E,KAAA,CAAAvE,oBAAA,CAAAwE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAwE,OAAA,CAAAlJ,uBAAA;UACA,IAAA2G,4CAAA;YACAxK,EAAA,EAAA+M,OAAA,CAAA7I,iBAAA,CAAAlE,EAAA;YACAyK,MAAA;YAAA;YACA1G,kBAAA,EAAAgJ,OAAA,CAAAjJ,oBAAA,CAAAC,kBAAA;YACAC,iBAAA,EAAA+I,OAAA,CAAAjJ,oBAAA,CAAAE,iBAAA;YACAC,QAAA,EAAA8I,OAAA,CAAAjJ,oBAAA,CAAAG;UACA,GAAAgB,IAAA,WAAAG,QAAA;YACA2H,OAAA,CAAAlJ,uBAAA;YACAkJ,OAAA,CAAAnJ,oBAAA;YACAmJ,OAAA,CAAAtE,UAAA;YACAsE,OAAA,CAAAhI,OAAA;YACAgI,OAAA,CAAA1H,kBAAA;UACA,GAAAyD,KAAA;YACAiE,OAAA,CAAAlJ,uBAAA;UACA;QACA;MACA;IACA;IACA,cACAmJ,WAAA,WAAAA,YAAArH,GAAA;MAAA,IAAAsH,OAAA;MAAA,WAAAC,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAC,QAAA;QAAA,IAAAC,OAAA,EAAAnR,IAAA,EAAA6D,EAAA;QAAA,WAAAmN,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEA;cACAJ,OAAA,GAAA3H,GAAA,CAAAiI,cAAA;cAAAH,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAE,4CAAA,EAAAP,OAAA;YAAA;cAAAnR,IAAA,GAAAsR,QAAA,CAAAK,IAAA;cAAA,MACA3R,IAAA,iBAAA4R,gBAAA,EAAA5R,IAAA,CAAA6D,EAAA;gBAAAyN,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACA3N,EAAA,GAAA7D,IAAA,CAAA6D,EAAA;cACAiN,OAAA,CAAA5E,KAAA,CAAAnM,kBAAA,CAAAa,IAAA;cAAA0Q,QAAA,CAAAE,IAAA;cAAA,OACAV,OAAA,CAAAzB,SAAA;YAAA;cACAyB,OAAA,CAAA5E,KAAA,CAAAnM,kBAAA,CAAAuK,KAAA;cACAwG,OAAA,CAAA5E,KAAA,CAAAnM,kBAAA,CAAA8R,IAAA,CAAAhO,EAAA;cAAAyN,QAAA,CAAAE,IAAA;cAAA;YAAA;cAEAV,OAAA,CAAAhE,QAAA,CAAAU,KAAA;YAAA;cAAA8D,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAGAS,OAAA,CAAAvE,KAAA,cAAA8D,QAAA,CAAAQ,EAAA;cACAhB,OAAA,CAAAhE,QAAA,CAAAU,KAAA,iBAAA8D,QAAA,CAAAQ,EAAA,CAAA3P,OAAA;YAAA;YAAA;cAAA,OAAAmP,QAAA,CAAAU,IAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IAEA;IAEA,eACAe,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,OAAA;MACA,IAAAD,KAAA;QACA,KAAAzF,QAAA;UACAuB,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAApF,IAAA;UACAqJ,OAAA,CAAAnQ,IAAA,CAAA4I,QAAA;QACA,GAAA+B,KAAA;UACAwF,OAAA,CAAAnQ,IAAA,CAAA4I,QAAA;QACA;MACA;IACA;IACA,kBACAwH,qBAAA,WAAAA,sBAAAF,KAAA,EAAA1I,GAAA;MAAA,IAAA6I,OAAA;MACA,IAAAH,KAAA;QACA,KAAAzF,QAAA;UACAuB,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAApF,IAAA;UACA;UACA,IAAAuD,8CAAA;YACAxI,EAAA,EAAA2F,GAAA,CAAA3F,EAAA;YACA+G,QAAA;UACA,GAAA9B,IAAA,WAAAG,QAAA;YACAoJ,OAAA,CAAA/F,UAAA;YACA+F,OAAA,CAAAzJ,OAAA;YACAyJ,OAAA,CAAAnJ,kBAAA;UACA;QACA,GAAAyD,KAAA;UACA;UACAnD,GAAA,CAAAoB,QAAA;QACA;MACA;QACA;QACA,IAAAyB,8CAAA;UACAxI,EAAA,EAAA2F,GAAA,CAAA3F,EAAA;UACA+G,QAAA;QACA,GAAA9B,IAAA,WAAAG,QAAA;UACAoJ,OAAA,CAAA/F,UAAA;UACA+F,OAAA,CAAAzJ,OAAA;UACAyJ,OAAA,CAAAnJ,kBAAA;QACA;MACA;IACA;IACA,eACAA,kBAAA,WAAAA,mBAAA;MAAA,IAAAoJ,OAAA;MACA,IAAA1I,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAjJ,WAAA;MACA;MACA,SAAA0B,SAAA,SAAAA,SAAA,CAAA2H,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAzH,SAAA;QACAqH,MAAA,CAAAK,YAAA,QAAA1H,SAAA;MACA;MACA,IAAAgQ,sCAAA,EAAA3I,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAqJ,OAAA,CAAA5O,cAAA,GAAAuF,QAAA,CAAAjJ,IAAA;MACA;IACA;IACA,gBACAwS,oBAAA,WAAAA,qBAAAhJ,GAAA;MAAA,IAAAiJ,OAAA;MACA,KAAA7O,kBAAA;QACAC,EAAA,EAAA2F,GAAA,CAAA3F,EAAA;QACA3C,eAAA,EAAAsI,GAAA,CAAAtI,eAAA;QACA4C,mBAAA,EAAA0F,GAAA,CAAAvI,QAAA;QACA8C,aAAA,EAAAyF,GAAA,CAAAxI,MAAA;QACAgD,aAAA;QACA5C,aAAA;QACA6C,kBAAA;MACA;;MAEA;MACA,IAAAyO,kDAAA;QACAlI,iBAAA,EAAAhB,GAAA,CAAAgB,iBAAA;QACAmI,UAAA,EAAAnJ,GAAA,CAAAmJ;MACA,GAAA7J,IAAA,WAAAG,QAAA;QACAwJ,OAAA,CAAAtO,eAAA,GAAA8E,QAAA,CAAAjJ,IAAA;MACA;MACA;MACA,IAAA4S,+CAAA,IAAA9J,IAAA,WAAAG,QAAA;QACAwJ,OAAA,CAAA7M,iBAAA,GAAAqD,QAAA,CAAAjJ,IAAA;MACA;MACA,KAAA2D,kBAAA;IACA;IACA,cACAkP,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,KAAA5G,KAAA,uBAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAApM,IAAA;YACA+S,aAAA,EAAAD,OAAA,CAAAlP,kBAAA,CAAAC,EAAA;YACAE,aAAA,EAAA+O,OAAA,CAAAlP,kBAAA,CAAAG,aAAA;YACAC,aAAA,EAAA8O,OAAA,CAAAlP,kBAAA,CAAAI,aAAA;YACA5C,aAAA,EAAA0R,OAAA,CAAAlP,kBAAA,CAAAxC,aAAA;YACA6C,kBAAA,EAAA6O,OAAA,CAAAlP,kBAAA,CAAAK;UACA;;UAEA;UACA,IAAA+O,mCAAA,EAAAhT,IAAA,EAAA8I,IAAA,WAAAG,QAAA;YACA6J,OAAA,CAAAxG,UAAA;YACAwG,OAAA,CAAAnP,kBAAA;YACAmP,OAAA,CAAAlK,OAAA;YACA;YACAkK,OAAA,CAAAzJ,sBAAA;YACAyJ,OAAA,CAAA5J,kBAAA;UACA,GAAAyD,KAAA;YACAmG,OAAA,CAAA/D,QAAA;UACA;QACA;MACA;IACA;IACA,iBACA1F,sBAAA,WAAAA,uBAAA;MAAA,IAAA4J,OAAA;MACA,IAAArJ,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAjJ,WAAA;MACA+I,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAApH,kBAAA;MACAoH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAnH,cAAA;MACAmH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAlH,oBAAA;MACAkH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAjH,qBAAA;MACA;MACA,OAAAiH,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAA1H,SAAA,SAAAA,SAAA,CAAA2H,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAzH,SAAA;QACAqH,MAAA,CAAAK,YAAA,QAAA1H,SAAA;MACA;MACAqH,MAAA,CAAAlI,iBAAA;MACAkI,MAAA,CAAA9I,OAAA,QAAAgF,qBAAA,CAAAhF,OAAA;MACA8I,MAAA,CAAA7I,QAAA,QAAA+E,qBAAA,CAAA/E,QAAA;MACA,IAAAoJ,4CAAA,EAAAP,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAgK,OAAA,CAAApN,gBAAA,GAAAoD,QAAA,CAAAmB,IAAA;QACA6I,OAAA,CAAAlN,eAAA,GAAAkD,QAAA,CAAA1I,KAAA;MACA;IACA;IACA,cACA2S,oBAAA,WAAAA,qBAAA1J,GAAA;MAAA,IAAA2J,OAAA;MACA,KAAAvP,kBAAA;QACAC,EAAA,EAAA2F,GAAA,CAAA3F,EAAA;QACA3C,eAAA,EAAAsI,GAAA,CAAAtI,eAAA;QACA4C,mBAAA;QACAC,aAAA;QACAC,aAAA;QACA5C,aAAA;QACA6C,kBAAA;MACA;;MAEA;MACA,IAAAyO,kDAAA;QACAlI,iBAAA,EAAAhB,GAAA,CAAAgB,iBAAA;QACAmI,UAAA,EAAAnJ,GAAA,CAAAmJ;MACA,GAAA7J,IAAA,WAAAG,QAAA;QACAkK,OAAA,CAAAhP,eAAA,GAAA8E,QAAA,CAAAjJ,IAAA;MACA;MACA;MACA,IAAA4S,+CAAA,IAAA9J,IAAA,WAAAG,QAAA;QACAkK,OAAA,CAAAvN,iBAAA,GAAAqD,QAAA,CAAAjJ,IAAA;MACA;MACA,KAAA2D,kBAAA;IACA;IACA,eACAyP,sBAAA,WAAAA,uBAAA5J,GAAA;MAAA,IAAA6J,OAAA;MACA,KAAA5G,QAAA,mBAAAjD,GAAA,CAAAtI,eAAA,aAAA4H,IAAA;QACA,WAAA4D,2CAAA,EAAAlD,GAAA,CAAA3F,EAAA;MACA,GAAAiF,IAAA;QACAuK,OAAA,CAAAhK,sBAAA;QACAgK,OAAA,CAAA/G,UAAA;MACA,GAAAK,KAAA;IACA;IACA,eACA2G,sBAAA,WAAAA,uBAAA9J,GAAA;MAAA,IAAA+J,OAAA;MACA,KAAAtL,gBAAA,GAAAuB,GAAA;MACA,KAAAjC,UAAA,CAAArG,eAAA,GAAAsI,GAAA,CAAAtI,eAAA;MACA,KAAAqG,UAAA,CAAAC,YAAA;MACA,KAAAH,gBAAA;MACA,KAAAgI,SAAA;QACAkE,OAAA,CAAArH,KAAA,CAAA3E,UAAA,CAAA+H,aAAA;MACA;IACA;IACA,mBACAkE,0BAAA,WAAAA,2BAAAC,OAAA;MACA,KAAA3N,qBAAA,CAAA/E,QAAA,GAAA0S,OAAA;MACA,KAAApK,sBAAA;IACA;IACA,iBACAqK,6BAAA,WAAAA,8BAAAC,OAAA;MACA,KAAA7N,qBAAA,CAAAhF,OAAA,GAAA6S,OAAA;MACA,KAAAtK,sBAAA;IACA;IACA,kBACAuK,qBAAA,WAAAA,sBAAA;MACA,KAAA5N,0BAAA,SAAAA,0BAAA;IACA;IACA,aACA6N,kBAAA,WAAAA,mBAAAvF,MAAA;MACA;MACA,KAAAzN,WAAA,CAAAiB,SAAA;MACA,KAAAjB,WAAA,CAAAM,gBAAA,GAAAmN,MAAA;MACA,KAAAjD,WAAA;IACA;IAEA,eACAyI,mBAAA,WAAAA,oBAAA;MACA;MACA,KAAAjT,WAAA,CAAAM,gBAAA;MACA;MACA,KAAAN,WAAA,CAAAiB,SAAA,QAAAjB,WAAA,CAAAiB,SAAA;MACA,KAAAuJ,WAAA;MAEA,SAAAxK,WAAA,CAAAiB,SAAA;QACA,KAAAgL,QAAA,CAAAiH,IAAA;MACA;QACA,KAAAjH,QAAA,CAAAiH,IAAA;MACA;IACA;IACA,eACAC,cAAA,WAAAA,eAAAnJ,OAAA,EAAAoJ,eAAA;MACA,IAAAvO,GAAA,OAAAxC,IAAA;MACA,IAAAK,GAAA,OAAAL,IAAA,CAAA2H,OAAA;MACA,IAAAqJ,MAAA,GAAAD,eAAA,OAAA/Q,IAAA,CAAA+Q,eAAA;;MAEA;MACA,IAAAE,SAAA,GAAAC,IAAA,CAAAC,IAAA,EAAA9Q,GAAA,GAAAmC,GAAA;;MAEA;MACA,IAAAwO,MAAA,IAAAxO,GAAA,GAAAwO,MAAA;QACA;MACA;MAEA,IAAAC,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA;QACA;MACA;IACA;IACA,eACAG,cAAA,WAAAA,eAAAzJ,OAAA,EAAAoJ,eAAA;MACA,IAAAvO,GAAA,OAAAxC,IAAA;MACA,IAAAK,GAAA,OAAAL,IAAA,CAAA2H,OAAA;MACA,IAAAqJ,MAAA,GAAAD,eAAA,OAAA/Q,IAAA,CAAA+Q,eAAA;MACA;MACA,IAAAE,SAAA,GAAAC,IAAA,CAAAC,IAAA,EAAA9Q,GAAA,GAAAmC,GAAA;MACA;MACA,IAAAwO,MAAA,IAAAxO,GAAA,GAAAwO,MAAA;QACA;MACA;MAEA,IAAAC,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA;QACA;MACA;IACA;IACA,iBACAI,uBAAA,WAAAA,wBAAA;MACA,KAAAlL,sBAAA;MACA,KAAAyD,QAAA,CAAAK,OAAA;IACA;IACA,aACAqH,cAAA,WAAAA,eAAAhL,GAAA;MACA,IAAA9D,GAAA,OAAAxC,IAAA;MACA,IAAA2H,OAAA,OAAA3H,IAAA,CAAAsG,GAAA,CAAAqB,OAAA;MACA,IAAA/I,SAAA;MACA,IAAA2S,WAAA;MACA;MACA,IAAAjL,GAAA,CAAArI,gBAAA;QACA;QACA,IAAAqI,GAAA,CAAAjI,gBAAA;UACA,IAAAA,gBAAA,OAAA2B,IAAA,CAAAsG,GAAA,CAAAjI,gBAAA;UACA,IAAAA,gBAAA,GAAAsJ,OAAA;YACA/I,SAAA;YACA2S,WAAA,GAAAL,IAAA,CAAAC,IAAA,EAAA9S,gBAAA,GAAAsJ,OAAA;UACA;QACA;MACA,WAAArB,GAAA,CAAArI,gBAAA;QACA;QACA,IAAAuE,GAAA,GAAAmF,OAAA;UACA/I,SAAA;UACA2S,WAAA,GAAAL,IAAA,CAAAC,IAAA,EAAA3O,GAAA,GAAAmF,OAAA;QACA;MACA,WAAArB,GAAA,CAAArI,gBAAA;QACA;QACA,IAAAuE,GAAA,GAAAmF,OAAA;UACA/I,SAAA;UACA2S,WAAA,GAAAL,IAAA,CAAAC,IAAA,EAAA3O,GAAA,GAAAmF,OAAA;QACA;MACA;MAEA;QACA/I,SAAA,EAAAA,SAAA;QACA2S,WAAA,EAAAA;MACA;IACA;IAEA;IAEA;IACAC,qBAAA,WAAAA,sBAAAlL,GAAA;MACA,KAAAjF,qBAAA,GAAAiF,GAAA;MACA,KAAAhF,eAAA,GAAAgF,GAAA;MACA,KAAApF,mBAAA;MACA,KAAA4L,aAAA,CAAAxG,GAAA,CAAA3F,EAAA;IACA;IAEA,aACAmM,aAAA,WAAAA,cAAA2E,qBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7D,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAA4D,SAAA;QAAA,IAAAC,qBAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,eAAA;QAAA,WAAAlE,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA+D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7D,IAAA,GAAA6D,SAAA,CAAA5D,IAAA;YAAA;cAAA4D,SAAA,CAAA7D,IAAA;cAAA6D,SAAA,CAAA5D,IAAA;cAAA,OAGAoD,OAAA,CAAAS,oBAAA,CAAAV,qBAAA;YAAA;cAAAG,qBAAA,GAAAM,SAAA,CAAAzD,IAAA;cACAiD,OAAA,CAAAvQ,aAAA,GAAAyQ,qBAAA,CAAA9U,IAAA;;cAEA;cAAA+U,SAAA,OAAAO,2BAAA,CAAAxL,OAAA,EACA8K,OAAA,CAAAvQ,aAAA;cAAA+Q,SAAA,CAAA7D,IAAA;cAAAwD,SAAA,CAAAQ,CAAA;YAAA;cAAA,KAAAP,KAAA,GAAAD,SAAA,CAAAS,CAAA,IAAAC,IAAA;gBAAAL,SAAA,CAAA5D,IAAA;gBAAA;cAAA;cAAAyD,KAAA,GAAAD,KAAA,CAAA9C,KAAA;cAAAkD,SAAA,CAAA5D,IAAA;cAAA,OACAoD,OAAA,CAAAc,oBAAA,CAAAT,KAAA;YAAA;cAAAG,SAAA,CAAA5D,IAAA;cAAA;YAAA;cAAA4D,SAAA,CAAA5D,IAAA;cAAA;YAAA;cAAA4D,SAAA,CAAA7D,IAAA;cAAA6D,SAAA,CAAAtD,EAAA,GAAAsD,SAAA;cAAAL,SAAA,CAAAY,CAAA,CAAAP,SAAA,CAAAtD,EAAA;YAAA;cAAAsD,SAAA,CAAA7D,IAAA;cAAAwD,SAAA,CAAAa,CAAA;cAAA,OAAAR,SAAA,CAAAS,MAAA;YAAA;cAAAT,SAAA,CAAA5D,IAAA;cAAA,OAIA,IAAAsE,wCAAA,EAAAnB,qBAAA;YAAA;cAAAO,eAAA,GAAAE,SAAA,CAAAzD,IAAA;cACAiD,OAAA,CAAAtQ,UAAA,GAAA4Q,eAAA,CAAAlV,IAAA;cAAAoV,SAAA,CAAA5D,IAAA;cAAA;YAAA;cAAA4D,SAAA,CAAA7D,IAAA;cAAA6D,SAAA,CAAAW,EAAA,GAAAX,SAAA;cAEArD,OAAA,CAAAvE,KAAA,cAAA4H,SAAA,CAAAW,EAAA;cACAnB,OAAA,CAAA9H,QAAA,CAAAU,KAAA;YAAA;YAAA;cAAA,OAAA4H,SAAA,CAAApD,IAAA;UAAA;QAAA,GAAA6C,QAAA;MAAA;IAEA;IAEA,gBACAa,oBAAA,WAAAA,qBAAAT,KAAA;MAAA,IAAAe,OAAA;MAAA,WAAAjF,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAgF,SAAA;QAAA,IAAAhN,QAAA;QAAA,WAAA+H,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA8E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5E,IAAA,GAAA4E,SAAA,CAAA3E,IAAA;YAAA;cAAA2E,SAAA,CAAA5E,IAAA;cAAA4E,SAAA,CAAA3E,IAAA;cAAA,OAEA,IAAA4E,4CAAA,EAAAnB,KAAA,CAAApR,EAAA;YAAA;cAAAoF,QAAA,GAAAkN,SAAA,CAAAxE,IAAA;cACAqE,OAAA,CAAAK,IAAA,CAAApB,KAAA,iBAAAhM,QAAA,CAAAjJ,IAAA;cAAAmW,SAAA,CAAA3E,IAAA;cAAA;YAAA;cAAA2E,SAAA,CAAA5E,IAAA;cAAA4E,SAAA,CAAArE,EAAA,GAAAqE,SAAA;cAEApE,OAAA,CAAAvE,KAAA,gBAAA2I,SAAA,CAAArE,EAAA;cACAkE,OAAA,CAAAK,IAAA,CAAApB,KAAA;YAAA;YAAA;cAAA,OAAAkB,SAAA,CAAAnE,IAAA;UAAA;QAAA,GAAAiE,QAAA;MAAA;IAEA;IAEA,eACAZ,oBAAA,WAAAA,qBAAAV,qBAAA;MAAA,WAAA5D,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAqF,SAAA;QAAA,IAAArN,QAAA;QAAA,WAAA+H,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAAmF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjF,IAAA,GAAAiF,SAAA,CAAAhF,IAAA;YAAA;cAAAgF,SAAA,CAAAjF,IAAA;cAAAiF,SAAA,CAAAhF,IAAA;cAAA,OAGA,IAAAiF,qCAAA,EAAA9B,qBAAA;YAAA;cAAA1L,QAAA,GAAAuN,SAAA,CAAA7E,IAAA;cAAA,OAAA6E,SAAA,CAAAE,MAAA,WACAzN,QAAA;YAAA;cAAAuN,SAAA,CAAAjF,IAAA;cAAAiF,SAAA,CAAA1E,EAAA,GAAA0E,SAAA;cAEAzE,OAAA,CAAAvE,KAAA,eAAAgJ,SAAA,CAAA1E,EAAA;cAAA,OAAA0E,SAAA,CAAAE,MAAA,WACA;gBAAA1W,IAAA;cAAA;YAAA;YAAA;cAAA,OAAAwW,SAAA,CAAAxE,IAAA;UAAA;QAAA,GAAAsE,QAAA;MAAA;IAEA;IAEA,YACAK,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7F,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAA4F,SAAA;QAAA,IAAA5N,QAAA;QAAA,WAAA+H,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA0F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxF,IAAA,GAAAwF,SAAA,CAAAvF,IAAA;YAAA;cAAA,IAEAoF,OAAA,CAAAzO,YAAA;gBAAA4O,SAAA,CAAAvF,IAAA;gBAAA;cAAA;cACAoF,OAAA,CAAA9J,QAAA,CAAAC,OAAA;cAAA,OAAAgK,SAAA,CAAAL,MAAA;YAAA;cAAAK,SAAA,CAAAxF,IAAA;cAAAwF,SAAA,CAAAvF,IAAA;cAAA,OAKAoF,OAAA,CAAAnK,QAAA;gBACAuB,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;cACA;YAAA;cAAA6I,SAAA,CAAAvF,IAAA;cAAA,OAEA,IAAAmF,kCAAA,EAAAC,OAAA,CAAArS,qBAAA,CAAAV,EAAA;YAAA;cAAAoF,QAAA,GAAA8N,SAAA,CAAApF,IAAA;cACAiF,OAAA,CAAA9J,QAAA,CAAAK,OAAA;;cAEA;cAAA4J,SAAA,CAAAvF,IAAA;cAAA,OACAoF,OAAA,CAAA5G,aAAA,CAAA4G,OAAA,CAAArS,qBAAA,CAAAV,EAAA;YAAA;cAEA;cACA+S,OAAA,CAAAhO,OAAA;cAAAmO,SAAA,CAAAvF,IAAA;cAAA;YAAA;cAAAuF,SAAA,CAAAxF,IAAA;cAAAwF,SAAA,CAAAjF,EAAA,GAAAiF,SAAA;cAEA,IAAAA,SAAA,CAAAjF,EAAA;gBACA8E,OAAA,CAAA9J,QAAA,CAAAU,KAAA,gBAAAuJ,SAAA,CAAAjF,EAAA,CAAA3P,OAAA,IAAA4U,SAAA,CAAAjF,EAAA;cACA;YAAA;YAAA;cAAA,OAAAiF,SAAA,CAAA/E,IAAA;UAAA;QAAA,GAAA6E,QAAA;MAAA;IAEA;IAEA,aACAG,gBAAA,WAAAA,iBAAA/B,KAAA;MAAA,IAAAgC,OAAA;MAAA,WAAAlG,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAiG,SAAA;QAAA,WAAAlG,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA+F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7F,IAAA,GAAA6F,SAAA,CAAA5F,IAAA;YAAA;cAAA,IACAyF,OAAA,CAAA9O,YAAA;gBAAAiP,SAAA,CAAA5F,IAAA;gBAAA;cAAA;cACAyF,OAAA,CAAAnK,QAAA,CAAAC,OAAA;cAAA,OAAAqK,SAAA,CAAAV,MAAA;YAAA;cAAAU,SAAA,CAAA7F,IAAA;cAAA6F,SAAA,CAAA5F,IAAA;cAAA,OAKAyF,OAAA,CAAAxK,QAAA,kCAAAW,MAAA,CAAA6H,KAAA,CAAAoC,UAAA;gBACArJ,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;cACA;YAAA;cAAAkJ,SAAA,CAAA5F,IAAA;cAAA,OAEA,IAAAwF,qCAAA,EAAA/B,KAAA,CAAApR,EAAA;YAAA;cACAoT,OAAA,CAAAnK,QAAA,CAAAK,OAAA;;cAEA;cAAAiK,SAAA,CAAA5F,IAAA;cAAA,OACAyF,OAAA,CAAAjH,aAAA,CAAAiH,OAAA,CAAA1S,qBAAA,CAAAV,EAAA;YAAA;cACAoT,OAAA,CAAArO,OAAA;cAAAwO,SAAA,CAAA5F,IAAA;cAAA;YAAA;cAAA4F,SAAA,CAAA7F,IAAA;cAAA6F,SAAA,CAAAtF,EAAA,GAAAsF,SAAA;cAEA,IAAAA,SAAA,CAAAtF,EAAA;gBACAmF,OAAA,CAAAnK,QAAA,CAAAU,KAAA,eAAA4J,SAAA,CAAAtF,EAAA,CAAA3P,OAAA,IAAAiV,SAAA,CAAAtF,EAAA;cACA;YAAA;YAAA;cAAA,OAAAsF,SAAA,CAAApF,IAAA;UAAA;QAAA,GAAAkF,QAAA;MAAA;IAEA;IAEA,aACAI,iBAAA,WAAAA,kBAAArC,KAAA;MACA,UAAA9M,YAAA;QACA,KAAA2E,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAA/H,eAAA;MACA,KAAAD,kBAAA,GAAAkQ,KAAA;;MAEA;MACA,KAAArQ,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;;MAEA;MACA,KAAAH,eAAA;IACA;IAEA,gBACA4S,sBAAA,WAAAA,uBAAA;MACA,UAAApP,YAAA;QACA,KAAA2E,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAA/H,eAAA;MACA,KAAAD,kBAAA;;MAEA;MACA,KAAAH,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;;MAEA;MACA,KAAAH,eAAA;IACA;IAEA,aACA6S,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA1G,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAyG,SAAA;QAAA,WAAA1G,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAAuG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArG,IAAA,GAAAqG,SAAA,CAAApG,IAAA;YAAA;cAAAoG,SAAA,CAAArG,IAAA;cAAA,MAEAkG,OAAA,CAAAzS,eAAA;gBAAA4S,SAAA,CAAApG,IAAA;gBAAA;cAAA;cAAAoG,SAAA,CAAApG,IAAA;cAAA,OAEA,IAAA8F,sCAAA,EACAG,OAAA,CAAA1S,kBAAA,CAAAlB,EAAA,EACA4T,OAAA,CAAA7S,eAAA,CAAAC,iBAAA,QACA4S,OAAA,CAAA7S,eAAA,CAAAE,MAAA,QACA2S,OAAA,CAAA1S,kBAAA,CAAAqB,cAAA,MACA;YAAA;cACAqR,OAAA,CAAA3K,QAAA,CAAAK,OAAA;cAAAyK,SAAA,CAAApG,IAAA;cAAA;YAAA;cAAAoG,SAAA,CAAApG,IAAA;cAAA,OAGA,IAAA+F,2CAAA,EAAAE,OAAA,CAAAlT,qBAAA,CAAAV,EAAA;YAAA;cACA4T,OAAA,CAAA3K,QAAA,CAAAK,OAAA;YAAA;cAGAsK,OAAA,CAAA9S,eAAA;;cAEA;cAAAiT,SAAA,CAAApG,IAAA;cAAA,OACAiG,OAAA,CAAAzH,aAAA,CAAAyH,OAAA,CAAAlT,qBAAA,CAAAV,EAAA;YAAA;cAEA;cACA4T,OAAA,CAAA7O,OAAA;cAAAgP,SAAA,CAAApG,IAAA;cAAA;YAAA;cAAAoG,SAAA,CAAArG,IAAA;cAAAqG,SAAA,CAAA9F,EAAA,GAAA8F,SAAA;cAEA7F,OAAA,CAAAvE,KAAA,YAAAoK,SAAA,CAAA9F,EAAA;cACA2F,OAAA,CAAA3K,QAAA,CAAAU,KAAA,eAAAoK,SAAA,CAAA9F,EAAA,CAAA3P,OAAA,IAAAyV,SAAA,CAAA9F,EAAA;YAAA;YAAA;cAAA,OAAA8F,SAAA,CAAA5F,IAAA;UAAA;QAAA,GAAA0F,QAAA;MAAA;IAEA;IAEA,aACAG,eAAA,WAAAA,gBAAA5C,KAAA;MAAA,IAAA6C,OAAA;MAAA,WAAA/G,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAA8G,SAAA;QAAA,IAAA9O,QAAA,EAAA+O,WAAA;QAAA,WAAAhH,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA6G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3G,IAAA,GAAA2G,SAAA,CAAA1G,IAAA;YAAA;cAAA0G,SAAA,CAAA3G,IAAA;cAAA2G,SAAA,CAAA1G,IAAA;cAAA,OAEA,IAAA4E,4CAAA,EAAAnB,KAAA,CAAApR,EAAA;YAAA;cAAAoF,QAAA,GAAAiP,SAAA,CAAAvG,IAAA;cACAqG,WAAA,GAAA/O,QAAA,CAAAjJ,IAAA,QAEA;cACA8X,OAAA,CAAApT,eAAA,OAAAmF,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAmL,KAAA;gBACA+C,WAAA,EAAAA;cAAA,EACA;cAEAF,OAAA,CAAArT,eAAA;cAAAyT,SAAA,CAAA1G,IAAA;cAAA;YAAA;cAAA0G,SAAA,CAAA3G,IAAA;cAAA2G,SAAA,CAAApG,EAAA,GAAAoG,SAAA;cAEAnG,OAAA,CAAAvE,KAAA,cAAA0K,SAAA,CAAApG,EAAA;cACAgG,OAAA,CAAAhL,QAAA,CAAAU,KAAA;YAAA;YAAA;cAAA,OAAA0K,SAAA,CAAAlG,IAAA;UAAA;QAAA,GAAA+F,QAAA;MAAA;IAEA;IAEA,aACAI,iBAAA,WAAAA,kBAAAC,SAAA;MACA,KAAAA,SAAA;MAEA,IAAA5U,KAAA,OAAAN,IAAA,CAAAkV,SAAA;MACA,IAAA1S,GAAA,OAAAxC,IAAA;MACA,IAAAmV,MAAA,GAAA3S,GAAA,GAAAlC,KAAA;MACA,IAAA8U,SAAA,GAAAlE,IAAA,CAAAmE,KAAA,CAAAF,MAAA;MACA,IAAAG,WAAA,GAAApE,IAAA,CAAAmE,KAAA,CAAAF,MAAA;MAEA,IAAAC,SAAA;QACA,UAAAlL,MAAA,CAAAkL,SAAA,kBAAAlL,MAAA,CAAAoL,WAAA;MACA;QACA,UAAApL,MAAA,CAAAoL,WAAA;MACA;IACA;IAEA,cACAC,kBAAA,WAAAA,mBAAAxD,KAAA;MAAA,IAAAyD,OAAA;MACA,KAAArC,IAAA,CAAApB,KAAA;MACA,KAAA5F,SAAA;QACA;QACA,IAAAsJ,KAAA,GAAAD,OAAA,CAAAE,GAAA,CAAAC,aAAA,kBAAAzL,MAAA,CAAA6H,KAAA,CAAA7O,cAAA;QACA,IAAAuS,KAAA,EAAAA,KAAA,CAAAG,KAAA;MACA;IACA;IAEA,kBACAC,uBAAA,WAAAA,wBAAA9D,KAAA;MACA,KAAA3P,yBAAA,GAAA2P,KAAA;MACA,KAAA/P,iBAAA;QACAC,cAAA;QACAC,cAAA;QACAN,MAAA;MACA;MACA,KAAAG,iBAAA;IACA;IAEA,kBACA+T,kBAAA,WAAAA,mBAAA;MACA,KAAA/T,iBAAA;MACA,KAAAK,yBAAA;MACA,KAAA4G,KAAA,CAAAhH,iBAAA,SAAAgH,KAAA,CAAAhH,iBAAA,CAAA+T,WAAA;IACA;IAEA,eACAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MAAA,WAAApI,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAmI,SAAA;QAAA,IAAAC,gBAAA;QAAA,WAAArI,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAAkI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhI,IAAA,GAAAgI,SAAA,CAAA/H,IAAA;YAAA;cAAA+H,SAAA,CAAAhI,IAAA;cAAAgI,SAAA,CAAA/H,IAAA;cAAA,OAEA2H,OAAA,CAAAjN,KAAA,CAAAhH,iBAAA,CAAAiH,QAAA;YAAA;cAEAkN,gBAAA;gBACAG,OAAA,EAAAL,OAAA,CAAA7T,yBAAA,CAAAzB,EAAA;gBACAsB,cAAA,EAAAgU,OAAA,CAAAjU,iBAAA,CAAAC,cAAA;gBACAC,cAAA,EAAA+T,OAAA,CAAAjU,iBAAA,CAAAE,cAAA;gBACAN,MAAA,EAAAqU,OAAA,CAAAjU,iBAAA,CAAAJ;cACA;cAAAyU,SAAA,CAAA/H,IAAA;cAAA,OAEA,IAAAiI,yCAAA,EAAAJ,gBAAA;YAAA;cAEAF,OAAA,CAAArM,QAAA,CAAAK,OAAA;cACAgM,OAAA,CAAAlU,iBAAA;;cAEA;cAAAsU,SAAA,CAAA/H,IAAA;cAAA,OACA2H,OAAA,CAAAzD,oBAAA,CAAAyD,OAAA,CAAA7T,yBAAA;YAAA;cAAAiU,SAAA,CAAA/H,IAAA;cAAA;YAAA;cAAA+H,SAAA,CAAAhI,IAAA;cAAAgI,SAAA,CAAAzH,EAAA,GAAAyH,SAAA;cAEAJ,OAAA,CAAArM,QAAA,CAAAU,KAAA,iBAAA+L,SAAA,CAAAzH,EAAA,CAAA3P,OAAA,IAAAoX,SAAA,CAAAzH,EAAA;YAAA;YAAA;cAAA,OAAAyH,SAAA,CAAAvH,IAAA;UAAA;QAAA,GAAAoH,QAAA;MAAA;IAEA;IAEA,aACAM,gBAAA,WAAAA,iBAAAzE,KAAA,EAAA0E,UAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7I,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAA4I,UAAA;QAAA,WAAA7I,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA0I,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxI,IAAA,GAAAwI,UAAA,CAAAvI,IAAA;YAAA;cAAAuI,UAAA,CAAAxI,IAAA;cAAAwI,UAAA,CAAAvI,IAAA;cAAA,OAEAoI,OAAA,CAAAnN,QAAA;gBACAuB,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;cACA;YAAA;cAAA6L,UAAA,CAAAvI,IAAA;cAAA,OAEA,IAAAwI,8CAAA,EAAAL,UAAA,CAAA9V,EAAA;YAAA;cACA+V,OAAA,CAAA9M,QAAA,CAAAK,OAAA;;cAEA;cAAA4M,UAAA,CAAAvI,IAAA;cAAA,OACAoI,OAAA,CAAAlE,oBAAA,CAAAT,KAAA;YAAA;cAAA8E,UAAA,CAAAvI,IAAA;cAAA;YAAA;cAAAuI,UAAA,CAAAxI,IAAA;cAAAwI,UAAA,CAAAjI,EAAA,GAAAiI,UAAA;cAEA,IAAAA,UAAA,CAAAjI,EAAA;gBACA8H,OAAA,CAAA9M,QAAA,CAAAU,KAAA,iBAAAuM,UAAA,CAAAjI,EAAA,CAAA3P,OAAA,IAAA4X,UAAA,CAAAjI,EAAA;cACA;YAAA;YAAA;cAAA,OAAAiI,UAAA,CAAA/H,IAAA;UAAA;QAAA,GAAA6H,SAAA;MAAA;IAEA;IAEA,eACAI,kBAAA,WAAAA,mBAAA3L,MAAA;MACA,IAAA4L,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAA5L,MAAA;IACA;IAEA,eACA6L,kBAAA,WAAAA,mBAAA7L,MAAA;MACA,IAAA8L,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA9L,MAAA;IACA;IAEA,eACA+L,wBAAA,WAAAA,yBAAAC,UAAA;MACA,KAAAA,UAAA;MAEA,IAAAC,SAAA,GAAAD,UAAA,CAAAE,WAAA;MACA,IAAAD,SAAA,CAAAE,QAAA,UAAAF,SAAA,CAAAE,QAAA,UAAAF,SAAA,CAAAE,QAAA;QACA;MACA,WAAAF,SAAA,CAAAE,QAAA,UAAAF,SAAA,CAAAE,QAAA;QACA;MACA,WAAAF,SAAA,CAAAE,QAAA,SAAAF,SAAA,CAAAE,QAAA,WAAAF,SAAA,CAAAE,QAAA;QACA;MACA;QACA;MACA;IACA;IAEA,qBACAC,oBAAA,WAAAA,qBAAA;MACA;MACA,KAAArW,aAAA;MACA,KAAAC,UAAA;MACA,KAAAC,qBAAA;MACA,KAAAC,eAAA;MACA,KAAAE,eAAA;MAEA,KAAAE,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;;MAEA;MACA,KAAAL,eAAA;MACA,KAAAQ,iBAAA;MACA,KAAAN,eAAA;IACA;IAEA,qBACAgW,gBAAA,WAAAA,iBAAA;MACA;MACA,KAAAjW,eAAA;IACA;IAEA,qBACAkW,gBAAA,WAAAA,iBAAA;MACA;MACA,KAAAjW,eAAA;MACA,KAAAK,eAAA;MACA,KAAAD,kBAAA;MACA,KAAAH,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;MACA;MACA,SAAAoH,KAAA,CAAAtH,eAAA;QACA,KAAAsH,KAAA,CAAAtH,eAAA,CAAA0K,aAAA;MACA;IACA;IAEA,aACAuL,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA/J,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAA8J,UAAA;QAAA,WAAA/J,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAA4J,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1J,IAAA,GAAA0J,UAAA,CAAAzJ,IAAA;YAAA;cAAA,KACAsJ,OAAA,CAAAvW,qBAAA;gBAAA0W,UAAA,CAAAzJ,IAAA;gBAAA;cAAA;cAAAyJ,UAAA,CAAAzJ,IAAA;cAAA,OACAsJ,OAAA,CAAA9K,aAAA,CAAA8K,OAAA,CAAAvW,qBAAA,CAAAV,EAAA;YAAA;cACAiX,OAAA,CAAAhO,QAAA,CAAAK,OAAA;YAAA;YAAA;cAAA,OAAA8N,UAAA,CAAAjJ,IAAA;UAAA;QAAA,GAAA+I,SAAA;MAAA;IAEA;IAEA,gBACA3L,sBAAA,WAAAA,uBAAAuF,qBAAA;MAAA,IAAAuG,OAAA;MAAA,WAAAnK,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAkK,UAAA;QAAA,IAAAlS,QAAA;QAAA,WAAA+H,oBAAA,CAAAlH,OAAA,IAAAsH,IAAA,UAAAgK,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9J,IAAA,GAAA8J,UAAA,CAAA7J,IAAA;YAAA;cAAA6J,UAAA,CAAA9J,IAAA;cAAA8J,UAAA,CAAA7J,IAAA;cAAA,OAEA,IAAA8J,+CAAA,EAAA3G,qBAAA;YAAA;cAAA1L,QAAA,GAAAoS,UAAA,CAAA1J,IAAA;cACAuJ,OAAA,CAAA7U,kBAAA,GAAA4C,QAAA,CAAAjJ,IAAA;cAAAqb,UAAA,CAAA7J,IAAA;cAAA;YAAA;cAAA6J,UAAA,CAAA9J,IAAA;cAAA8J,UAAA,CAAAvJ,EAAA,GAAAuJ,UAAA;cAEAtJ,OAAA,CAAAvE,KAAA,iBAAA6N,UAAA,CAAAvJ,EAAA;cACAoJ,OAAA,CAAA7U,kBAAA;cACA6U,OAAA,CAAApO,QAAA,CAAAU,KAAA;YAAA;YAAA;cAAA,OAAA6N,UAAA,CAAArJ,IAAA;UAAA;QAAA,GAAAmJ,SAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}]}