<template>
  <div class="app-container">
    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-row" style="margin-bottom: 20px;">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card total">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-s-order"></i>
            </div>
            <div class="stats-info">
              <div class="stats-title">总任务</div>
              <div class="stats-number">{{ dashboardStats.total || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card completed" @click.native="handleStatusFilter('2')" :class="{'active': queryParams.completionStatus === '2'}">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-check"></i>
            </div>
            <div class="stats-info">
              <div class="stats-title">已完成</div>
              <div class="stats-number">{{ dashboardStats.completed || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card in-progress" @click.native="handleStatusFilter('1')" :class="{'active': queryParams.completionStatus === '1'}">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-loading"></i>
            </div>
            <div class="stats-info">
              <div class="stats-title">进行中</div>
              <div class="stats-number">{{ dashboardStats.inProgress || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card overdue" @click.native="handleOverdueFilter" :class="{'active': queryParams.isOverdue === 1}" style="cursor: pointer;">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-warning"></i>
            </div>
            <div class="stats-info">
              <div class="stats-title">逾期任务</div>
              <div class="stats-number">{{ dashboardStats.overdue || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 未分配工程师的打样单告警 -->
    <el-card class="alert-card">
      <div slot="header" class="alert-header">
        <div class="alert-title" @click="toggleUnassignedPanel">
          <span><i class="el-icon-warning-outline"></i>待分配打样单({{ unassignedTotal }}个)</span>
        </div>
        <div class="alert-actions">
          <el-button
            type="text"
            icon="el-icon-refresh"
            @click="handleRefreshUnassigned"
            style="margin-right: 10px"
            title="刷新列表">
            刷新
          </el-button>
          <el-button
            type="text"
            @click="toggleUnassignedPanel"
            style="margin-right: 10px">
            {{ isUnassignedPanelCollapsed ? '展开' : '收起' }}
            <i :class="isUnassignedPanelCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </el-button>
          <el-pagination
            @size-change="handleUnassignedSizeChange"
            @current-change="handleUnassignedCurrentChange"
            :current-page="unassignedQueryParams.pageNum"
            :page-sizes="[8, 16, 24, 32]"
            :page-size="unassignedQueryParams.pageSize"
            layout="total, sizes, prev, pager, next"
            :total="unassignedTotal">
          </el-pagination>
        </div>
      </div>
      <el-collapse-transition>
        <div v-show="!isUnassignedPanelCollapsed">
          <el-row :gutter="20" class="alert-cards">
            <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="item in unassignedOrders" :key="item.id">
              <el-card shadow="hover" class="alert-item">
                <div class="alert-item-header">
                  <div class="order-code-section">
                    <span style="color: #00afff;cursor: pointer"  @click="orderDetail(item)" class="order-code">{{ item.sampleOrderCode }}</span>
                    <el-tag
                      :type="getUrgencyType(item.endDate, item.latestStartTime)"
                      size="mini"
                      class="urgency-tag">
                      {{ getUrgencyText(item.endDate, item.latestStartTime) }}
                    </el-tag>
                  </div>
                </div>
                <div class="alert-item-content">
                  <div class="info-section">
                    <!-- 预估工时和难度等级 -->
                    <div class="info-row info-row-double">
                      <div class="info-item date-time-item" v-if="item.latestStartTime">
                        <i class="el-icon-alarm-clock" style="color: #909399;"></i>
                        <span class="info-label">最晚开始:</span>
                        <span class="info-value">{{ parseTime(item.latestStartTime, '{y}-{m}-{d}') }}</span>
                      </div>
                      <div class="info-item standard-item">
                        <i class="el-icon-time" style="color: #409EFF;"></i>
                        <span class="info-label">预估工时:</span>
                        <span class="info-value">{{ item.estimatedManHours || '-' }}H</span>
                      </div>
                    </div>
                    <!-- 最晚开始日期和截止日期排 -->
                    <div class="info-row info-row-double">
                      <div class="info-item date-time-item">
                        <i class="el-icon-date" style="color: #F56C6C;"></i>
                        <span class="info-label">截止日期:</span>
                        <span class="info-value">{{ parseTime(item.endDate, '{y}-{m}-{d}') }}</span>
                      </div>
                      <div class="info-item standard-item">
                        <i class="el-icon-star-on" style="color: #E6A23C;"></i>
                        <span class="info-label">难度等级:</span>
                        <el-tooltip :content="selectDictLabel(dylbOptions, item.difficultyLevelId)" placement="top">
                          <span class="info-value difficulty-text">{{selectDictLabel(dylbOptions,item.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>
                        </el-tooltip>
                      </div>
                    </div>
                  </div>
                  <el-tooltip v-if="item.failureReason" :content="item.failureReason" placement="top">
                    <div class="info-reason">
                      <i class="el-icon-warning-outline"></i>
                      <span class="reason-text">{{ item.failureReason }}</span>
                    </div>
                  </el-tooltip>
                </div>
                <div class="alert-item-footer">
                  <el-button type="primary" size="mini" icon="el-icon-user-solid" @click="handleAssignEngineer(item)" v-hasPermi="['software:engineerSampleOrder:changeEngineer']">
                    分配工程师
                  </el-button>
                  <el-button type="text" size="mini" icon="el-icon-close" @click="handleRejectUnassigned(item)" v-hasPermi="['software:engineerSampleOrder:rejectRask']" class="reject-btn" style="color: #F56C6C;">
                    驳回
                  </el-button>
                </div>
              </el-card>
           </el-col>
          </el-row>
         </div>
      </el-collapse-transition>
    </el-card>

    <!-- 筛选条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="客户" prop="customerId">
        <el-select v-model="queryParams.customerId" filterable placeholder="客户" clearable size="small">
          <el-option
            v-for="dict in customerOptions"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="日期范围" prop="dateRange" label-width="80px">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          :picker-options="dataPickerOptions"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
          style="width: 240px"/>
      </el-form-item>
      <el-form-item label="排单日期" prop="scheduledDate">
        <el-date-picker
          v-model="scheduledDateRange"
          type="daterange"
          :picker-options="dataPickerOptions"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"/>
      </el-form-item>
      <el-form-item label="申请日期" prop="startDate">
        <el-date-picker
          v-model="startDateRange"
          type="daterange"
          :picker-options="dataPickerOptions"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"/>
      </el-form-item>
      <el-form-item label="研发组别" prop="deptIds">
        <el-select v-model="queryParams.deptIds" multiple filterable placeholder="请选择对应研发组别">
          <el-option
            v-for="dept in researchDeptDatas"
            :key="dept.deptId"
            :label="dept.deptName"
            :value="dept.deptId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="服务模式" prop="serviceMode">
        <el-select v-model="queryParams.serviceMode" placeholder="请选择客户服务模式" clearable>
          <el-option
            v-for="dict in serviceModeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="实验室" prop="laboratory">
        <el-select v-model="queryParams.laboratory" placeholder="请选择实验室" clearable>
          <el-option label="宜侬" value="0" />
          <el-option label="瀛彩" value="1" />
        </el-select>
      </el-form-item>

      <el-form-item label="确认编码" prop="confirmCode">
        <el-input
          v-model.trim="queryParams.confirmCode"
          placeholder="请输入确认编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model.trim="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="姓名" prop="nickName" label-width="50px">
        <el-input v-model.trim="queryParams.nickName" placeholder="请输入工程师姓名" clearable/>
      </el-form-item>
      <el-form-item label="打样单编号" label-width="90px" prop="sampleOrderCode">
        <el-input v-model.trim="queryParams.sampleOrderCode" placeholder="请输入打样单编号" clearable/>
      </el-form-item>
      <el-form-item label="完成情况" prop="completionStatus">
        <el-select v-model="queryParams.completionStatus" placeholder="请选择完成情况" clearable>
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="实际开始时间" prop="actualStartTime" label-width="100px">
        <el-date-picker
          v-model="actualStartTimeRange"
          type="daterange"
          :picker-options="dataPickerOptions"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"/>
      </el-form-item>
      <el-form-item label="实际完成时间" prop="actualFinishTime" label-width="100px">
        <el-date-picker
          v-model="actualFinishTimeRange"
          type="daterange"
          :picker-options="dataPickerOptions"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['software:engineerSampleOrder:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['software:engineerSampleOrder:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['software:engineerSampleOrder:remove']">删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="doExportSampleOrder"
          v-hasPermi="['software:engineerSampleOrder:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-download" size="mini" :disabled="multiple" @click="handleBatchExportNrw"
          v-hasPermi="['software:engineerSampleOrder:export']">导出打样单任务</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAddSampleOrder"
          v-hasPermi="['software:engineerSampleOrder:add']">新增打样单</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 主要内容区域 -->
     <!-- 工单列表 -->
    <el-table v-loading="loading" :data="engineerSampleOrderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键ID" align="center" prop="id" /> -->
<!--      <el-table-column label="工程师ID" align="center" prop="userId" />-->
      <el-table-column align="center" prop="sampleOrderCode" width="160" fixed="left">
        <template slot="header">
          打样单编号
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              点击编号可查看详细信息
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-button type="text" @click="orderDetail(scope.row)">{{ scope.row.sampleOrderCode }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="startDate" width="140" fixed="left">
        <template slot="header">
          申请日期
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              客户提交打样申请的日期时间
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="scheduledDate" width="120" fixed="left">
        <template slot="header">
          排单日期
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              工程师被安排处理此打样单的日期
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.scheduledDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="latestStartTime" width="120" fixed="left">
        <template slot="header">
          最晚开始日期
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              必须在此日期前开始工作，否则可能影响交期
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.latestStartTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="endDate" width="120" fixed="left">
        <template slot="header">
          最晚截至日期
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              打样工作必须在此日期前完成
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" width="90" prop="completionStatus">
        <template slot="header">
          完成情况
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              当前打样单的状态
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.completionStatus == '0' ? 'info' :
                   scope.row.completionStatus == '1' ? 'primary' :
                   scope.row.completionStatus == '2' ? 'success' : 'info'"
            size="mini"
          >
            {{selectDictLabel(statusOptions,scope.row.completionStatus)}}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" width="90">
        <template slot="header">
          批次信息
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              显示当前批次总数
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div v-if="scope.row.totalBatches > 0">
            <el-tag size="mini" type="primary">
              {{ scope.row.totalBatches || 0 }}
            </el-tag>
          </div>
          <div v-else>
            -
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" width="90">
        <template slot="header">
          逾期情况
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              根据截止日期判断是否逾期及逾期天数
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div v-if="getOverdueInfo(scope.row).isOverdue">
            <el-tag type="danger" size="mini" style="margin-bottom: 2px;">
              逾期 {{ getOverdueInfo(scope.row).overdueDays }}天
            </el-tag>
          </div>
          <el-tag v-else type="success" size="mini">
            正常
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="nickName" width="95">
        <template slot="header">
          跟进人
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              负责此打样单的工程师姓名及职级
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          {{ scope.row.nickName }}{{ scope.row.rank ? '-' + scope.row.rank : '' }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="assistantName" width="95">
        <template slot="header">
          协助人
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              此打样单难度高于工程师能力范围
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          {{ scope.row.assistantName }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="isLocked" width="90">
        <template slot="header">
          锁定状态
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              锁定后不允许更换工程师，防止误操作
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isLocked"
            :active-value="1"
            :inactive-value="0"
            @change="(val) => handleTableLockChange(val, scope.row)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="laboratoryCode" width="150">
        <template slot="header">
          实验编码
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              实验室分配的唯一编码标识
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="difficultyLevelId" width="80">
        <template slot="header">
          难度
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              打样工作的技术难度等级
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-tooltip :content="selectDictLabel(dylbOptions,scope.row.difficultyLevelId)" placement="top">
            <span>{{selectDictLabel(dylbOptions,scope.row.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="标准工时" align="center" prop="standardManHours">
        <template slot-scope="scope">
          <span v-if="scope.row.standardManHours">{{ scope.row.standardManHours }}H</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="laboratory" width="90">
        <template slot="header">
          实验室
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              负责此打样单的实验室分支
            </div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          {{
            scope.row.laboratory === '0' ? '宜侬' :
            scope.row.laboratory === '1' ? '瀛彩' :
            ''
          }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="categoryName" width="70">
        <template slot="header">
          品类
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              产品所属的分类类别
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="SKU数" align="center" prop="sku" width="70" />
      <el-table-column align="center" prop="applicant" width="90">
        <template slot="header">
          申请人
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              提交此打样申请的人员
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="sales" width="70">
        <template slot="header">
          销售
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              负责此项目的销售人员
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="customerName" width="150">
        <template slot="header">
          客户名称
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              委托打样的客户公司名称
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="产品/项目等级" align="center" width="150">
        <template slot-scope="scope">
          {{ getProjectLevel(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="productName" width="150">
        <template slot="header">
          产品名称
          <el-tooltip placement="top">
            <i class="el-icon-question"></i>
            <div slot="content">
              需要打样的产品名称
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="实际工作时间" align="center" width="180">
        <template slot-scope="scope">
          <div>
            <div v-if="scope.row.actualStartTime">开始: {{ parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}') }}</div>
            <div v-if="scope.row.actualFinishTime">完成: {{ parseTime(scope.row.actualFinishTime, '{y}-{m}-{d} {h}:{i}') }}</div>
            <span v-if="!scope.row.actualStartTime && !scope.row.actualFinishTime">-</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="实际工时" align="center" prop="actualManHours">
        <template slot-scope="scope">
          <span v-if="scope.row.actualManHours">{{ scope.row.actualManHours }}H</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="预估工时" align="center" prop="estimatedManHours">
        <template slot-scope="scope">
          <span v-if="scope.row.estimatedManHours">{{ scope.row.estimatedManHours }}H</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="服务模式" align="center" prop="serviceMode" width="150" />
      <el-table-column label="计算类型" align="center" prop="checkType" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.checkType == 1">客户等级</span>
          <span v-else>打样单难度</span>
        </template>
      </el-table-column>
      <el-table-column label="完成质量" align="center" prop="qualityEvaluation" />
<!--      <el-table-column label="创建时间" align="center" prop="createTime" width="140">-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="打样单备注" align="center" prop="sampleOrderRemark" min-width="150">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.sampleOrderRemark" placement="top" :disabled="!scope.row.sampleOrderRemark">
            <div class="remark-text-ellipsis">
              {{ scope.row.sampleOrderRemark || '-' }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" min-width="150">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.remark" placement="top" :disabled="!scope.row.remark">
            <div class="remark-text-ellipsis">
              {{ scope.row.remark || '-' }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="未出样原因" align="center" prop="reasonForNoSample" min-width="150">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.reasonForNoSample" placement="top" :disabled="!scope.row.reasonForNoSample">
            <div class="remark-text-ellipsis">
              {{ scope.row.reasonForNoSample || '-' }}
            </div>
            </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="解决方案" align="center" prop="solution" min-width="150">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.solution" placement="top" :disabled="!scope.row.solution">
            <div class="remark-text-ellipsis">
              {{ scope.row.solution || '-' }}
            </div>
            </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="驳回原因" align="center" prop="rejectReason" min-width="150">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.rejectReason" placement="top" :disabled="!scope.row.rejectReason">
            <div class="remark-text-ellipsis">
              {{ scope.row.rejectReason || '-' }}
            </div>
            </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" align="center" class-name="fixed-width" width="180">
        <template slot-scope="scope">
          <!-- <el-tooltip content="编辑" placement="top">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['software:engineerSampleOrder:edit']" />
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['software:engineerSampleOrder:remove']" />
          </el-tooltip> -->
          <el-tooltip  v-if="scope.row.completionStatus==0" content="更换工程师或更改日期" placement="top">
            <el-button size="mini" type="text" icon="el-icon-user" v-if="scope.row.isLocked === 0" @click="handleChangeEngineer(scope.row)" v-hasPermi="['software:engineerSampleOrder:changeEngineer']" />
          </el-tooltip>


          <el-tooltip content="批次管理" v-if="[0,1,2].includes(scope.row.completionStatus)" placement="top">
            <el-button size="mini" type="text" icon="el-icon-collection" @click="handleBatchManagement(scope.row)" v-hasPermi="['software:engineerSampleOrder:batchManagement']" />
          </el-tooltip>
          <el-tooltip content="逾期操作" v-if="getOverdueInfo(scope.row).isOverdue" placement="top">
            <el-button size="mini" type="text" icon="el-icon-warning" @click="handleOverdueOperation(scope.row)" v-hasPermi="['software:engineerSampleOrder:overdueOperation']" style="color: #E6A23C;" />
          </el-tooltip>
          <el-tooltip content="驳回" v-if="scope.row.completionStatus == 0" placement="top">
            <el-button size="mini" type="text" icon="el-icon-close" @click="handleReject(scope.row)" v-hasPermi="['software:engineerSampleOrder:rejectRask']" style="color: #F56C6C;" />
          </el-tooltip>
          <el-tooltip content="更新实验室编码" v-if="scope.row.completionStatus == '2' && scope.row.itemStatus == 0" placement="top">
            <el-button size="mini" type="text" icon="el-icon-edit-outline" @click="handleUpdateLaboratoryCode(scope.row)" v-hasPermi="['software:engineerSampleOrder:editSampleOrderCode']" />
          </el-tooltip>
          <el-tooltip content="下载打样单" placement="top">
            <el-button size="mini" type="text" icon="el-icon-download" @click="handleDownloadSampleOrder(scope.row)" v-hasPermi="['software:engineerSampleOrder:export']" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      :auto-scroll="false" @pagination="getList" />

    <!-- 添加或修改工程师打样单关联对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="工程师ID" prop="userId">
              <el-input v-model="form.userId" placeholder="请输入工程师ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="打样单编号" prop="sampleOrderCode">
              <el-input v-model="form.sampleOrderCode" placeholder="请输入打样单编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="难度" prop="difficultyLevelId">
              <el-select v-model="form.difficultyLevelId" placeholder="请选择打样难度等级">
                  <el-option
                    v-for="dict in dylbOptions"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  ></el-option>
                </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="完成情况" prop="completionStatus">
              <el-select v-model="form.completionStatus" placeholder="请选择完成情况" style="width: 100%">
                <el-option
                  v-for="dict in statusOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="排单日期" prop="scheduledDate">
              <el-date-picker
                v-model="form.scheduledDate"
                type="datetime"
                placeholder="选择排单日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最晚截至日期" prop="endDate">
              <el-date-picker
                v-model="form.endDate"
                type="datetime"
                placeholder="选择最晚截至日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="锁定状态" prop="isLocked">
              <el-switch
                v-model="form.isLocked"
                :active-value="1"
                :inactive-value="0"
                active-text="已锁定"
                inactive-text="未锁定"
                @change="handleLockChange">
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="实际开始时间" prop="actualStartTime">
              <el-date-picker
                v-model="form.actualStartTime"
                type="datetime"
                placeholder="选择开始时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际完成时间" prop="actualFinishTime">
              <el-date-picker
                v-model="form.actualFinishTime"
                type="datetime"
                placeholder="选择完成时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="实际工时" prop="actualManHours">
              <el-input-number v-model="form.actualManHours" :min="0" :precision="1" :step="0.5" style="width: 100%" placeholder="请输入实际工时" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估工时" prop="estimatedManHours">
              <el-input-number v-model="form.estimatedManHours" :min="0" :precision="1" :step="0.5" style="width: 100%" placeholder="请输入预估工时" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="完成质量" prop="qualityEvaluation">
              <el-input v-model="form.qualityEvaluation" type="textarea" :rows="2" placeholder="请输入完成质量情况" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="打样单备注" prop="sampleOrderRemark">
              <el-input v-model="form.sampleOrderRemark" type="textarea" :rows="2" placeholder="请输入打样单备注信息" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注信息" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog> -->

    <!-- 新增打样单对话框 -->
    <el-dialog title="新增打样单" :visible.sync="addSampleOrderOpen" width="600px" append-to-body>
      <el-form ref="addSampleOrderForm" :model="addSampleOrderForm" :rules="addSampleOrderRules" label-width="100px">
        <el-form-item label="实验室" prop="labNo">
          <el-select v-model="addSampleOrderForm.labNo" placeholder="请选择实验室" style="width: 100%">
            <el-option label="宜侬" value="0" />
            <el-option label="瀛彩" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="类别" prop="categoryText">
          <el-select v-model="addSampleOrderForm.categoryText" filterable placeholder="请选择类别" style="width: 100%">
            <el-option
              v-for="dict in sckxxpgCplbs"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="难度" prop="dylb">
          <el-select v-model="addSampleOrderForm.dylb" placeholder="请选择难度" style="width: 100%">
            <el-option
              v-for="dict in dylbOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="addSampleOrderForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAddSampleOrder" :loading="addSampleOrderLoading">确 定</el-button>
        <el-button @click="cancelAddSampleOrder">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 更改工程师对话框 -->
    <el-dialog title="更改工程师" :visible.sync="changeEngineerOpen" width="600px" append-to-body>
      <el-form ref="changeEngineerForm" :model="changeEngineerForm" :rules="changeEngineerRules" label-width="150px">
        <el-form-item label="打样单编号">
          <el-input v-model="changeEngineerForm.sampleOrderCode" disabled />
        </el-form-item>
        <el-form-item label="当前工程师">
          <el-input v-model="changeEngineerForm.currentEngineerName" disabled />
        </el-form-item>
        <el-form-item label="选择工程师" prop="newEngineerId">
          <el-radio-group v-model="engineerSelectType" style="margin-bottom: 15px;">
            <el-radio label="specified">指定部门工程师</el-radio>
            <el-radio label="other">其他部门工程师</el-radio>
          </el-radio-group>

          <!-- 指定工程师选择 -->
          <div v-show="engineerSelectType === 'specified'">
            <el-select
              v-model="changeEngineerForm.newEngineerId"
              placeholder="请选择工程师"
              style="width: 100%"
              filterable>
              <el-option
                v-for="engineer in engineerOptions"
                :key="engineer.userId"
                :label="engineer.nickName"
                :value="engineer.userId"
              />
            </el-select>
          </div>

          <!-- 其他工程师选择 -->
          <div v-show="engineerSelectType === 'other'" class="other-engineer-select">
            <div class="select-item" style="margin-top: 10px;">
              <el-select
                v-model="changeEngineerForm.newEngineerId"
                placeholder="请选择工程师"
                style="width: 100%"
                filterable
                remote>
                <el-option
                  v-for="engineer in searchedEngineers"
                  :key="engineer.userId"
                  :label="engineer.nickName"
                  :value="engineer.userId"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="选择指定日期" prop="scheduledDate">
          <el-date-picker
            v-model="changeEngineerForm.scheduledDate"
            type="datetime"
            placeholder="选择日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="futureDatePickerOptions"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="重新调整工作安排">
          <el-switch
            v-model="changeEngineerForm.adjustWorkSchedule"
            :active-value="1"
            :inactive-value="0"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitChangeEngineer">确 定</el-button>
        <el-button @click="changeEngineerOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批次管理对话框 -->
    <el-dialog title="批次管理" :visible.sync="batchManagementOpen" width="900px" append-to-body @close="closeBatchManagement">
      <div class="batch-management-container">
        <!-- 状态提示信息 -->
        <el-alert
          v-if="!canEditBatch"
          title="当前打样单状态不允许编辑批次信息"
          type="warning"
          description="只有状态为'进行中'的打样单才可以编辑和添加测试批次内容，其他状态只能查看批次信息。"
          show-icon
          :closable="false"
          style="margin-bottom: 20px;">
        </el-alert>

        <!-- 进行中批次信息 -->
        <el-card class="active-batches-card" v-if="activeBatches && activeBatches.length > 0">
          <div slot="header" class="clearfix">
            <span>进行中批次 ({{ activeBatches.length }}个)</span>
            <el-button
              v-if="canEditBatch"
              style="float: right; padding: 3px 0"
              type="text"
              @click="finishAllActiveBatches">
              结束所有批次
            </el-button>
            <el-tag
              v-else
              style="float: right;"
              type="info"
              size="mini">
              只读模式
            </el-tag>
          </div>
          <!-- 进行中批次列表 -->
          <el-table :data="activeBatches" size="small" border stripe>
            <el-table-column prop="batchIndex" label="批次序号" width="80" align="center">
              <template slot-scope="scope">
                <el-tag size="mini" type="primary">第{{ scope.row.batchIndex }}批次</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="startTime" label="开始时间" width="150" align="center">
              <template slot-scope="scope">
                {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}
              </template>
            </el-table-column>
            <el-table-column label="已用时长" width="120" align="center">
              <template slot-scope="scope">
                <span>{{ calculateDuration(scope.row.startTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="实验室编号" width="200" align="center">
              <template slot-scope="scope">
                <div class="experiment-codes">
                  <el-tag
                    v-for="experiment in scope.row.experiments || []"
                    :key="experiment.id"
                    size="mini"
                    closable
                    @close="removeExperiment(scope.row, experiment)"
                    style="margin: 2px;"
                  >
                    {{ experiment.experimentCode }}
                  </el-tag>
                  <el-button
                    v-if="canEditBatch"
                    type="text"
                    size="mini"
                    @click="showAddExperimentDialog(scope.row)"
                    icon="el-icon-plus"
                  >
                    添加编号
                  </el-button>
                  <span v-if="(!scope.row.experiments || scope.row.experiments.length === 0) && !canEditBatch" style="color: #C0C4CC;">未设置</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="80" align="center">
              <template>
                <el-tag type="success" size="mini">进行中</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button
                  v-if="canEditBatch"
                  size="mini"
                  type="primary"
                  @click="finishSingleBatch(scope.row)">
                  结束批次
                </el-button>
                <el-button size="mini" type="text" @click="viewBatchDetail(scope.row)" icon="el-icon-view">
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>

        </el-card>

        <!-- 开始新批次按钮 -->
        <div class="new-batch-section" style="text-align: center; margin: 20px 0;">
          <el-button
            v-if="canEditBatch"
            type="primary"
            size="medium"
            @click="startNewBatch">
            开始新批次
          </el-button>
          <div v-else style="text-align: center; color: #909399;">
            <i class="el-icon-info" style="font-size: 48px; color: #C0C4CC;"></i>
            <p style="margin-top: 16px;">当前状态不允许开始新批次</p>
          </div>
        </div>

        <!-- 所有批次列表 -->
        <el-card class="all-batches-card" style="margin-top: 20px;">
          <div slot="header" class="clearfix">
            <span>所有批次 ({{ allBatches.length }}个)</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshBatchData">刷新</el-button>
          </div>
          <el-table :data="allBatches" size="small" border stripe>
            <el-table-column prop="batchIndex" label="批次序号" width="80" align="center">
              <template slot-scope="scope">
                <el-tag size="mini" type="primary">第{{ scope.row.batchIndex }}批次</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="startTime" label="开始时间" width="150" align="center">
              <template slot-scope="scope">
                {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}
              </template>
            </el-table-column>
            <el-table-column prop="endTime" label="结束时间" width="150" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.endTime">{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                <span v-else style="color: #909399;">未结束</span>
              </template>
            </el-table-column>
            <el-table-column prop="batchStatus" label="状态" width="100" align="center">
              <template slot-scope="scope">
                <el-tag :type="getBatchStatusType(scope.row.batchStatus)" size="mini">
                  {{ getBatchStatusText(scope.row.batchStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="laboratoryCode" label="实验室编号" width="120" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.laboratoryCode" style="color: #606266;">{{ scope.row.laboratoryCode }}</span>
                <span v-else style="color: #C0C4CC;">未设置</span>
              </template>
            </el-table-column>
            <el-table-column prop="actualManHours" label="实际工时" width="100" align="center">
              <template slot-scope="scope">
                <el-tag size="mini" :type="scope.row.actualManHours > 8 ? 'warning' : 'success'">
                  {{ scope.row.actualManHours || 0 }}小时
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="qualityEvaluation" label="质量评价" width="120" align="center">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.qualityEvaluation" size="mini"
                        :type="getQualityEvaluationType(scope.row.qualityEvaluation)">
                  {{ scope.row.qualityEvaluation }}
                </el-tag>
                <span v-else style="color: #909399;">未评价</span>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="批次备注" min-width="150" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="scope.row.remark" style="color: #606266;">{{ scope.row.remark }}</span>
                <span v-else style="color: #C0C4CC;">无备注</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button
                  v-if="canEditBatch && scope.row.batchStatus === 0"
                  size="mini"
                  type="success"
                  @click="startSingleBatch(scope.row)">
                  开始
                </el-button>
                <el-button
                  v-if="canEditBatch && scope.row.batchStatus === 1"
                  size="mini"
                  type="primary"
                  @click="finishSingleBatch(scope.row)">
                  结束
                </el-button>
                <el-button size="mini" type="text" @click="viewBatchDetail(scope.row)" icon="el-icon-view">
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 空状态 -->
          <div v-if="!allBatches || allBatches.length === 0" class="empty-state" style="text-align: center; padding: 40px;">
            <i class="el-icon-document" style="font-size: 48px; color: #C0C4CC;"></i>
            <p style="color: #909399; margin-top: 16px;">暂无批次记录</p>
          </div>
        </el-card>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="currentBatchRow && currentBatchRow.completionStatus == '0'"
          type="success"
          icon="el-icon-video-play"
          @click="handleStartFromBatch"
          v-hasPermi="['software:engineerSampleOrder:startRask']">
          开始任务
        </el-button>
        <el-button
          v-if="currentBatchRow && currentBatchRow.completionStatus == '1'"
          type="primary"
          icon="el-icon-check"
          @click="handleFinishFromBatch"
          v-hasPermi="['software:engineerSampleOrder:endRask']">
          完成任务
        </el-button>
        <el-button @click="batchManagementOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 批次详情对话框 -->
    <el-dialog title="批次详情" :visible.sync="batchDetailOpen" width="800px" append-to-body @close="closeBatchDetail">
      <div v-if="batchDetailData" class="batch-detail-container">
        <!-- 批次基本信息 -->
        <el-card class="batch-info-card" style="margin-bottom: 20px;">
          <div slot="header" class="clearfix">
            <span>批次基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>批次序号：</label>
                <el-tag type="primary">第{{ batchDetailData.batchIndex }}批次</el-tag>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 15px;">
            <el-col :span="12">
              <div class="info-item">
                <label>开始时间：</label>
                <span class="info-value">{{ parseTime(batchDetailData.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>结束时间：</label>
                <span class="info-value">
                  {{ batchDetailData.endTime ? parseTime(batchDetailData.endTime, '{y}-{m}-{d} {h}:{i}:{s}') : '未结束' }}
                </span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 15px;">
            <el-col :span="12">
              <div class="info-item">
                <label>实际工时：</label>
                <el-tag :type="batchDetailData.actualManHours > 8 ? 'warning' : 'success'">
                  {{ batchDetailData.actualManHours || 0 }}小时
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>质量评价：</label>
                <el-tag v-if="batchDetailData.qualityEvaluation"
                        :type="getQualityEvaluationType(batchDetailData.qualityEvaluation)">
                  {{ batchDetailData.qualityEvaluation }}
                </el-tag>
                <span v-else class="info-value">未评价</span>
              </div>
            </el-col>
          </el-row>
          <el-row style="margin-top: 15px;">
            <el-col :span="24">
              <div class="info-item">
                <label>批次备注：</label>
                <div class="remark-content">
                  {{ batchDetailData.remark || '无备注' }}
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 实验记录详情 -->
        <el-card class="experiments-card">
          <div slot="header" class="clearfix">
            <span>实验记录详情</span>
            <el-tag size="mini" style="margin-left: 10px;">
              共{{ batchDetailData.experiments ? batchDetailData.experiments.length : 0 }}条记录
            </el-tag>
          </div>
          <el-table :data="batchDetailData.experiments" size="small" border stripe>
            <el-table-column prop="experimentCode" label="实验编号" width="150" align="center">
              <template slot-scope="scope">
                <span style="color: #409EFF; font-weight: bold;">{{ scope.row.experimentCode }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="experimentNote" label="实验备注" min-width="200" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="scope.row.experimentNote">{{ scope.row.experimentNote }}</span>
                <span v-else style="color: #C0C4CC;">无备注</span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="150" align="center">
              <template slot-scope="scope">
                {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}
              </template>
            </el-table-column>
            <el-table-column prop="createBy" label="创建人" width="100" align="center">
              <template slot-scope="scope">
                <el-tag size="mini" type="info">{{ scope.row.createBy || '系统' }}</el-tag>
              </template>
            </el-table-column>
          </el-table>

          <!-- 实验记录空状态 -->
          <div v-if="!batchDetailData.experiments || batchDetailData.experiments.length === 0"
               class="empty-experiments" style="text-align: center; padding: 40px;">
            <i class="el-icon-data-line" style="font-size: 48px; color: #C0C4CC;"></i>
            <p style="color: #909399; margin-top: 16px;">该批次暂无实验记录</p>
          </div>
        </el-card>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="batchDetailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 添加实验记录对话框 -->
    <el-dialog title="添加实验室编号" :visible.sync="addExperimentOpen" width="500px" append-to-body @close="closeAddExperiment">
      <el-form :model="addExperimentForm" ref="addExperimentForm" label-width="100px" :rules="addExperimentRules">
        <el-form-item label="实验编号" prop="experimentCode">
          <el-input v-model="addExperimentForm.experimentCode" placeholder="请输入实验编号"></el-input>
        </el-form-item>
        <el-form-item label="实验备注">
          <el-input v-model="addExperimentForm.experimentNote" type="textarea" placeholder="请输入实验备注"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="addExperimentForm.remark" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAddExperiment">确 定</el-button>
        <el-button @click="addExperimentOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 结束批次对话框 -->
    <el-dialog :title="finishBatchMode === 'single' ? `结束第${currentFinishBatch && currentFinishBatch.batchIndex || ''}批次` : '结束当前批次'" :visible.sync="finishBatchOpen" width="500px" append-to-body @close="closeFinishBatch">
      <el-form :model="finishBatchForm" ref="finishBatchForm" label-width="100px">
        <el-form-item label="质量评价">
          <el-input v-model="finishBatchForm.qualityEvaluation" type="textarea" placeholder="请输入质量评价"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="finishBatchForm.remark" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFinishBatch">确 定</el-button>
        <el-button @click="finishBatchOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 完成任务对话框 -->
    <el-dialog title="完成任务" :visible.sync="finishTaskOpen" width="600px" append-to-body>
      <el-form ref="finishTaskForm" :model="finishTaskForm" :rules="finishTaskRules" label-width="120px">
        <el-form-item label="实验室编码" prop="laboratoryCode">
          <el-select v-model="finishTaskForm.laboratoryCode" multiple filterable placeholder="请选择实验室编码" style="width: 100%">
            <el-option
              v-for="item in experimentCodeList"
              :key="item.id"
              :label="item.experimentCode"
              :value="item.experimentCode">
              <span style="float: left">{{ item.experimentCode }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px" v-if="item.experimentNote">{{ item.experimentNote }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="finishTaskForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息（非必填）" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="finishTaskOpen = false">取 消</el-button>
        <el-button type="primary" @click="confirmFinishTask" :loading="finishTaskLoading">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 更新实验室编码对话框 -->
    <el-dialog title="更新实验室编码" :visible.sync="updateLaboratoryCodeOpen" width="600px" append-to-body>
      <el-form ref="updateLaboratoryCodeForm" :model="updateLaboratoryCodeForm" :rules="updateLaboratoryCodeRules" label-width="120px">
        <el-form-item label="实验室编码" prop="laboratoryCode">
          <el-input v-model="updateLaboratoryCodeForm.laboratoryCode" placeholder="请输入实验室编码(多个使用,分割)" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="updateLaboratoryCodeForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息（非必填）" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="updateLaboratoryCodeOpen = false">取 消</el-button>
        <el-button type="primary" @click="confirmUpdateLaboratoryCode" :loading="updateLaboratoryCodeLoading">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 导出选择对话框 -->
    <el-dialog title="选择导出内容" :visible.sync="exportDialogOpen" width="400px" append-to-body>
      <div class="export-options">
        <p style="margin-bottom: 20px; color: #606266;">请选择要导出的打样单类型：</p>
        <el-checkbox-group v-model="exportOptions" style="display: flex; flex-direction: column;">
          <el-checkbox label="assigned" style="margin-bottom: 15px;">
            <span style="font-weight: 500;">已分配</span>
            <div style="color: #909399; font-size: 12px; margin-top: 5px;">
              导出已分配给工程师的打样单数据
            </div>
          </el-checkbox>
          <el-checkbox label="unassigned" style="margin-bottom: 15px;">
            <span style="font-weight: 500;">未分配</span>
            <div style="color: #909399; font-size: 12px; margin-top: 5px;">
              导出尚未分配工程师的打样单数据
            </div>
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportDialogOpen = false">取 消</el-button>
        <el-button type="primary" @click="confirmExport" :disabled="exportOptions.length === 0" :loading="exportLoading">
          确认导出
        </el-button>
      </div>
    </el-dialog>

    <!-- 驳回对话框 -->
    <el-dialog title="驳回打样单" :visible.sync="rejectDialogOpen" width="500px" append-to-body>
      <el-form ref="rejectForm" :model="rejectForm" :rules="rejectRules" label-width="100px">
        <el-form-item label="打样单编号">
          <el-input v-model="rejectForm.sampleOrderCode" disabled />
        </el-form-item>
        <el-form-item label="驳回理由" prop="rejectReason">
          <el-input v-model="rejectForm.rejectReason" type="textarea" :rows="4" placeholder="请输入驳回理由" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="rejectDialogOpen = false">取 消</el-button>
        <el-button type="primary" @click="confirmReject" :loading="rejectLoading">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 逾期操作对话框 -->
    <el-dialog title="逾期操作" :visible.sync="overdueOperationOpen" width="600px" append-to-body>
      <el-form ref="overdueOperationForm" :model="overdueOperationForm" label-width="120px">
        <el-form-item label="打样单编号">
          <el-input v-model="overdueOperationForm.sampleOrderCode" disabled />
        </el-form-item>

        <el-form-item label="预计出样时间">
          <el-date-picker
            v-model="overdueOperationForm.expectedSampleTime"
            type="datetime"
            placeholder="选择日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="futureDatePickerOptions"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="未出样原因">
          <el-input v-model="overdueOperationForm.reasonForNoSample" type="textarea" :rows="3" placeholder="请输入未出样原因（非必填）" />
        </el-form-item>
        <el-form-item label="解决方案">
          <el-input v-model="overdueOperationForm.solution" type="textarea" :rows="3" placeholder="请输入解决方案（非必填）" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="overdueOperationOpen = false">取 消</el-button>
        <el-button type="primary" @click="confirmOverdueOperation" :loading="overdueOperationLoading">确 定</el-button>
      </div>
    </el-dialog>


    <!-- 添加或修改项目流程进行对话框 -->
    <executionAddOrEdit ref="executionAddOrEdit"></executionAddOrEdit>
  </div>
</template>

<script>
import {
  listEngineerSampleOrder,
  getEngineerSampleOrder,
  delEngineerSampleOrder,
  addEngineerSampleOrder,
  updateEngineerSampleOrder,
  updateSampleOrderStatus,
  getDashboardStats,
  getResearchDepartments,
  getEngineersByDifficultyLevel,
  changeEngineer,
  getResearchDepartmentsUser,
  getBatchesByOrderId,
  startNewBatch,
  addExperimentToBatch,
  getExperimentsByBatchId,
  exportEngineerSampleOrder,
  getExecutionByOrderInfo,
  getBatchExperimentCodeList,
  getActiveBatches,
  startSingleBatch,
  finishSingleBatch,
  finishAllActiveBatches,
  removeExperimentFromBatch
} from "@/api/software/engineerSampleOrder";
import {exportNrwItem, exportMultipleNrw} from "@/api/project/projectItemOrder";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {customerBaseAll} from "@/api/customer/customer";
import {isNull} from "@/utils/validate";
import executionAddOrEdit from "@/components/Project/components/executionAddOrEdit.vue";

export default {
  name: "EngineerSampleOrder",
  components: {
     Treeselect,executionAddOrEdit
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 选中的完整行数据
      selectedRows: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工程师打样单关联表格数据
      engineerSampleOrderList: [],
      // 研发部部门树列表
      researchDeptDatas:[],
      // 打样类别字典
      dylbOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        nickName: null,
        sampleOrderCode: null,
        completionStatus: null,
        scheduledDate: null,
        startDate: null,
        actualStartTime: null,
        actualFinishTime: null,
        deptId: null,
        deptIds: [],
        associationStatus: null,
        customerId: null,
        productName: null,
        confirmCode: null,
        isOverdue: null,  // 增逾期任务过滤参数
        laboratory: null  // 实验室筛选参数
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "工程师ID不能为空", trigger: "blur" }
        ],
        sampleOrderCode: [
          { required: true, message: "打样单编号不能为空", trigger: "blur" }
        ],
        completionStatus: [
          { required: true, message: "完成情况不能为空", trigger: "change" }
        ]
      },
      statusOptions: [],
      serviceModeOptions: [],
      dateRange: [], // 新增的日期范围筛选
      scheduledDateRange: [],
      startDateRange: [],
      actualStartTimeRange: [],
      actualFinishTimeRange: [],
      // 日期选择器配置
      dataPickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            const today = new Date();
            picker.$emit('pick', [today, today]);
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const yesterday = new Date();
            yesterday.setTime(yesterday.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', [yesterday, yesterday]);
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      // 状态更新对话框
      statusOpen: false,
      dashboardStats: {
        "total": 0,
        "completed": 0,
        "inProgress": 0,
        "overdue": 0
      },
      // 更改工程师对话框
      changeEngineerOpen: false,
      // 更改工程师表单
      changeEngineerForm: {
        id: null,
        sampleOrderCode: null,
        currentEngineerName: null,
        oldEngineerId: null,
        newEngineerId: null,
        scheduledDate: null,
        adjustWorkSchedule: 0
      },
      // 更改工程师表单校验
      changeEngineerRules: {
        newEngineerId: [
          { required: true, message: "请选择工程师", trigger: "change" }
        ],
        scheduledDate: [
          { required: true, message: "请选择日期", trigger: "change" }
        ]
      },
      // 工程师选项
      engineerOptions: [],
      // 批次管理相关
      batchManagementOpen: false,
      activeBatches: [], // 进行中的批次列表
      allBatches: [], // 所有批次列表
      selectedOrderForBatch: null,
      currentBatchRow: null, // 当前批次管理的行数据
      // 批次详情对话框
      batchDetailOpen: false,
      batchDetailData: null,
      // 结束批次对话框
      finishBatchOpen: false,
      finishBatchForm: {
        qualityEvaluation: '',
        remark: ''
      },
      currentFinishBatch: null, // 当前要结束的批次（单个批次时使用）
      finishBatchMode: 'all', // 'all' 表示结束所有批次，'single' 表示结束单个批次
      // 添加实验记录对话框
      addExperimentOpen: false,
      addExperimentForm: {
        experimentCode: '',
        experimentNote: '',
        remark: ''
      },
      addExperimentRules: {
        experimentCode: [
          { required: true, message: '实验编号不能为空', trigger: 'blur' }
        ]
      },
      currentBatchForExperiment: null,
      // 未来日期选择器配置
      futureDatePickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 禁用今天之前的日期
        }
      },
      engineerSelectType: 'specified',
      searchedEngineers: [],
      // 未分配打样单告警
      unassignedOrders: [],
      unassignedQueryParams: {
        pageNum: 1,
        pageSize: 8
      },
      unassignedTotal: 0,
      // 未分配面板折叠状态
      isUnassignedPanelCollapsed: true,
      // 完成任务对话框
      finishTaskOpen: false,
      finishTaskLoading: false,
      finishTaskForm: {
        laboratoryCode: '',
        remark: ''
      },
      experimentCodeList: [], // 实验室编码列表
      finishTaskRules: {
        laboratoryCode: [
          { required: true, message: "实验室编码不能为空", trigger: "blur" }
        ]
      },
      currentFinishRow: null,
      // 更新实验室编码对话框
      updateLaboratoryCodeOpen: false,
      updateLaboratoryCodeLoading: false,
      updateLaboratoryCodeForm: {
        laboratoryCode: '',
        remark: ''
      },
      updateLaboratoryCodeRules: {
        laboratoryCode: [
          { required: true, message: "实验室编码不能为空", trigger: "blur" }
        ]
      },
      currentUpdateLaboratoryCodeRow: null,
      // 导出选择对话框
      exportDialogOpen: false,
      exportOptions: [],
      exportLoading: false,
      readonly: true,
      // 项目详情相关
      currentProjectType: null,
      confirmItemCodes: [],
      customerOptions: [],
      itemNames: [],
      // 驳回对话框
      rejectDialogOpen: false,
      rejectLoading: false,
      rejectForm: {
        sampleOrderCode: '',
        rejectReason: ''
      },
      // 逾期操作对话框
      overdueOperationOpen: false,
      overdueOperationLoading: false,
      overdueOperationForm: {
        sampleOrderCode: '',
        expectedSampleTime: '',
        reasonForNoSample: '',
        solution: ''
      },
      currentOverdueRow: null,
      rejectRules: {
        rejectReason: [
          { required: true, message: "驳回理由不能为空", trigger: "blur" }
        ]
      },
      currentRejectRow: null
    };
  },
  computed: {
    /** 计算是否可以编辑批次 - 只有状态为'1'(进行中)的打样单才可以编辑 */
    canEditBatch() {
      return this.selectedOrderForBatch && this.selectedOrderForBatch.completionStatus === 1;
    }
  },
  created() {
    // 设置默认日期范围为当天
    const today = new Date();
    const todayStr = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');
    this.dateRange = [todayStr, todayStr];

    this.getList();
    customerBaseAll().then(res=> this.customerOptions = res)

    this.getDicts("DYD_GCSZT").then(response => {
        this.statusOptions = response.data;
    });
    this.getDicts("CUSTOMER_SERVICE_MODE").then(response => {
      this.serviceModeOptions = response.data;
    });
    this.getDicts("project_nrw_dylb").then(response => {
      this.dylbOptions = response.data;
    });
    this.loadDashboardStats();
    // 获取研发部部门列表
    getResearchDepartments().then(response => {
      this.researchDeptDatas = this.handleTree(response.data, "deptId");
    });
    // 获取未分配打样单
    this.handleUnassignedOrders();
  },
  methods: {
    /** 计算产品/项目等级 */
    getProjectLevel(row) {
      const customerLevel = row.customerLevel || '';
      const projectLevel = row.projectLevel || '';

      // 如果 projectLevel 是空字符串或 "/" 就不相加
      if (projectLevel === '' || projectLevel === '/') {
        return customerLevel;
      }

      return customerLevel + projectLevel;
    },
    /** 查询工程师打样单关联列表 */
    getList() {
      let params = { ...this.queryParams };
      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')
      params = this.addDateRange(params, this.startDateRange,'StartDate')
      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')
      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')
      // 清空之前的日期范围参数，根据当前状态重新设置
      delete params.beginDateRange;
      delete params.endDateRange;
      // 添加日期范围筛选参数
      if (this.dateRange && this.dateRange.length === 2) {
        params.beginDateRange = this.dateRange[0];
        params.endDateRange = this.dateRange[1];
      }
      this.loading = true;
      params.associationStatus = 1
      listEngineerSampleOrder(params).then(response => {
        this.engineerSampleOrderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        sampleOrderCode: null,
        serviceMode: null,
        difficultyLevelId: null,
        completionStatus: null,
        scheduledDate: null,
        actualStartTime: null,
        actualFinishTime: null,
        actualManHours: null,
        estimatedManHours: null,
        standardManHours: null,
        qualityEvaluation: null,
        isLocked: 0,
        startDate: null,
        endDate: null,
        checkType: null,
        sampleOrderRemark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.unassignedQueryParams.pageNum = 1;
      this.getList();
      this.loadDashboardStats();
      this.handleUnassignedOrders();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.dateRange = [] // 重置日期范围
      this.scheduledDateRange = []
      this.startDateRange = []
      this.actualStartTimeRange = []
      this.actualFinishTimeRange = []
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.selectedRows = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工程师打样单关联";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getEngineerSampleOrder(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工程师打样单关联";addEngineerSampleOrder
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateEngineerSampleOrder(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.loadDashboardStats();
            });
          } else {
            addEngineerSampleOrder(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.loadDashboardStats();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除工程师打样单关联编号为"' + row.sampleOrderCode + '"的数据项？').then(function () {
        return delEngineerSampleOrder(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
        this.loadDashboardStats();
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      // 重置导出选项并显示选择对话框
      this.exportOptions = [];
      this.exportDialogOpen = true;
    },
    /** 确认导出操作 */
    confirmExport() {
      if (this.exportOptions.length === 0) {
        this.$message.warning('请至少选择一种导出类型');
        return;
      }
      this.exportLoading = true;
      this.executeExports(0);
    },

    /** 串行执行导出操作 */
    executeExports(index) {
      if (index >= this.exportOptions.length) {
        // 所有导出完成
        this.exportLoading = false;
        this.exportDialogOpen = false;
        this.$message.success(`导出完成，共导出${this.exportOptions.length}个文件`);
        return;
      }

      const option = this.exportOptions[index];
      const associationStatus = option === 'assigned' ? 1 : 0;
      const typeName = option === 'assigned' ? '已分配' : '未分配';

      this.doExport(associationStatus, option).then(() => {
        this.$message.success(`${typeName}打样单导出成功`);
        // 继续导出下一个
        this.executeExports(index + 1);
      }).catch((error) => {
        this.exportLoading = false;
        this.$message.error(`${typeName}打样单导出失败: ${error.message || error}`);
      });
    },
    /** 执行导出操作 */
    doExport(associationStatus, exportType) {
      let params = { ...this.queryParams };
      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')
      params = this.addDateRange(params, this.startDateRange,'StartDate')
      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')
      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')
      // 清空之前的日期范围参数，根据当前状态重新设置
      delete params.beginDateRange;
      delete params.endDateRange;
      // 添加日期范围筛选参数
      if (this.dateRange && this.dateRange.length === 2) {
        params.beginDateRange = this.dateRange[0];
        params.endDateRange = this.dateRange[1];
      }
      params.associationStatus = associationStatus;
      params.exportType = exportType;
      return exportEngineerSampleOrder(params).then(response => {
        this.download(response.msg);
      });
    },
    /** 执行导出打样单（已分配/未分配） */
    doExportSampleOrder() {
      let params = { ...this.queryParams };
      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')
      params = this.addDateRange(params, this.startDateRange,'StartDate')
      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')
      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')
      // 清空之前的日期范围参数，根据当前状态重新设置
      delete params.beginDateRange;
      delete params.endDateRange;
      // 添加日期范围筛选参数
      if (this.dateRange && this.dateRange.length === 2) {
        params.beginDateRange = this.dateRange[0];
        params.endDateRange = this.dateRange[1];
      }
      this.$confirm('是否确认导出所有（已分配/未分配）打样单关联数据？', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.exportLoading = true;
        return exportEngineerSampleOrder(params);
      }).then(response => {
        this.download(response.msg);
        this.exportLoading = false;
        this.$message.success("导出成功");
      }).catch(() => {
        this.exportLoading = false;
      });
    },
    /** 点击"开始"按钮操作 */
    handleStart(row) {
      this.$confirm('确定要将打样单：' + row.sampleOrderCode + '标记为进行中吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateSampleOrderStatus({
          id: row.id,
          status: '1'
        }).then(response => {
          this.msgSuccess('打样单已设置为已开始');
          this.getList();
          this.loadDashboardStats();
        });
      });
    },
    /** 下载打样单操作 */
    handleDownloadSampleOrder(row) {
      this.$confirm('是否确认导出打样单?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.exportLoading = true;
        return exportNrwItem({itemId: row.itemId,projectId: row.projectId})
      }).then(response => {
        this.download(response.msg);
        this.exportLoading = false;
      }).catch(() => {});
    },
    /** 批量导出打样单任务操作 */
    handleBatchExportNrw() {
      if (this.selectedRows.length === 0) {
        this.$modal.msgError("请选择要导出的打样单任务");
        return;
      }

      // 构造批量导出数据，传递itemId和projectId
      const exportData = this.selectedRows.map(row => ({
        itemId: row.itemId,
        projectId: row.projectId
      }));

      this.$confirm('是否确认导出所选打样单任务的内容物数据？', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.exportLoading = true;
        return exportMultipleNrw(exportData);
      }).then(response => {
        this.download(response.msg);
        this.exportLoading = false;
        this.$message.success("导出成功");
      }).catch(() => {
        this.exportLoading = false;
      });
    },
    /** 点击"完成"按钮操作 */
    handleFinish(row) {
      this.currentFinishRow = row;
      this.finishTaskForm.laboratoryCode = '';
      this.finishTaskForm.remark = '';
      this.finishTaskOpen = true;
      // 加载实验室编码列表
      this.loadExperimentCodeList(row.id);
      this.$nextTick(() => {
        this.$refs.finishTaskForm.clearValidate();
      });
    },
    /** 确认完成任务 */
    confirmFinishTask() {
      this.$refs.finishTaskForm.validate(valid => {
        // 处理实验室编号字段
        let laboratoryCode = this.finishTaskForm.laboratoryCode;

        // 如果是数组类型，使用逗号拼接
        if (Array.isArray(laboratoryCode)) {
          laboratoryCode = laboratoryCode.join(",");
        }
        if (valid) {
          this.finishTaskLoading = true;
          updateSampleOrderStatus({
            id: this.currentFinishRow.id,
            status: '2',
            laboratoryCode: laboratoryCode,
            remark: this.finishTaskForm.remark
          }).then(response => {
            this.finishTaskLoading = false;
            this.finishTaskOpen = false;
            this.msgSuccess('打样单已设置为已完成');
            this.getList();
            this.loadDashboardStats();
          }).catch(() => {
            this.finishTaskLoading = false;
          });
        }
      });
    },
    /** 从批次管理对话框完成任务 */
    handleFinishFromBatch() {
      this.currentFinishRow = this.currentBatchRow;
      this.finishTaskForm.laboratoryCode = '';
      this.finishTaskForm.remark = '';
      this.finishTaskOpen = true;
      // 加载实验室编码列表
      this.loadExperimentCodeList(this.currentBatchRow.id);
      this.$nextTick(() => {
        this.$refs.finishTaskForm.clearValidate();
      });
    },
    /** 从批次管理对话框开始任务 */
    handleStartFromBatch() {
      const row = this.currentBatchRow;
      this.$confirm('确定要将打样单：' + row.sampleOrderCode + '标记为进行中吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateSampleOrderStatus({
          id: row.id,
          status: '1'
        }).then(response => {
          this.msgSuccess('打样单已设置为已开始');
          this.getList();
          this.loadDashboardStats();
          // 更新当前批次行数据的状态
          this.currentBatchRow.completionStatus = '1';
          this.selectedOrderForBatch.completionStatus = 1;
          if (this.selectedOrderForBatch) {
            this.loadBatchData(this.selectedOrderForBatch.id);
          }
        });
      });
    },
    /** 点击"更新实验室编码"按钮操作 */
    handleUpdateLaboratoryCode(row) {
      this.currentUpdateLaboratoryCodeRow = row;
      // 回显当前的实验室编码和备注
      this.updateLaboratoryCodeForm.laboratoryCode = row.laboratoryCode || '';
      this.updateLaboratoryCodeForm.remark = row.remark || '';
      this.updateLaboratoryCodeOpen = true;
      this.$nextTick(() => {
        this.$refs.updateLaboratoryCodeForm.clearValidate();
      });
    },
    /** 确认更新实验室编码 */
    confirmUpdateLaboratoryCode() {
      this.$refs.updateLaboratoryCodeForm.validate(valid => {
        if (valid) {
          this.updateLaboratoryCodeLoading = true;
          updateSampleOrderStatus({
            id: this.currentUpdateLaboratoryCodeRow.id,
            status: this.currentUpdateLaboratoryCodeRow.completionStatus, // 保持当前状态
            laboratoryCode: this.updateLaboratoryCodeForm.laboratoryCode,
            remark: this.updateLaboratoryCodeForm.remark
          }).then(response => {
            this.updateLaboratoryCodeLoading = false;
            this.updateLaboratoryCodeOpen = false;
            this.msgSuccess('实验室编码更新成功');
            this.getList();
            this.loadDashboardStats();
          }).catch(() => {
            this.updateLaboratoryCodeLoading = false;
          });
        }
      });
    },
    /** 点击"驳回"按钮操作 */
    handleReject(row) {
      this.currentRejectRow = row;
      this.rejectForm.sampleOrderCode = row.sampleOrderCode;
      this.rejectForm.rejectReason = '';
      this.rejectDialogOpen = true;
      this.$nextTick(() => {
        this.$refs.rejectForm.clearValidate();
      });
    },
    /** 确认驳回 */
    confirmReject() {
      this.$refs.rejectForm.validate(valid => {
        if (valid) {
          this.rejectLoading = true;
          updateSampleOrderStatus({
            id: this.currentRejectRow.id,
            status: '3',
            rejectReason: this.rejectForm.rejectReason
          }).then(response => {
            this.rejectLoading = false;
            this.rejectDialogOpen = false;
            this.msgSuccess('打样单已驳回');
            this.getList();
            this.loadDashboardStats();
            // 如果是未分配打样单的驳回，也需要刷新未分配列表
            this.handleUnassignedOrders();
          }).catch(() => {
            this.rejectLoading = false;
          });
        }
      });
    },
    /** 点击"逾期操作"按钮操作 */
    handleOverdueOperation(row) {
      this.currentOverdueRow = row;
      this.overdueOperationForm.sampleOrderCode = row.sampleOrderCode;
      this.overdueOperationForm.expectedSampleTime = row.expectedSampleTime;
      this.overdueOperationForm.reasonForNoSample = row.reasonForNoSample;
      this.overdueOperationForm.solution = row.solution;
      this.overdueOperationOpen = true;
      this.$nextTick(() => {
        this.$refs.overdueOperationForm.clearValidate();
      });
    },
    /** 确认逾期操作 */
    confirmOverdueOperation() {
      this.$refs.overdueOperationForm.validate(valid => {
        if (valid) {
          this.overdueOperationLoading = true;
          updateSampleOrderStatus({
            id: this.currentOverdueRow.id,
            status: "11", // 特殊处理，只更新“逾期情况”填写的字段
            expectedSampleTime: this.overdueOperationForm.expectedSampleTime,
            reasonForNoSample: this.overdueOperationForm.reasonForNoSample,
            solution: this.overdueOperationForm.solution
          }).then(response => {
            this.overdueOperationLoading = false;
            this.overdueOperationOpen = false;
            this.msgSuccess('逾期操作已完成');
            this.getList();
            this.loadDashboardStats();
          }).catch(() => {
            this.overdueOperationLoading = false;
          });
        }
      });
    },
    /** 查看打样单详情 */
    async orderDetail(row) {
      try {
        //获取执行ID
        const orderId = row.projectOrderId;
        let data = await getExecutionByOrderInfo(orderId);
        if(data!=null && !isNull(data.id)){
            let id = data.id;
            this.$refs.executionAddOrEdit.open = true;
            await this.$nextTick()
            this.$refs.executionAddOrEdit.reset()
            this.$refs.executionAddOrEdit.show(id, 1)
        }else{
          this.$message.error('获取数据失败');
        }
      } catch (error) {
        console.error('查看项目详情失败:', error);
        this.$message.error('查看项目详情失败: ' + (error.message || '未知错误'));
      }
    },

    /** 锁定状态改变处理 */
    handleLockChange(value) {
      if (value === 1) {
        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.form.isLocked = 1;
        }).catch(() => {
          this.form.isLocked = 0;
        });
      }
    },
    /** 表格中锁定状态改变处理 */
    handleTableLockChange(value, row) {
      if (value === 1) {
        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 调用更新接口
          updateEngineerSampleOrder({
            id: row.id,
            isLocked: 1
          }).then(response => {
            this.msgSuccess("锁定成功");
            this.getList();
            this.loadDashboardStats();
          });
        }).catch(() => {
          // 取消操作，恢复原值
          row.isLocked = 0;
        });
      } else {
        // 解锁操作
        updateEngineerSampleOrder({
          id: row.id,
          isLocked: 0
        }).then(response => {
          this.msgSuccess("解锁成功");
          this.getList();
          this.loadDashboardStats();
        });
      }
    },
    /** 加载统计概览数据 */
    loadDashboardStats() {
      let params = { ...this.queryParams };
      // 添加日期范围筛选参数
      if (this.dateRange && this.dateRange.length === 2) {
        params.beginDateRange = this.dateRange[0];
        params.endDateRange = this.dateRange[1];
      }
      getDashboardStats(params).then(response => {
        this.dashboardStats = response.data || {};
      });
    },
    /** 更改工程师按钮操作 */
    handleChangeEngineer(row) {
      this.changeEngineerForm = {
        id: row.id,
        sampleOrderCode: row.sampleOrderCode,
        currentEngineerName: row.nickName,
        oldEngineerId: row.userId,
        newEngineerId: null,
        scheduledDate: null,
        adjustWorkSchedule: 0
      };

      // 获取可用工程师列表
      getEngineersByDifficultyLevel({
        difficultyLevelId: row.difficultyLevelId,
        categoryId: row.categoryId
      }).then(response => {
        this.engineerOptions = response.data || [];
      });
      // 获取研发所有工程师列表
      getResearchDepartmentsUser().then(response => {
        this.searchedEngineers = response.data || [];
      });
      this.changeEngineerOpen = true;
    },
    /** 提交更改工程师 */
    submitChangeEngineer() {
      this.$refs["changeEngineerForm"].validate(valid => {
        if (valid) {
          const data = {
            sampleOrderId: this.changeEngineerForm.id,
            oldEngineerId: this.changeEngineerForm.oldEngineerId,
            newEngineerId: this.changeEngineerForm.newEngineerId,
            scheduledDate: this.changeEngineerForm.scheduledDate,
            adjustWorkSchedule: this.changeEngineerForm.adjustWorkSchedule
          };

          // 调用更改工程师的API接口
          changeEngineer(data).then(response => {
            this.msgSuccess("更改工程师成功");
            this.changeEngineerOpen = false;
            this.getList();
            // 获取未分配打样单
            this.handleUnassignedOrders();
            this.loadDashboardStats();
          }).catch(() => {
            this.msgError("更改工程师失败");
          });
        }
      });
    },
    /** 获取未分配打样单列表 */
    handleUnassignedOrders() {
      let params = { ...this.queryParams };
      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')
      params = this.addDateRange(params, this.startDateRange,'StartDate')
      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')
      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')
      // 清空之前的日期范围参数，根据当前状态重新设置
      delete params.beginDateRange;
      delete params.endDateRange;
      // 添加日期范围筛选参数
      if (this.dateRange && this.dateRange.length === 2) {
        params.beginDateRange = this.dateRange[0];
        params.endDateRange = this.dateRange[1];
      }
      params.associationStatus = 0
      params.pageNum = this.unassignedQueryParams.pageNum;
      params.pageSize = this.unassignedQueryParams.pageSize;
      listEngineerSampleOrder(params).then(response => {
        this.unassignedOrders = response.rows;
        this.unassignedTotal = response.total;
      });
    },
    /** 分配工程师操作 */
    handleAssignEngineer(row) {
      this.changeEngineerForm = {
        id: row.id,
        sampleOrderCode: row.sampleOrderCode,
        currentEngineerName: '',
        oldEngineerId: null,
        newEngineerId: null,
        scheduledDate: null,
        adjustWorkSchedule: 0
      };

      // 获取可用工程师列表
      getEngineersByDifficultyLevel({
        difficultyLevelId: row.difficultyLevelId,
        categoryId: row.categoryId
      }).then(response => {
        this.engineerOptions = response.data || [];
      });
      // 获取研发所有工程师列表
      getResearchDepartmentsUser().then(response => {
        this.searchedEngineers = response.data || [];
      });
      this.changeEngineerOpen = true;
    },
    /** 删除未分配打样单 */
    handleDeleteUnassigned(row) {
      this.$confirm('是否确认删除打样单编号为"' + row.sampleOrderCode + '"的数据项？').then(function() {
        return delEngineerSampleOrder(row.id);
      }).then(() => {
        this.handleUnassignedOrders();
        this.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 驳回未分配打样单 */
    handleRejectUnassigned(row) {
      this.currentRejectRow = row;
      this.rejectForm.sampleOrderCode = row.sampleOrderCode;
      this.rejectForm.rejectReason = '';
      this.rejectDialogOpen = true;
      this.$nextTick(() => {
        this.$refs.rejectForm.clearValidate();
      });
    },
    /** 未分配打样单分页大小改变 */
    handleUnassignedSizeChange(newSize) {
      this.unassignedQueryParams.pageSize = newSize;
      this.handleUnassignedOrders();
    },
    /** 未分配打样单页码改变 */
    handleUnassignedCurrentChange(newPage) {
      this.unassignedQueryParams.pageNum = newPage;
      this.handleUnassignedOrders();
    },
    /** 切换未分配面板显示状态 */
    toggleUnassignedPanel() {
      this.isUnassignedPanelCollapsed = !this.isUnassignedPanelCollapsed;
    },
    /** 处理状态过滤 */
    handleStatusFilter(status) {
      // 清除逾期过滤
      this.queryParams.isOverdue = null;
      this.queryParams.completionStatus = status;
      this.handleQuery();
    },

    /** 处理逾期任务过滤 */
    handleOverdueFilter() {
      // 清除完成状态过滤
      this.queryParams.completionStatus = null;
      // 设置逾期过滤
      this.queryParams.isOverdue = this.queryParams.isOverdue === 1 ? null : 1;
      this.handleQuery();

      if (this.queryParams.isOverdue === 1) {
        this.$message.info('已筛选显示逾期任务');
      } else {
        this.$message.info('已清除逾期任务筛选');
      }
    },
    /** 获取紧急程度类型 */
    getUrgencyType(endDate, latestStartTime) {
      const now = new Date();
      const end = new Date(endDate);
      const latest = latestStartTime ? new Date(latestStartTime) : null;

      // 计算距离截止日期的天数
      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));

      // 如果有最晚开始时间，检查是否已经超过
      if (latest && now > latest) {
        return 'danger'; // 已超过最晚开始时间
      }

      if (daysToEnd <= 1) {
        return 'danger'; // 紧急：1天内截止
      } else if (daysToEnd <= 3) {
        return 'warning'; // 警告：3天内截止
      } else if (daysToEnd <= 7) {
        return 'primary'; // 一般：7天内截止
      } else {
        return 'success'; // 充足时间
      }
    },
    /** 获取紧急程度文本 */
    getUrgencyText(endDate, latestStartTime) {
      const now = new Date();
      const end = new Date(endDate);
      const latest = latestStartTime ? new Date(latestStartTime) : null;
      // 计算距离截止日期的天数
      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));
      // 如果有最晚开始时间，检查是否已经超过
      if (latest && now > latest) {
        return '超期未开始';
      }

      if (daysToEnd <= 0) {
        return '已逾期';
      } else if (daysToEnd <= 1) {
        return '紧急';
      } else if (daysToEnd <= 3) {
        return '较急';
      } else if (daysToEnd <= 7) {
        return '一般';
      } else {
        return '充足';
      }
    },
    /** 刷新未分配打样单列表 */
    handleRefreshUnassigned() {
      this.handleUnassignedOrders();
      this.$message.success('刷新成功');
    },
    /** 获取逾期信息 */
    getOverdueInfo(row) {
      const now = new Date();
      const endDate = new Date(row.endDate);
      let isOverdue = false;
      let overdueDays = 0;
      // 根据后台SQL逻辑判断逾期
      if (row.completionStatus === 2) {
        // 已完成且逾期：实际完成时间 > 截止日期
        if (row.actualFinishTime) {
          const actualFinishTime = new Date(row.actualFinishTime);
          if (actualFinishTime > endDate) {
            isOverdue = true;
            overdueDays = Math.ceil((actualFinishTime - endDate) / (1000 * 60 * 60 * 24));
          }
        }
      } else if (row.completionStatus === 1) {
        // 进行中且逾期：当前时间 > 截止日期
        if (now > endDate) {
          isOverdue = true;
          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));
        }
      } else if (row.completionStatus === 0) {
        // 未开始且逾期：未开始但当前时间 > 开始日期
        if (now > endDate) {
          isOverdue = true;
          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));
        }
      }

      return {
        isOverdue,
        overdueDays
      };
    },

    // ==================== 批次管理相关方法 ====================

    /** 打开批次管理对话框 */
    handleBatchManagement(row) {
      this.selectedOrderForBatch = row;
      this.currentBatchRow = row; // 保存当前行数据
      this.batchManagementOpen = true;
      this.loadBatchData(row.id);
    },

    /** 加载批次数据 */
    async loadBatchData(engineerSampleOrderId) {
      try {
        // 加载进行中的批次
        const activeBatchesResponse = await this.getActiveBatchesData(engineerSampleOrderId);
        this.activeBatches = activeBatchesResponse.data || [];

        // 为每个进行中的批次加载实验记录
        for (let batch of this.activeBatches) {
          await this.loadBatchExperiments(batch);
        }

        // 加载所有批次
        const batchesResponse = await getBatchesByOrderId(engineerSampleOrderId);
        this.allBatches = batchesResponse.data || [];
      } catch (error) {
        console.error('加载批次数据失败:', error);
        this.$message.error('加载批次数据失败');
      }
    },

    /** 加载批次的实验记录 */
    async loadBatchExperiments(batch) {
      try {
        const response = await getExperimentsByBatchId(batch.id);
        this.$set(batch, 'experiments', response.data || []);
      } catch (error) {
        console.error('加载批次实验记录失败:', error);
        this.$set(batch, 'experiments', []);
      }
    },

    /** 获取进行中的批次 */
    async getActiveBatchesData(engineerSampleOrderId) {
      try {
        // 调用新的API获取进行中的批次
        const response = await getActiveBatches(engineerSampleOrderId);
        return response;
      } catch (error) {
        console.error('获取进行中批次失败:', error);
        return { data: [] };
      }
    },

    /** 开始新批次 */
    async startNewBatch() {
      // 检查编辑权限
      if (!this.canEditBatch) {
        this.$message.warning('当前打样单状态不允许开始新批次，只有状态为"进行中"的打样单才可以编辑批次信息');
        return;
      }

      try {
        await this.$confirm('确认开始新的打样批次？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        const response = await startNewBatch(this.selectedOrderForBatch.id, '');
        this.$message.success('新批次开始成功');

        // 重新加载批次数据
        await this.loadBatchData(this.selectedOrderForBatch.id);

        // 刷新主列表
        this.getList();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('开始新批次失败: ' + (error.message || error));
        }
      }
    },

    /** 开始单个批次 */
    async startSingleBatch(batch) {
      if (!this.canEditBatch) {
        this.$message.warning('当前打样单状态不允许开始批次');
        return;
      }

      try {
        await this.$confirm(`确认开始第${batch.batchIndex}批次？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        await startSingleBatch(batch.id);
        this.$message.success('批次开始成功');

        // 重新加载批次数据
        await this.loadBatchData(this.selectedOrderForBatch.id);
        this.getList();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('开始批次失败: ' + (error.message || error));
        }
      }
    },

    /** 结束单个批次 */
    finishSingleBatch(batch) {
      if (!this.canEditBatch) {
        this.$message.warning('当前打样单状态不允许结束批次');
        return;
      }

      // 设置为单个批次结束模式
      this.finishBatchMode = 'single';
      this.currentFinishBatch = batch;

      // 重置表单
      this.finishBatchForm = {
        qualityEvaluation: '',
        remark: ''
      };

      // 显示对话框
      this.finishBatchOpen = true;
    },

    /** 结束所有进行中批次 */
    finishAllActiveBatches() {
      if (!this.canEditBatch) {
        this.$message.warning('当前打样单状态不允许结束批次');
        return;
      }

      // 设置为结束所有批次模式
      this.finishBatchMode = 'all';
      this.currentFinishBatch = null;

      // 重置表单
      this.finishBatchForm = {
        qualityEvaluation: '',
        remark: ''
      };

      // 显示对话框
      this.finishBatchOpen = true;
    },

    /** 提交结束批次 */
    async submitFinishBatch() {
      try {
        if (this.finishBatchMode === 'single') {
          // 结束单个批次
          await finishSingleBatch(
            this.currentFinishBatch.id,
            this.finishBatchForm.qualityEvaluation || '',
            this.finishBatchForm.remark || '',
            this.currentFinishBatch.laboratoryCode || ''
          );
          this.$message.success('批次结束成功');
        } else {
          // 结束所有进行中的批次
          await finishAllActiveBatches(this.selectedOrderForBatch.id);
          this.$message.success('所有批次结束成功');
        }

        this.finishBatchOpen = false;

        // 重新加载批次数据
        await this.loadBatchData(this.selectedOrderForBatch.id);

        // 刷新主列表
        this.getList();
      } catch (error) {
        console.error('结束批次失败:', error);
        this.$message.error('结束批次失败: ' + (error.message || error));
      }
    },

    /** 查看批次详情 */
    async viewBatchDetail(batch) {
      try {
        const response = await getExperimentsByBatchId(batch.id);
        const experiments = response.data || [];

        // 设置批次详情数据
        this.batchDetailData = {
          ...batch,
          experiments: experiments
        };

        this.batchDetailOpen = true;
      } catch (error) {
        console.error('查看批次详情失败:', error);
        this.$message.error('查看批次详情失败');
      }
    },

    /** 计算持续时间 */
    calculateDuration(startTime) {
      if (!startTime) return '0小时';

      const start = new Date(startTime);
      const now = new Date();
      const diffMs = now - start;
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      if (diffHours > 0) {
        return `${diffHours}小时${diffMinutes}分钟`;
      } else {
        return `${diffMinutes}分钟`;
      }
    },

    /** 编辑实验室编号 */
    editLaboratoryCode(batch) {
      this.$set(batch, 'editingLab', true);
      this.$nextTick(() => {
        // 聚焦到输入框
        const input = this.$el.querySelector(`input[value="${batch.laboratoryCode || ''}"]`);
        if (input) input.focus();
      });
    },

    /** 显示添加实验记录对话框 */
    showAddExperimentDialog(batch) {
      this.currentBatchForExperiment = batch;
      this.addExperimentForm = {
        experimentCode: '',
        experimentNote: '',
        remark: ''
      };
      this.addExperimentOpen = true;
    },

    /** 关闭添加实验记录对话框 */
    closeAddExperiment() {
      this.addExperimentOpen = false;
      this.currentBatchForExperiment = null;
      this.$refs.addExperimentForm && this.$refs.addExperimentForm.resetFields();
    },

    /** 提交添加实验记录 */
    async submitAddExperiment() {
      try {
        await this.$refs.addExperimentForm.validate();

        const experimentRecord = {
          batchId: this.currentBatchForExperiment.id,
          experimentCode: this.addExperimentForm.experimentCode,
          experimentNote: this.addExperimentForm.experimentNote || '',
          remark: this.addExperimentForm.remark
        };

        await addExperimentToBatch(experimentRecord);

        this.$message.success('实验记录添加成功');
        this.addExperimentOpen = false;

        // 重新加载当前批次的实验记录
        await this.loadBatchExperiments(this.currentBatchForExperiment);
      } catch (error) {
        this.$message.error('添加实验记录失败: ' + (error.message || error));
      }
    },

    /** 删除实验记录 */
    async removeExperiment(batch, experiment) {
      try {
        await this.$confirm('确认删除该实验记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        await removeExperimentFromBatch(experiment.id);
        this.$message.success('实验记录删除成功');

        // 重新加载当前批次的实验记录
        await this.loadBatchExperiments(batch);
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除实验记录失败: ' + (error.message || error));
        }
      }
    },

    /** 获取批次状态文本 */
    getBatchStatusText(status) {
      const statusMap = {
        0: '未开始',
        1: '进行中',
        2: '已完成',
        3: '已取消'
      };
      return statusMap[status] || '未知';
    },

    /** 获取批次状态类型 */
    getBatchStatusType(status) {
      const typeMap = {
        0: 'info',
        1: 'success',
        2: 'primary',
        3: 'danger'
      };
      return typeMap[status] || 'info';
    },

    /** 获取质量评价类型 */
    getQualityEvaluationType(evaluation) {
      if (!evaluation) return '';

      const lowerEval = evaluation.toLowerCase();
      if (lowerEval.includes('优秀') || lowerEval.includes('良好') || lowerEval.includes('好')) {
        return 'success';
      } else if (lowerEval.includes('一般') || lowerEval.includes('中等')) {
        return 'warning';
      } else if (lowerEval.includes('差') || lowerEval.includes('不合格') || lowerEval.includes('失败')) {
        return 'danger';
      } else {
        return 'info';
      }
    },

    /** 关闭批次管理对话框并清空数据 */
    closeBatchManagement() {
      // 清空批次管理相关的数据
      this.activeBatches = [];
      this.allBatches = [];
      this.selectedOrderForBatch = null;
      this.currentBatchRow = null; // 清空当前行数据
      this.batchDetailData = null;

      this.finishBatchForm = {
        qualityEvaluation: '',
        remark: ''
      };

      // 关闭所有相关的子对话框
      this.batchDetailOpen = false;
      this.addExperimentOpen = false;
      this.finishBatchOpen = false;
    },

    /** 关闭批次详情对话框并清空数据 */
    closeBatchDetail() {
      // 清空批次详情数据
      this.batchDetailData = null;
    },

    /** 关闭结束批次对话框并清空数据 */
    closeFinishBatch() {
      // 清空结束批次表单数据
      this.finishBatchOpen = false;
      this.finishBatchMode = 'all';
      this.currentFinishBatch = null;
      this.finishBatchForm = {
        qualityEvaluation: '',
        remark: ''
      };
      // 清除表单验证状态
      if (this.$refs.finishBatchForm) {
        this.$refs.finishBatchForm.clearValidate();
      }
    },

    /** 刷新批次数据 */
    async refreshBatchData() {
      if (this.selectedOrderForBatch) {
        await this.loadBatchData(this.selectedOrderForBatch.id);
        this.$message.success('批次数据已刷新');
      }
    },

    /** 加载实验室编码列表 */
    async loadExperimentCodeList(engineerSampleOrderId) {
      try {
        const response = await getBatchExperimentCodeList(engineerSampleOrderId);
        this.experimentCodeList = response.data || [];
      } catch (error) {
        console.error('加载实验室编码列表失败:', error);
        this.experimentCodeList = [];
        this.$message.error('加载实验室编码列表失败');
      }
    }
  }
};
</script>

<style scoped>
.stats-row {
  margin-bottom: 20px;
}
.stats-card {
  border: none;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 批次详情样式 */
.batch-detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.batch-info-card {
  border: 1px solid #EBEEF5;
  border-radius: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item label {
  font-weight: 600;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.info-value {
  color: #303133;
  font-size: 14px;
}

.remark-content {
  background-color: #F5F7FA;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
  color: #606266;
  line-height: 1.5;
  margin-top: 8px;
  min-height: 40px;
}

.experiments-card {
  border: 1px solid #EBEEF5;
  border-radius: 8px;
}

.empty-state, .empty-experiments {
  background-color: #FAFAFA;
  border-radius: 8px;
  margin: 10px 0;
}

/* 历史批次表格样式优化 */
.history-batches-card .el-table {
  border-radius: 8px;
  overflow: hidden;
}

.history-batches-card .el-table th {
  background-color: #F5F7FA;
  color: #606266;
  font-weight: 600;
}

.history-batches-card .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #FAFAFA;
}

.stats-card:not(.total) {
  cursor: pointer;
}

.stats-card:not(.total):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stats-card.active {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
}

.stats-card.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: currentColor;
}

.stats-card.completed.active::after {
  background: #67C23A;
}

.stats-card.in-progress.active::after {
  background: #409EFF;
}

.stats-card.overdue.active::after {
  background: #F56C6C;
}

.stats-card.overdue .stats-icon {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 20px;
}
.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}
.stats-icon i {
  font-size: 24px;
  color: white;
}
.stats-info {
  flex: 1;
}
.stats-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}
.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}
.stats-card.total .stats-icon {
  background: linear-gradient(135deg, #3a7bd5, #3a6073);
}
.stats-card.completed .stats-icon {
  background: linear-gradient(135deg, #E6A23C, #F0C78A);
}
.stats-card.in-progress .stats-icon {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}
.stats-card.overdue .stats-icon {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}
@media (max-width: 768px) {
  .stats-card .stats-content {
    padding: 15px;
  }
  .stats-icon {
    width: 50px;
    height: 50px;
    margin-right: 15px;
  }
  .stats-icon i {
    font-size: 20px;
  }
  .stats-number {
    font-size: 24px;
  }
}
.other-engineer-select {
  margin-top: 10px;
}
.select-item {
  width: 100%;
}

/* 未分配打样单告警样式 */
.alert-card {
  margin-bottom: 20px;
}
.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.alert-title {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.alert-title span {
  font-size: 16px;
  font-weight: bold;
  color: #FF1414;
  display: flex;
  align-items: center;
}
.alert-actions {
  display: flex;
  align-items: center;
}
.alert-cards {
  margin-top: 20px;
}
.alert-item {
  margin-bottom: 20px;
  transition: all 0.3s;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  height: 225px;
  display: flex;
  flex-direction: column;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}
.alert-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  border-color: #409EFF;
}
.alert-item-header {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f2f5;
  background: rgba(64, 158, 255, 0.02);
  margin: -16px -16px 12px -16px;
  padding: 12px 16px;
  border-radius: 6px 6px 0 0;
}
.order-code-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.order-code {
  font-size: 15px;
  font-weight: 500;
  color: #303133;
}
.urgency-tag {
  margin-left: 8px;
}
.alert-item-content {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-height: 0;
}
.info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.info-row {
  margin-bottom: 8px;
}
.info-row-double {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}
.info-row-double .info-item {
  flex: 1;
  min-width: 0;
}
.info-row-double .info-item.date-time-item {
  flex: 1.5;
}
.info-row-double .info-item.standard-item {
  flex: 1;
}
.info-item {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 13px;
}
.info-label {
  margin-left: 6px;
  margin-right: 4px;
  color: #909399;
  font-size: 12px;
}
.info-value {
  color: #303133;
  font-weight: 500;
}
.difficulty-text {
  cursor: pointer;
  border-bottom: 1px dashed #ccc;
}
.info-reason {
  display: flex;
  align-items: flex-start;
  color: #f56c6c;
  margin-top: 8px;
  padding: 6px 8px;
  background-color: #fef0f0;
  border-radius: 4px;
  border-left: 3px solid #f56c6c;
  font-size: 12px;
}
.reason-text {
  margin-left: 4px;
  line-height: 1.4;
  word-break: break-word;
}
.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
}
.info-reason span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.info-item i,
.info-date i,
.info-reason i {
  margin-right: 6px;
  font-size: 14px;
}
.alert-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding: 12px 0 0 0;
  border-top: 1px solid #f0f2f5;
  flex-shrink: 0;
  background: rgba(250, 251, 252, 0.5);
  border-radius: 0 0 6px 6px;
  margin-left: -16px;
  margin-right: -16px;
  padding-left: 16px;
  padding-right: 16px;
}
.alert-item-footer .el-button {
  border-radius: 4px;
  font-weight: 500;
}
.alert-item-footer .el-button--primary {
  background: #409EFF;
  border-color: #409EFF;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}
.alert-item-footer .el-button--primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
}
.alert-item-footer .el-button--text {
  color: #909399;
  transition: all 0.3s;
}
.alert-item-footer .el-button--text:hover {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
}
.delete-btn:hover {
  color: #f56c6c !important;
}

/* 确保卡片内容区域的统一布局 */
.alert-item .el-card__body {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  position: relative;
}

/* 确保内容区域占据剩余空间 */
.alert-item .alert-item-header {
  flex-shrink: 0;
}

.alert-item .alert-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.alert-item .alert-item-footer {
  flex-shrink: 0;
  margin-top: auto;
}

.alert-item .info-group {
  margin-bottom: 8px;
}

.alert-item .info-date {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alert-item {
    height: auto;
    min-height: 200px;
  }

  .info-row-double {
    flex-direction: column;
    gap: 8px;
  }

  .alert-item-footer {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .alert-item-footer .el-button {
    width: 100%;
  }
}

/* 批次管理相关样式 */
.batch-management-container {
  max-height: 600px;
  overflow-y: auto;
}

.current-batch-card {
  margin-bottom: 20px;
}

.batch-info-item {
  margin-bottom: 10px;
}

.batch-info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.experiment-section {
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header span {
  font-weight: bold;
  color: #303133;
}

.history-batches-card {
  margin-top: 20px;
}

.new-batch-section {
  padding: 40px 0;
  text-align: center;
  background-color: #fafafa;
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
}

.new-batch-section .el-button {
  padding: 12px 30px;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-management-container {
    max-height: 500px;
  }

  .batch-info-item {
    margin-bottom: 8px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .section-header .el-button {
    margin-top: 10px;
  }
}

/* 备注文本省略样式 */
.remark-text-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: 2.8em; /* 2行的高度，基于line-height */
  word-break: break-word;
  white-space: normal;
  cursor: pointer;
}

.remark-text-ellipsis:hover {
  color: #409EFF;
}

/* 导出选择对话框样式 */
.export-options {
  padding: 10px 0;
}

.export-options .el-checkbox {
  width: 100%;
  margin-right: 0;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s;
}

.export-options .el-checkbox:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.export-options .el-checkbox.is-checked {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.export-options .el-checkbox__label {
  width: 100%;
  padding-left: 10px;
}

/* 实验记录相关样式 */
.experiment-codes {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 4px;
  min-height: 32px;
}

.experiment-codes .el-tag {
  margin: 2px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.experiment-codes .el-button {
  margin: 2px;
  padding: 4px 8px;
  font-size: 12px;
}
</style>
