{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue?vue&type=style&index=0&id=b582a56c&scoped=true&lang=css", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue", "mtime": 1754285363614}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1744596528942}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1744596529996}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnN0YXRzLXJvdyB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQouc3RhdHMtY2FyZCB7DQogIGJvcmRlcjogbm9uZTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KfQ0KDQovKiDmibnmrKHor6bmg4XmoLflvI8gKi8NCi5iYXRjaC1kZXRhaWwtY29udGFpbmVyIHsNCiAgbWF4LWhlaWdodDogNzB2aDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCn0NCg0KLmJhdGNoLWluZm8tY2FyZCB7DQogIGJvcmRlcjogMXB4IHNvbGlkICNFQkVFRjU7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg0KLmluZm8taXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCn0NCg0KLmluZm8taXRlbSBsYWJlbCB7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgbWluLXdpZHRoOiA4MHB4Ow0KfQ0KDQouaW5mby12YWx1ZSB7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5yZW1hcmstY29udGVudCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNGNUY3RkE7DQogIHBhZGRpbmc6IDEycHg7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjNDA5RUZGOw0KICBjb2xvcjogIzYwNjI2NjsNCiAgbGluZS1oZWlnaHQ6IDEuNTsNCiAgbWFyZ2luLXRvcDogOHB4Ow0KICBtaW4taGVpZ2h0OiA0MHB4Ow0KfQ0KDQouZXhwZXJpbWVudHMtY2FyZCB7DQogIGJvcmRlcjogMXB4IHNvbGlkICNFQkVFRjU7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg0KLmVtcHR5LXN0YXRlLCAuZW1wdHktZXhwZXJpbWVudHMgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjRkFGQUZBOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIG1hcmdpbjogMTBweCAwOw0KfQ0KDQovKiDljoblj7LmibnmrKHooajmoLzmoLflvI/kvJjljJYgKi8NCi5oaXN0b3J5LWJhdGNoZXMtY2FyZCAuZWwtdGFibGUgew0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5oaXN0b3J5LWJhdGNoZXMtY2FyZCAuZWwtdGFibGUgdGggew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjVGN0ZBOw0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLmhpc3RvcnktYmF0Y2hlcy1jYXJkIC5lbC10YWJsZS0tc3RyaXBlZCAuZWwtdGFibGVfX2JvZHkgdHIuZWwtdGFibGVfX3Jvdy0tc3RyaXBlZCB0ZCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNGQUZBRkE7DQp9DQoNCi5zdGF0cy1jYXJkOm5vdCgudG90YWwpIHsNCiAgY3Vyc29yOiBwb2ludGVyOw0KfQ0KDQouc3RhdHMtY2FyZDpub3QoLnRvdGFsKTpob3ZlciB7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsNCiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMTUpOw0KfQ0KDQouc3RhdHMtY2FyZC5hY3RpdmUgew0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQogIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjE1KTsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KfQ0KDQouc3RhdHMtY2FyZC5hY3RpdmU6OmFmdGVyIHsNCiAgY29udGVudDogJyc7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgYm90dG9tOiAwOw0KICBsZWZ0OiAwOw0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAzcHg7DQogIGJhY2tncm91bmQ6IGN1cnJlbnRDb2xvcjsNCn0NCg0KLnN0YXRzLWNhcmQuY29tcGxldGVkLmFjdGl2ZTo6YWZ0ZXIgew0KICBiYWNrZ3JvdW5kOiAjNjdDMjNBOw0KfQ0KDQouc3RhdHMtY2FyZC5pbi1wcm9ncmVzcy5hY3RpdmU6OmFmdGVyIHsNCiAgYmFja2dyb3VuZDogIzQwOUVGRjsNCn0NCg0KLnN0YXRzLWNhcmQub3ZlcmR1ZS5hY3RpdmU6OmFmdGVyIHsNCiAgYmFja2dyb3VuZDogI0Y1NkM2QzsNCn0NCg0KLnN0YXRzLWNhcmQub3ZlcmR1ZSAuc3RhdHMtaWNvbiB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNGNTZDNkMsICNGNzg5ODkpOw0KfQ0KDQouc3RhdHMtY29udGVudCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQouc3RhdHMtaWNvbiB7DQogIHdpZHRoOiA2MHB4Ow0KICBoZWlnaHQ6IDYwcHg7DQogIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIG1hcmdpbi1yaWdodDogMjBweDsNCn0NCi5zdGF0cy1pY29uIGkgew0KICBmb250LXNpemU6IDI0cHg7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCi5zdGF0cy1pbmZvIHsNCiAgZmxleDogMTsNCn0NCi5zdGF0cy10aXRsZSB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM2NjY7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCn0NCi5zdGF0cy1udW1iZXIgew0KICBmb250LXNpemU6IDI4cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzMzMzsNCn0NCi5zdGF0cy1jYXJkLnRvdGFsIC5zdGF0cy1pY29uIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzNhN2JkNSwgIzNhNjA3Myk7DQp9DQouc3RhdHMtY2FyZC5jb21wbGV0ZWQgLnN0YXRzLWljb24gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjRTZBMjNDLCAjRjBDNzhBKTsNCn0NCi5zdGF0cy1jYXJkLmluLXByb2dyZXNzIC5zdGF0cy1pY29uIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzQwOUVGRiwgIzY2QjFGRik7DQp9DQouc3RhdHMtY2FyZC5vdmVyZHVlIC5zdGF0cy1pY29uIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI0Y1NkM2QywgI0Y3ODk4OSk7DQp9DQpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsNCiAgLnN0YXRzLWNhcmQgLnN0YXRzLWNvbnRlbnQgew0KICAgIHBhZGRpbmc6IDE1cHg7DQogIH0NCiAgLnN0YXRzLWljb24gew0KICAgIHdpZHRoOiA1MHB4Ow0KICAgIGhlaWdodDogNTBweDsNCiAgICBtYXJnaW4tcmlnaHQ6IDE1cHg7DQogIH0NCiAgLnN0YXRzLWljb24gaSB7DQogICAgZm9udC1zaXplOiAyMHB4Ow0KICB9DQogIC5zdGF0cy1udW1iZXIgew0KICAgIGZvbnQtc2l6ZTogMjRweDsNCiAgfQ0KfQ0KLm90aGVyLWVuZ2luZWVyLXNlbGVjdCB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQp9DQouc2VsZWN0LWl0ZW0gew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLyog5pyq5YiG6YWN5omT5qC35Y2V5ZGK6K2m5qC35byPICovDQouYWxlcnQtY2FyZCB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQouYWxlcnQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KLmFsZXJ0LXRpdGxlIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgY3Vyc29yOiBwb2ludGVyOw0KfQ0KLmFsZXJ0LXRpdGxlIHNwYW4gew0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogI0ZGMTQxNDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCi5hbGVydC1hY3Rpb25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCi5hbGVydC1jYXJkcyB7DQogIG1hcmdpbi10b3A6IDIwcHg7DQp9DQouYWxlcnQtaXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7DQogIGhlaWdodDogMjI1cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMDUpOw0KfQ0KLmFsZXJ0LWl0ZW06aG92ZXIgew0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQogIGJveC1zaGFkb3c6IDAgNnB4IDE2cHggcmdiYSgwLCAwLCAwLCAwLjEyKTsNCiAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGOw0KfQ0KLmFsZXJ0LWl0ZW0taGVhZGVyIHsNCiAgbWFyZ2luLWJvdHRvbTogMTJweDsNCiAgcGFkZGluZy1ib3R0b206IDEycHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMmY1Ow0KICBiYWNrZ3JvdW5kOiByZ2JhKDY0LCAxNTgsIDI1NSwgMC4wMik7DQogIG1hcmdpbjogLTE2cHggLTE2cHggMTJweCAtMTZweDsNCiAgcGFkZGluZzogMTJweCAxNnB4Ow0KICBib3JkZXItcmFkaXVzOiA2cHggNnB4IDAgMDsNCn0NCi5vcmRlci1jb2RlLXNlY3Rpb24gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoub3JkZXItY29kZSB7DQogIGZvbnQtc2l6ZTogMTVweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgY29sb3I6ICMzMDMxMzM7DQp9DQoudXJnZW5jeS10YWcgew0KICBtYXJnaW4tbGVmdDogOHB4Ow0KfQ0KLmFsZXJ0LWl0ZW0tY29udGVudCB7DQogIHBhZGRpbmc6IDA7DQogIGZsZXg6IDE7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsNCiAgbWluLWhlaWdodDogMDsNCn0NCi5pbmZvLXNlY3Rpb24gew0KICBmbGV4OiAxOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7DQp9DQouaW5mby1yb3cgew0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQouaW5mby1yb3ctZG91YmxlIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBnYXA6IDEycHg7DQp9DQouaW5mby1yb3ctZG91YmxlIC5pbmZvLWl0ZW0gew0KICBmbGV4OiAxOw0KICBtaW4td2lkdGg6IDA7DQp9DQouaW5mby1yb3ctZG91YmxlIC5pbmZvLWl0ZW0uZGF0ZS10aW1lLWl0ZW0gew0KICBmbGV4OiAxLjU7DQp9DQouaW5mby1yb3ctZG91YmxlIC5pbmZvLWl0ZW0uc3RhbmRhcmQtaXRlbSB7DQogIGZsZXg6IDE7DQp9DQouaW5mby1pdGVtIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGZvbnQtc2l6ZTogMTNweDsNCn0NCi5pbmZvLWxhYmVsIHsNCiAgbWFyZ2luLWxlZnQ6IDZweDsNCiAgbWFyZ2luLXJpZ2h0OiA0cHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBmb250LXNpemU6IDEycHg7DQp9DQouaW5mby12YWx1ZSB7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KLmRpZmZpY3VsdHktdGV4dCB7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IGRhc2hlZCAjY2NjOw0KfQ0KLmluZm8tcmVhc29uIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7DQogIGNvbG9yOiAjZjU2YzZjOw0KICBtYXJnaW4tdG9wOiA4cHg7DQogIHBhZGRpbmc6IDZweCA4cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZWYwZjA7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgYm9yZGVyLWxlZnQ6IDNweCBzb2xpZCAjZjU2YzZjOw0KICBmb250LXNpemU6IDEycHg7DQp9DQoucmVhc29uLXRleHQgew0KICBtYXJnaW4tbGVmdDogNHB4Ow0KICBsaW5lLWhlaWdodDogMS40Ow0KICB3b3JkLWJyZWFrOiBicmVhay13b3JkOw0KfQ0KLnRleHQtb3ZlcmZsb3cgew0KICBvdmVyZmxvdzogaGlkZGVuOw0KICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgZmxleDogMTsNCiAgbWluLXdpZHRoOiAwOw0KfQ0KLmluZm8tcmVhc29uIHNwYW4gew0KICBvdmVyZmxvdzogaGlkZGVuOw0KICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCn0NCi5pbmZvLWl0ZW0gaSwNCi5pbmZvLWRhdGUgaSwNCi5pbmZvLXJlYXNvbiBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA2cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCi5hbGVydC1pdGVtLWZvb3RlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLXRvcDogYXV0bzsNCiAgcGFkZGluZzogMTJweCAwIDAgMDsNCiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNmMGYyZjU7DQogIGZsZXgtc2hyaW5rOiAwOw0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1MCwgMjUxLCAyNTIsIDAuNSk7DQogIGJvcmRlci1yYWRpdXM6IDAgMCA2cHggNnB4Ow0KICBtYXJnaW4tbGVmdDogLTE2cHg7DQogIG1hcmdpbi1yaWdodDogLTE2cHg7DQogIHBhZGRpbmctbGVmdDogMTZweDsNCiAgcGFkZGluZy1yaWdodDogMTZweDsNCn0NCi5hbGVydC1pdGVtLWZvb3RlciAuZWwtYnV0dG9uIHsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KLmFsZXJ0LWl0ZW0tZm9vdGVyIC5lbC1idXR0b24tLXByaW1hcnkgew0KICBiYWNrZ3JvdW5kOiAjNDA5RUZGOw0KICBib3JkZXItY29sb3I6ICM0MDlFRkY7DQogIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDY0LCAxNTgsIDI1NSwgMC4zKTsNCn0NCi5hbGVydC1pdGVtLWZvb3RlciAuZWwtYnV0dG9uLS1wcmltYXJ5OmhvdmVyIHsNCiAgYmFja2dyb3VuZDogIzY2YjFmZjsNCiAgYm9yZGVyLWNvbG9yOiAjNjZiMWZmOw0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7DQogIGJveC1zaGFkb3c6IDAgNHB4IDhweCByZ2JhKDY0LCAxNTgsIDI1NSwgMC40KTsNCn0NCi5hbGVydC1pdGVtLWZvb3RlciAuZWwtYnV0dG9uLS10ZXh0IHsNCiAgY29sb3I6ICM5MDkzOTk7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzOw0KfQ0KLmFsZXJ0LWl0ZW0tZm9vdGVyIC5lbC1idXR0b24tLXRleHQ6aG92ZXIgew0KICBjb2xvcjogI2Y1NmM2YzsNCiAgYmFja2dyb3VuZDogcmdiYSgyNDUsIDEwOCwgMTA4LCAwLjEpOw0KfQ0KLmRlbGV0ZS1idG46aG92ZXIgew0KICBjb2xvcjogI2Y1NmM2YyAhaW1wb3J0YW50Ow0KfQ0KDQovKiDnoa7kv53ljaHniYflhoXlrrnljLrln5/nmoTnu5/kuIDluIPlsYAgKi8NCi5hbGVydC1pdGVtIC5lbC1jYXJkX19ib2R5IHsNCiAgaGVpZ2h0OiAxMDAlOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBwYWRkaW5nOiAxNnB4Ow0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQp9DQoNCi8qIOehruS/neWGheWuueWMuuWfn+WNoOaNruWJqeS9meepuumXtCAqLw0KLmFsZXJ0LWl0ZW0gLmFsZXJ0LWl0ZW0taGVhZGVyIHsNCiAgZmxleC1zaHJpbms6IDA7DQp9DQoNCi5hbGVydC1pdGVtIC5hbGVydC1pdGVtLWNvbnRlbnQgew0KICBmbGV4OiAxOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBtaW4taGVpZ2h0OiAwOw0KfQ0KDQouYWxlcnQtaXRlbSAuYWxlcnQtaXRlbS1mb290ZXIgew0KICBmbGV4LXNocmluazogMDsNCiAgbWFyZ2luLXRvcDogYXV0bzsNCn0NCg0KLmFsZXJ0LWl0ZW0gLmluZm8tZ3JvdXAgew0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi5hbGVydC1pdGVtIC5pbmZvLWRhdGUgew0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi8qIOWTjeW6lOW8j+iuvuiuoSAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5hbGVydC1pdGVtIHsNCiAgICBoZWlnaHQ6IGF1dG87DQogICAgbWluLWhlaWdodDogMjAwcHg7DQogIH0NCg0KICAuaW5mby1yb3ctZG91YmxlIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGdhcDogOHB4Ow0KICB9DQoNCiAgLmFsZXJ0LWl0ZW0tZm9vdGVyIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGdhcDogOHB4Ow0KICAgIGFsaWduLWl0ZW1zOiBzdHJldGNoOw0KICB9DQoNCiAgLmFsZXJ0LWl0ZW0tZm9vdGVyIC5lbC1idXR0b24gew0KICAgIHdpZHRoOiAxMDAlOw0KICB9DQp9DQoNCi8qIOaJueasoeeuoeeQhuebuOWFs+agt+W8jyAqLw0KLmJhdGNoLW1hbmFnZW1lbnQtY29udGFpbmVyIHsNCiAgbWF4LWhlaWdodDogNjAwcHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQoNCi5jdXJyZW50LWJhdGNoLWNhcmQgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQouYmF0Y2gtaW5mby1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLmJhdGNoLWluZm8taXRlbSBsYWJlbCB7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzYwNjI2NjsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQp9DQoNCi5leHBlcmltZW50LXNlY3Rpb24gew0KICBib3JkZXItdG9wOiAxcHggc29saWQgI2ViZWVmNTsNCiAgcGFkZGluZy10b3A6IDE1cHg7DQp9DQoNCi5zZWN0aW9uLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLnNlY3Rpb24taGVhZGVyIHNwYW4gew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICMzMDMxMzM7DQp9DQoNCi5oaXN0b3J5LWJhdGNoZXMtY2FyZCB7DQogIG1hcmdpbi10b3A6IDIwcHg7DQp9DQoNCi5uZXctYmF0Y2gtc2VjdGlvbiB7DQogIHBhZGRpbmc6IDQwcHggMDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhOw0KICBib3JkZXI6IDJweCBkYXNoZWQgI2RjZGZlNjsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KfQ0KDQoubmV3LWJhdGNoLXNlY3Rpb24gLmVsLWJ1dHRvbiB7DQogIHBhZGRpbmc6IDEycHggMzBweDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQovKiDlk43lupTlvI/orr7orqEgKi8NCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAuYmF0Y2gtbWFuYWdlbWVudC1jb250YWluZXIgew0KICAgIG1heC1oZWlnaHQ6IDUwMHB4Ow0KICB9DQoNCiAgLmJhdGNoLWluZm8taXRlbSB7DQogICAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICB9DQoNCiAgLnNlY3Rpb24taGVhZGVyIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICB9DQoNCiAgLnNlY3Rpb24taGVhZGVyIC5lbC1idXR0b24gew0KICAgIG1hcmdpbi10b3A6IDEwcHg7DQogIH0NCn0NCg0KLyog5aSH5rOo5paH5pys55yB55Wl5qC35byPICovDQoucmVtYXJrLXRleHQtZWxsaXBzaXMgew0KICBkaXNwbGF5OiAtd2Via2l0LWJveDsNCiAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDsNCiAgLXdlYmtpdC1saW5lLWNsYW1wOiAyOw0KICBsaW5lLWNsYW1wOiAyOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgbGluZS1oZWlnaHQ6IDEuNDsNCiAgbWF4LWhlaWdodDogMi44ZW07IC8qIDLooYznmoTpq5jluqbvvIzln7rkuo5saW5lLWhlaWdodCAqLw0KICB3b3JkLWJyZWFrOiBicmVhay13b3JkOw0KICB3aGl0ZS1zcGFjZTogbm9ybWFsOw0KICBjdXJzb3I6IHBvaW50ZXI7DQp9DQoNCi5yZW1hcmstdGV4dC1lbGxpcHNpczpob3ZlciB7DQogIGNvbG9yOiAjNDA5RUZGOw0KfQ0KDQovKiDlr7zlh7rpgInmi6nlr7nor53moYbmoLflvI8gKi8NCi5leHBvcnQtb3B0aW9ucyB7DQogIHBhZGRpbmc6IDEwcHggMDsNCn0NCg0KLmV4cG9ydC1vcHRpb25zIC5lbC1jaGVja2JveCB7DQogIHdpZHRoOiAxMDAlOw0KICBtYXJnaW4tcmlnaHQ6IDA7DQogIHBhZGRpbmc6IDE1cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3M7DQp9DQoNCi5leHBvcnQtb3B0aW9ucyAuZWwtY2hlY2tib3g6aG92ZXIgew0KICBib3JkZXItY29sb3I6ICM0MDllZmY7DQogIGJhY2tncm91bmQtY29sb3I6ICNmMGY5ZmY7DQp9DQoNCi5leHBvcnQtb3B0aW9ucyAuZWwtY2hlY2tib3guaXMtY2hlY2tlZCB7DQogIGJvcmRlci1jb2xvcjogIzQwOWVmZjsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2VjZjVmZjsNCn0NCg0KLmV4cG9ydC1vcHRpb25zIC5lbC1jaGVja2JveF9fbGFiZWwgew0KICB3aWR0aDogMTAwJTsNCiAgcGFkZGluZy1sZWZ0OiAxMHB4Ow0KfQ0KDQovKiDlrp7pqozorrDlvZXnm7jlhbPmoLflvI8gKi8NCi5leHBlcmltZW50LWNvZGVzIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgZ2FwOiA0cHg7DQogIG1pbi1oZWlnaHQ6IDMycHg7DQp9DQoNCi5leHBlcmltZW50LWNvZGVzIC5lbC10YWcgew0KICBtYXJnaW46IDJweDsNCiAgbWF4LXdpZHRoOiAxMjBweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogIHdoaXRlLXNwYWNlOiBub3dyYXA7DQp9DQoNCi5leHBlcmltZW50LWNvZGVzIC5lbC1idXR0b24gew0KICBtYXJnaW46IDJweDsNCiAgcGFkZGluZzogNHB4IDhweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs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file": "index.vue", "sourceRoot": "src/views/software/engineerSampleOrder", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 统计概览 -->\r\n    <el-row :gutter=\"20\" class=\"stats-row\" style=\"margin-bottom: 20px;\">\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card total\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-s-order\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">总任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.total || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card completed\" @click.native=\"handleStatusFilter('2')\" :class=\"{'active': queryParams.completionStatus === '2'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-check\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">已完成</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.completed || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card in-progress\" @click.native=\"handleStatusFilter('1')\" :class=\"{'active': queryParams.completionStatus === '1'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-loading\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">进行中</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.inProgress || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card overdue\" @click.native=\"handleOverdueFilter\" :class=\"{'active': queryParams.isOverdue === 1}\" style=\"cursor: pointer;\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-warning\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">逾期任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.overdue || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 未分配工程师的打样单告警 -->\r\n    <el-card class=\"alert-card\">\r\n      <div slot=\"header\" class=\"alert-header\">\r\n        <div class=\"alert-title\" @click=\"toggleUnassignedPanel\">\r\n          <span><i class=\"el-icon-warning-outline\"></i>待分配打样单({{ unassignedTotal }}个)</span>\r\n        </div>\r\n        <div class=\"alert-actions\">\r\n          <el-button\r\n            type=\"text\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"handleRefreshUnassigned\"\r\n            style=\"margin-right: 10px\"\r\n            title=\"刷新列表\">\r\n            刷新\r\n          </el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            @click=\"toggleUnassignedPanel\"\r\n            style=\"margin-right: 10px\">\r\n            {{ isUnassignedPanelCollapsed ? '展开' : '收起' }}\r\n            <i :class=\"isUnassignedPanelCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\r\n          </el-button>\r\n          <el-pagination\r\n            @size-change=\"handleUnassignedSizeChange\"\r\n            @current-change=\"handleUnassignedCurrentChange\"\r\n            :current-page=\"unassignedQueryParams.pageNum\"\r\n            :page-sizes=\"[8, 16, 24, 32]\"\r\n            :page-size=\"unassignedQueryParams.pageSize\"\r\n            layout=\"total, sizes, prev, pager, next\"\r\n            :total=\"unassignedTotal\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n      <el-collapse-transition>\r\n        <div v-show=\"!isUnassignedPanelCollapsed\">\r\n          <el-row :gutter=\"20\" class=\"alert-cards\">\r\n            <el-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\" v-for=\"item in unassignedOrders\" :key=\"item.id\">\r\n              <el-card shadow=\"hover\" class=\"alert-item\">\r\n                <div class=\"alert-item-header\">\r\n                  <div class=\"order-code-section\">\r\n                    <span style=\"color: #00afff;cursor: pointer\"  @click=\"orderDetail(item)\" class=\"order-code\">{{ item.sampleOrderCode }}</span>\r\n                    <el-tag\r\n                      :type=\"getUrgencyType(item.endDate, item.latestStartTime)\"\r\n                      size=\"mini\"\r\n                      class=\"urgency-tag\">\r\n                      {{ getUrgencyText(item.endDate, item.latestStartTime) }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n                <div class=\"alert-item-content\">\r\n                  <div class=\"info-section\">\r\n                    <!-- 预估工时和难度等级 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\" v-if=\"item.latestStartTime\">\r\n                        <i class=\"el-icon-alarm-clock\" style=\"color: #909399;\"></i>\r\n                        <span class=\"info-label\">最晚开始:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-time\" style=\"color: #409EFF;\"></i>\r\n                        <span class=\"info-label\">预估工时:</span>\r\n                        <span class=\"info-value\">{{ item.estimatedManHours || '-' }}H</span>\r\n                      </div>\r\n                    </div>\r\n                    <!-- 最晚开始日期和截止日期排 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\">\r\n                        <i class=\"el-icon-date\" style=\"color: #F56C6C;\"></i>\r\n                        <span class=\"info-label\">截止日期:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.endDate, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-star-on\" style=\"color: #E6A23C;\"></i>\r\n                        <span class=\"info-label\">难度等级:</span>\r\n                        <el-tooltip :content=\"selectDictLabel(dylbOptions, item.difficultyLevelId)\" placement=\"top\">\r\n                          <span class=\"info-value difficulty-text\">{{selectDictLabel(dylbOptions,item.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n                        </el-tooltip>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <el-tooltip v-if=\"item.failureReason\" :content=\"item.failureReason\" placement=\"top\">\r\n                    <div class=\"info-reason\">\r\n                      <i class=\"el-icon-warning-outline\"></i>\r\n                      <span class=\"reason-text\">{{ item.failureReason }}</span>\r\n                    </div>\r\n                  </el-tooltip>\r\n                </div>\r\n                <div class=\"alert-item-footer\">\r\n                  <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-user-solid\" @click=\"handleAssignEngineer(item)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\">\r\n                    分配工程师\r\n                  </el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-close\" @click=\"handleRejectUnassigned(item)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" class=\"reject-btn\" style=\"color: #F56C6C;\">\r\n                    驳回\r\n                  </el-button>\r\n                </div>\r\n              </el-card>\r\n           </el-col>\r\n          </el-row>\r\n         </div>\r\n      </el-collapse-transition>\r\n    </el-card>\r\n\r\n    <!-- 筛选条件 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"客户\" prop=\"customerId\">\r\n        <el-select v-model=\"queryParams.customerId\" filterable placeholder=\"客户\" clearable size=\"small\">\r\n          <el-option\r\n            v-for=\"dict in customerOptions\"\r\n            :key=\"dict.id\"\r\n            :label=\"dict.name\"\r\n            :value=\"dict.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"日期范围\" prop=\"dateRange\" label-width=\"80px\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"\r\n          style=\"width: 240px\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n        <el-date-picker\r\n          v-model=\"scheduledDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"申请日期\" prop=\"startDate\">\r\n        <el-date-picker\r\n          v-model=\"startDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"研发组别\" prop=\"deptIds\">\r\n        <el-select v-model=\"queryParams.deptIds\" multiple filterable placeholder=\"请选择对应研发组别\">\r\n          <el-option\r\n            v-for=\"dept in researchDeptDatas\"\r\n            :key=\"dept.deptId\"\r\n            :label=\"dept.deptName\"\r\n            :value=\"dept.deptId\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"服务模式\" prop=\"serviceMode\">\r\n        <el-select v-model=\"queryParams.serviceMode\" placeholder=\"请选择客户服务模式\" clearable>\r\n          <el-option\r\n            v-for=\"dict in serviceModeOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"实验室\" prop=\"laboratory\">\r\n        <el-select v-model=\"queryParams.laboratory\" placeholder=\"请选择实验室\" clearable>\r\n          <el-option label=\"宜侬\" value=\"0\" />\r\n          <el-option label=\"瀛彩\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"确认编码\" prop=\"confirmCode\">\r\n        <el-input\r\n          v-model.trim=\"queryParams.confirmCode\"\r\n          placeholder=\"请输入确认编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"产品名称\" prop=\"productName\">\r\n        <el-input\r\n          v-model.trim=\"queryParams.productName\"\r\n          placeholder=\"请输入产品名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"姓名\" prop=\"nickName\" label-width=\"50px\">\r\n        <el-input v-model.trim=\"queryParams.nickName\" placeholder=\"请输入工程师姓名\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"打样单编号\" label-width=\"90px\" prop=\"sampleOrderCode\">\r\n        <el-input v-model.trim=\"queryParams.sampleOrderCode\" placeholder=\"请输入打样单编号\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n        <el-select v-model=\"queryParams.completionStatus\" placeholder=\"请选择完成情况\" clearable>\r\n          <el-option\r\n            v-for=\"dict in statusOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualStartTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualFinishTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:add']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:edit']\">修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:remove']\">删除</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"doExportSampleOrder\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleBatchExportNrw\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出打样单任务</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAddSampleOrder\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:add']\">新增打样单</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 主要内容区域 -->\r\n     <!-- 工单列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"engineerSampleOrderList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"主键ID\" align=\"center\" prop=\"id\" /> -->\r\n<!--      <el-table-column label=\"工程师ID\" align=\"center\" prop=\"userId\" />-->\r\n      <el-table-column align=\"center\" prop=\"sampleOrderCode\" width=\"160\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          打样单编号\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              点击编号可查看详细信息\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"orderDetail(scope.row)\">{{ scope.row.sampleOrderCode }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"startDate\" width=\"140\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          申请日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              客户提交打样申请的日期时间\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"scheduledDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          排单日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              工程师被安排处理此打样单的日期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.scheduledDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"latestStartTime\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚开始日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              必须在此日期前开始工作，否则可能影响交期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"endDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚截至日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作必须在此日期前完成\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\" prop=\"completionStatus\">\r\n        <template slot=\"header\">\r\n          完成情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              当前打样单的状态\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"scope.row.completionStatus == '0' ? 'info' :\r\n                   scope.row.completionStatus == '1' ? 'primary' :\r\n                   scope.row.completionStatus == '2' ? 'success' : 'info'\"\r\n            size=\"mini\"\r\n          >\r\n            {{selectDictLabel(statusOptions,scope.row.completionStatus)}}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          批次信息\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              显示当前批次总数\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.totalBatches > 0\">\r\n            <el-tag size=\"mini\" type=\"primary\">\r\n              {{ scope.row.totalBatches || 0 }}\r\n            </el-tag>\r\n          </div>\r\n          <div v-else>\r\n            -\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          逾期情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              根据截止日期判断是否逾期及逾期天数\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"getOverdueInfo(scope.row).isOverdue\">\r\n            <el-tag type=\"danger\" size=\"mini\" style=\"margin-bottom: 2px;\">\r\n              逾期 {{ getOverdueInfo(scope.row).overdueDays }}天\r\n            </el-tag>\r\n          </div>\r\n          <el-tag v-else type=\"success\" size=\"mini\">\r\n            正常\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"nickName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          跟进人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的工程师姓名及职级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.nickName }}{{ scope.row.rank ? '-' + scope.row.rank : '' }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"assistantName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          协助人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              此打样单难度高于工程师能力范围\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.assistantName }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"isLocked\" width=\"90\">\r\n        <template slot=\"header\">\r\n          锁定状态\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              锁定后不允许更换工程师，防止误操作\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.isLocked\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            @change=\"(val) => handleTableLockChange(val, scope.row)\">\r\n          </el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratoryCode\" width=\"150\">\r\n        <template slot=\"header\">\r\n          实验编码\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              实验室分配的唯一编码标识\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"difficultyLevelId\" width=\"80\">\r\n        <template slot=\"header\">\r\n          难度\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作的技术难度等级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"selectDictLabel(dylbOptions,scope.row.difficultyLevelId)\" placement=\"top\">\r\n            <span>{{selectDictLabel(dylbOptions,scope.row.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"标准工时\" align=\"center\" prop=\"standardManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.standardManHours\">{{ scope.row.standardManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratory\" width=\"90\">\r\n        <template slot=\"header\">\r\n          实验室\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的实验室分支\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{\r\n            scope.row.laboratory === '0' ? '宜侬' :\r\n            scope.row.laboratory === '1' ? '瀛彩' :\r\n            ''\r\n          }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"categoryName\" width=\"70\">\r\n        <template slot=\"header\">\r\n          品类\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              产品所属的分类类别\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"SKU数\" align=\"center\" prop=\"sku\" width=\"70\" />\r\n      <el-table-column align=\"center\" prop=\"applicant\" width=\"90\">\r\n        <template slot=\"header\">\r\n          申请人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              提交此打样申请的人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"sales\" width=\"70\">\r\n        <template slot=\"header\">\r\n          销售\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此项目的销售人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"customerName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          客户名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              委托打样的客户公司名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"产品/项目等级\" align=\"center\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getProjectLevel(scope.row) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"productName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          产品名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              需要打样的产品名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工作时间\" align=\"center\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div v-if=\"scope.row.actualStartTime\">开始: {{ parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <div v-if=\"scope.row.actualFinishTime\">完成: {{ parseTime(scope.row.actualFinishTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <span v-if=\"!scope.row.actualStartTime && !scope.row.actualFinishTime\">-</span>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工时\" align=\"center\" prop=\"actualManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.actualManHours\">{{ scope.row.actualManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预估工时\" align=\"center\" prop=\"estimatedManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.estimatedManHours\">{{ scope.row.estimatedManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"服务模式\" align=\"center\" prop=\"serviceMode\" width=\"150\" />\r\n      <el-table-column label=\"计算类型\" align=\"center\" prop=\"checkType\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.checkType == 1\">客户等级</span>\r\n          <span v-else>打样单难度</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"完成质量\" align=\"center\" prop=\"qualityEvaluation\" />\r\n<!--      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"140\">-->\r\n<!--        <template slot-scope=\"scope\">-->\r\n<!--          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n      <el-table-column label=\"打样单备注\" align=\"center\" prop=\"sampleOrderRemark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.sampleOrderRemark\" placement=\"top\" :disabled=\"!scope.row.sampleOrderRemark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.sampleOrderRemark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.remark\" placement=\"top\" :disabled=\"!scope.row.remark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.remark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"未出样原因\" align=\"center\" prop=\"reasonForNoSample\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.reasonForNoSample\" placement=\"top\" :disabled=\"!scope.row.reasonForNoSample\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.reasonForNoSample || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"解决方案\" align=\"center\" prop=\"solution\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.solution\" placement=\"top\" :disabled=\"!scope.row.solution\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.solution || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"驳回原因\" align=\"center\" prop=\"rejectReason\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.rejectReason\" placement=\"top\" :disabled=\"!scope.row.rejectReason\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.rejectReason || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"fixed-width\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- <el-tooltip content=\"编辑\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:edit']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"删除\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:remove']\" />\r\n          </el-tooltip> -->\r\n          <el-tooltip  v-if=\"scope.row.completionStatus==0\" content=\"更换工程师或更改日期\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-user\" v-if=\"scope.row.isLocked === 0\" @click=\"handleChangeEngineer(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\" />\r\n          </el-tooltip>\r\n\r\n\r\n          <el-tooltip content=\"批次管理\" v-if=\"[0,1,2].includes(scope.row.completionStatus)\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-collection\" @click=\"handleBatchManagement(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:batchManagement']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"逾期操作\" v-if=\"getOverdueInfo(scope.row).isOverdue\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-warning\" @click=\"handleOverdueOperation(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:overdueOperation']\" style=\"color: #E6A23C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"驳回\" v-if=\"scope.row.completionStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-close\" @click=\"handleReject(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" style=\"color: #F56C6C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"更新实验室编码\" v-if=\"scope.row.completionStatus == '2' && scope.row.itemStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit-outline\" @click=\"handleUpdateLaboratoryCode(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:editSampleOrderCode']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"下载打样单\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-download\" @click=\"handleDownloadSampleOrder(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:export']\" />\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      :auto-scroll=\"false\" @pagination=\"getList\" />\r\n\r\n    <!-- 添加或修改工程师打样单关联对话框 -->\r\n    <!-- <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"650px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程师ID\" prop=\"userId\">\r\n              <el-input v-model=\"form.userId\" placeholder=\"请输入工程师ID\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"打样单编号\" prop=\"sampleOrderCode\">\r\n              <el-input v-model=\"form.sampleOrderCode\" placeholder=\"请输入打样单编号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"难度\" prop=\"difficultyLevelId\">\r\n              <el-select v-model=\"form.difficultyLevelId\" placeholder=\"请选择打样难度等级\">\r\n                  <el-option\r\n                    v-for=\"dict in dylbOptions\"\r\n                    :key=\"dict.dictValue\"\r\n                    :label=\"dict.dictLabel\"\r\n                    :value=\"dict.dictValue\"\r\n                  ></el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n              <el-select v-model=\"form.completionStatus\" placeholder=\"请选择完成情况\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in statusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n              <el-date-picker\r\n                v-model=\"form.scheduledDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择排单日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"最晚截至日期\" prop=\"endDate\">\r\n              <el-date-picker\r\n                v-model=\"form.endDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择最晚截至日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"锁定状态\" prop=\"isLocked\">\r\n              <el-switch\r\n                v-model=\"form.isLocked\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n                active-text=\"已锁定\"\r\n                inactive-text=\"未锁定\"\r\n                @change=\"handleLockChange\">\r\n              </el-switch>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualStartTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择开始时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualFinishTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择完成时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际工时\" prop=\"actualManHours\">\r\n              <el-input-number v-model=\"form.actualManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入实际工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"预估工时\" prop=\"estimatedManHours\">\r\n              <el-input-number v-model=\"form.estimatedManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入预估工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"完成质量\" prop=\"qualityEvaluation\">\r\n              <el-input v-model=\"form.qualityEvaluation\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入完成质量情况\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"打样单备注\" prop=\"sampleOrderRemark\">\r\n              <el-input v-model=\"form.sampleOrderRemark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入打样单备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n\r\n    <!-- 新增打样单对话框 -->\r\n    <el-dialog title=\"新增打样单\" :visible.sync=\"addSampleOrderOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"addSampleOrderForm\" :model=\"addSampleOrderForm\" :rules=\"addSampleOrderRules\" label-width=\"100px\">\r\n        <el-form-item label=\"实验室\" prop=\"labNo\">\r\n          <el-select v-model=\"addSampleOrderForm.labNo\" placeholder=\"请选择实验室\" style=\"width: 100%\">\r\n            <el-option label=\"宜侬\" value=\"0\" />\r\n            <el-option label=\"瀛彩\" value=\"1\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"类别\" prop=\"categoryText\">\r\n          <el-select v-model=\"addSampleOrderForm.categoryText\" filterable placeholder=\"请选择类别\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in sckxxpgCplbs\"\r\n              :key=\"dict.dictValue\"\r\n              :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"难度\" prop=\"dylb\">\r\n          <el-select v-model=\"addSampleOrderForm.dylb\" placeholder=\"请选择难度\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in dylbOptions\"\r\n              :key=\"dict.dictValue\"\r\n              :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"addSampleOrderForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAddSampleOrder\" :loading=\"addSampleOrderLoading\">确 定</el-button>\r\n        <el-button @click=\"cancelAddSampleOrder\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 更改工程师对话框 -->\r\n    <el-dialog title=\"更改工程师\" :visible.sync=\"changeEngineerOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"changeEngineerForm\" :model=\"changeEngineerForm\" :rules=\"changeEngineerRules\" label-width=\"150px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"changeEngineerForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"当前工程师\">\r\n          <el-input v-model=\"changeEngineerForm.currentEngineerName\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"选择工程师\" prop=\"newEngineerId\">\r\n          <el-radio-group v-model=\"engineerSelectType\" style=\"margin-bottom: 15px;\">\r\n            <el-radio label=\"specified\">指定部门工程师</el-radio>\r\n            <el-radio label=\"other\">其他部门工程师</el-radio>\r\n          </el-radio-group>\r\n\r\n          <!-- 指定工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'specified'\">\r\n            <el-select\r\n              v-model=\"changeEngineerForm.newEngineerId\"\r\n              placeholder=\"请选择工程师\"\r\n              style=\"width: 100%\"\r\n              filterable>\r\n              <el-option\r\n                v-for=\"engineer in engineerOptions\"\r\n                :key=\"engineer.userId\"\r\n                :label=\"engineer.nickName\"\r\n                :value=\"engineer.userId\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n\r\n          <!-- 其他工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'other'\" class=\"other-engineer-select\">\r\n            <div class=\"select-item\" style=\"margin-top: 10px;\">\r\n              <el-select\r\n                v-model=\"changeEngineerForm.newEngineerId\"\r\n                placeholder=\"请选择工程师\"\r\n                style=\"width: 100%\"\r\n                filterable\r\n                remote>\r\n                <el-option\r\n                  v-for=\"engineer in searchedEngineers\"\r\n                  :key=\"engineer.userId\"\r\n                  :label=\"engineer.nickName\"\r\n                  :value=\"engineer.userId\"\r\n                />\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"选择指定日期\" prop=\"scheduledDate\">\r\n          <el-date-picker\r\n            v-model=\"changeEngineerForm.scheduledDate\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"重新调整工作安排\">\r\n          <el-switch\r\n            v-model=\"changeEngineerForm.adjustWorkSchedule\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            active-text=\"是\"\r\n            inactive-text=\"否\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitChangeEngineer\">确 定</el-button>\r\n        <el-button @click=\"changeEngineerOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次管理对话框 -->\r\n    <el-dialog title=\"批次管理\" :visible.sync=\"batchManagementOpen\" width=\"900px\" append-to-body @close=\"closeBatchManagement\">\r\n      <div class=\"batch-management-container\">\r\n        <!-- 状态提示信息 -->\r\n        <el-alert\r\n          v-if=\"!canEditBatch\"\r\n          title=\"当前打样单状态不允许编辑批次信息\"\r\n          type=\"warning\"\r\n          description=\"只有状态为'进行中'的打样单才可以编辑和添加测试批次内容，其他状态只能查看批次信息。\"\r\n          show-icon\r\n          :closable=\"false\"\r\n          style=\"margin-bottom: 20px;\">\r\n        </el-alert>\r\n\r\n        <!-- 进行中批次信息 -->\r\n        <el-card class=\"active-batches-card\" v-if=\"activeBatches && activeBatches.length > 0\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>进行中批次 ({{ activeBatches.length }}个)</span>\r\n            <el-button\r\n              v-if=\"canEditBatch\"\r\n              style=\"float: right; padding: 3px 0\"\r\n              type=\"text\"\r\n              @click=\"finishAllActiveBatches\">\r\n              结束所有批次\r\n            </el-button>\r\n            <el-tag\r\n              v-else\r\n              style=\"float: right;\"\r\n              type=\"info\"\r\n              size=\"mini\">\r\n              只读模式\r\n            </el-tag>\r\n          </div>\r\n          <!-- 进行中批次列表 -->\r\n          <el-table :data=\"activeBatches\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"batchIndex\" label=\"批次序号\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"primary\">第{{ scope.row.batchIndex }}批次</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"已用时长\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ calculateDuration(scope.row.startTime) }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"实验室编号\" width=\"200\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"experiment-codes\">\r\n                  <el-tag\r\n                    v-for=\"experiment in scope.row.experiments || []\"\r\n                    :key=\"experiment.id\"\r\n                    size=\"mini\"\r\n                    closable\r\n                    @close=\"removeExperiment(scope.row, experiment)\"\r\n                    style=\"margin: 2px;\"\r\n                  >\r\n                    {{ experiment.experimentCode }}\r\n                  </el-tag>\r\n                  <el-button\r\n                    v-if=\"canEditBatch\"\r\n                    type=\"text\"\r\n                    size=\"mini\"\r\n                    @click=\"showAddExperimentDialog(scope.row)\"\r\n                    icon=\"el-icon-plus\"\r\n                  >\r\n                    添加编号\r\n                  </el-button>\r\n                  <span v-if=\"(!scope.row.experiments || scope.row.experiments.length === 0) && !canEditBatch\" style=\"color: #C0C4CC;\">未设置</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"状态\" width=\"80\" align=\"center\">\r\n              <template>\r\n                <el-tag type=\"success\" size=\"mini\">进行中</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"150\" align=\"center\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"canEditBatch\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"finishSingleBatch(scope.row)\">\r\n                  结束批次\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"viewBatchDetail(scope.row)\" icon=\"el-icon-view\">\r\n                  详情\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n        </el-card>\r\n\r\n        <!-- 开始新批次按钮 -->\r\n        <div class=\"new-batch-section\" style=\"text-align: center; margin: 20px 0;\">\r\n          <el-button\r\n            v-if=\"canEditBatch\"\r\n            type=\"primary\"\r\n            size=\"medium\"\r\n            @click=\"startNewBatch\">\r\n            开始新批次\r\n          </el-button>\r\n          <div v-else style=\"text-align: center; color: #909399;\">\r\n            <i class=\"el-icon-info\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"margin-top: 16px;\">当前状态不允许开始新批次</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 所有批次列表 -->\r\n        <el-card class=\"all-batches-card\" style=\"margin-top: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>所有批次 ({{ allBatches.length }}个)</span>\r\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshBatchData\">刷新</el-button>\r\n          </div>\r\n          <el-table :data=\"allBatches\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"batchIndex\" label=\"批次序号\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"primary\">第{{ scope.row.batchIndex }}批次</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.endTime\">{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n                <span v-else style=\"color: #909399;\">未结束</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"batchStatus\" label=\"状态\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag :type=\"getBatchStatusType(scope.row.batchStatus)\" size=\"mini\">\r\n                  {{ getBatchStatusText(scope.row.batchStatus) }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"laboratoryCode\" label=\"实验室编号\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.laboratoryCode\" style=\"color: #606266;\">{{ scope.row.laboratoryCode }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">未设置</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"actualManHours\" label=\"实际工时\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" :type=\"scope.row.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ scope.row.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"qualityEvaluation\" label=\"质量评价\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag v-if=\"scope.row.qualityEvaluation\" size=\"mini\"\r\n                        :type=\"getQualityEvaluationType(scope.row.qualityEvaluation)\">\r\n                  {{ scope.row.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else style=\"color: #909399;\">未评价</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remark\" label=\"批次备注\" min-width=\"150\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.remark\" style=\"color: #606266;\">{{ scope.row.remark }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"canEditBatch && scope.row.batchStatus === 0\"\r\n                  size=\"mini\"\r\n                  type=\"success\"\r\n                  @click=\"startSingleBatch(scope.row)\">\r\n                  开始\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"canEditBatch && scope.row.batchStatus === 1\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"finishSingleBatch(scope.row)\">\r\n                  结束\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"viewBatchDetail(scope.row)\" icon=\"el-icon-view\">\r\n                  详情\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 空状态 -->\r\n          <div v-if=\"!allBatches || allBatches.length === 0\" class=\"empty-state\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-document\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">暂无批次记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button\r\n          v-if=\"currentBatchRow && currentBatchRow.completionStatus == '0'\"\r\n          type=\"success\"\r\n          icon=\"el-icon-video-play\"\r\n          @click=\"handleStartFromBatch\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:startRask']\">\r\n          开始任务\r\n        </el-button>\r\n        <el-button\r\n          v-if=\"currentBatchRow && currentBatchRow.completionStatus == '1'\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-check\"\r\n          @click=\"handleFinishFromBatch\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:endRask']\">\r\n          完成任务\r\n        </el-button>\r\n        <el-button @click=\"batchManagementOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次详情对话框 -->\r\n    <el-dialog title=\"批次详情\" :visible.sync=\"batchDetailOpen\" width=\"800px\" append-to-body @close=\"closeBatchDetail\">\r\n      <div v-if=\"batchDetailData\" class=\"batch-detail-container\">\r\n        <!-- 批次基本信息 -->\r\n        <el-card class=\"batch-info-card\" style=\"margin-bottom: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>批次基本信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>批次序号：</label>\r\n                <el-tag type=\"primary\">第{{ batchDetailData.batchIndex }}批次</el-tag>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>开始时间：</label>\r\n                <span class=\"info-value\">{{ parseTime(batchDetailData.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>结束时间：</label>\r\n                <span class=\"info-value\">\r\n                  {{ batchDetailData.endTime ? parseTime(batchDetailData.endTime, '{y}-{m}-{d} {h}:{i}:{s}') : '未结束' }}\r\n                </span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>实际工时：</label>\r\n                <el-tag :type=\"batchDetailData.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ batchDetailData.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>质量评价：</label>\r\n                <el-tag v-if=\"batchDetailData.qualityEvaluation\"\r\n                        :type=\"getQualityEvaluationType(batchDetailData.qualityEvaluation)\">\r\n                  {{ batchDetailData.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else class=\"info-value\">未评价</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row style=\"margin-top: 15px;\">\r\n            <el-col :span=\"24\">\r\n              <div class=\"info-item\">\r\n                <label>批次备注：</label>\r\n                <div class=\"remark-content\">\r\n                  {{ batchDetailData.remark || '无备注' }}\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n\r\n        <!-- 实验记录详情 -->\r\n        <el-card class=\"experiments-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>实验记录详情</span>\r\n            <el-tag size=\"mini\" style=\"margin-left: 10px;\">\r\n              共{{ batchDetailData.experiments ? batchDetailData.experiments.length : 0 }}条记录\r\n            </el-tag>\r\n          </div>\r\n          <el-table :data=\"batchDetailData.experiments\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"experimentCode\" label=\"实验编号\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span style=\"color: #409EFF; font-weight: bold;\">{{ scope.row.experimentCode }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"experimentNote\" label=\"实验备注\" min-width=\"200\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.experimentNote\">{{ scope.row.experimentNote }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"info\">{{ scope.row.createBy || '系统' }}</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 实验记录空状态 -->\r\n          <div v-if=\"!batchDetailData.experiments || batchDetailData.experiments.length === 0\"\r\n               class=\"empty-experiments\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-data-line\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">该批次暂无实验记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"batchDetailOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加实验记录对话框 -->\r\n    <el-dialog title=\"添加实验室编号\" :visible.sync=\"addExperimentOpen\" width=\"500px\" append-to-body @close=\"closeAddExperiment\">\r\n      <el-form :model=\"addExperimentForm\" ref=\"addExperimentForm\" label-width=\"100px\" :rules=\"addExperimentRules\">\r\n        <el-form-item label=\"实验编号\" prop=\"experimentCode\">\r\n          <el-input v-model=\"addExperimentForm.experimentCode\" placeholder=\"请输入实验编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"实验备注\">\r\n          <el-input v-model=\"addExperimentForm.experimentNote\" type=\"textarea\" placeholder=\"请输入实验备注\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"addExperimentForm.remark\" type=\"textarea\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAddExperiment\">确 定</el-button>\r\n        <el-button @click=\"addExperimentOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 结束批次对话框 -->\r\n    <el-dialog :title=\"finishBatchMode === 'single' ? `结束第${currentFinishBatch && currentFinishBatch.batchIndex || ''}批次` : '结束当前批次'\" :visible.sync=\"finishBatchOpen\" width=\"500px\" append-to-body @close=\"closeFinishBatch\">\r\n      <el-form :model=\"finishBatchForm\" ref=\"finishBatchForm\" label-width=\"100px\">\r\n        <el-form-item label=\"质量评价\">\r\n          <el-input v-model=\"finishBatchForm.qualityEvaluation\" type=\"textarea\" placeholder=\"请输入质量评价\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishBatchForm.remark\" type=\"textarea\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFinishBatch\">确 定</el-button>\r\n        <el-button @click=\"finishBatchOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 完成任务对话框 -->\r\n    <el-dialog title=\"完成任务\" :visible.sync=\"finishTaskOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"finishTaskForm\" :model=\"finishTaskForm\" :rules=\"finishTaskRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-select v-model=\"finishTaskForm.laboratoryCode\" multiple filterable placeholder=\"请选择实验室编码\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"item in experimentCodeList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.experimentCode\"\r\n              :value=\"item.experimentCode\">\r\n              <span style=\"float: left\">{{ item.experimentCode }}</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 13px\" v-if=\"item.experimentNote\">{{ item.experimentNote }}</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishTaskForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"finishTaskOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmFinishTask\" :loading=\"finishTaskLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 更新实验室编码对话框 -->\r\n    <el-dialog title=\"更新实验室编码\" :visible.sync=\"updateLaboratoryCodeOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"updateLaboratoryCodeForm\" :model=\"updateLaboratoryCodeForm\" :rules=\"updateLaboratoryCodeRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.laboratoryCode\" placeholder=\"请输入实验室编码(多个使用,分割)\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"updateLaboratoryCodeOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmUpdateLaboratoryCode\" :loading=\"updateLaboratoryCodeLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导出选择对话框 -->\r\n    <el-dialog title=\"选择导出内容\" :visible.sync=\"exportDialogOpen\" width=\"400px\" append-to-body>\r\n      <div class=\"export-options\">\r\n        <p style=\"margin-bottom: 20px; color: #606266;\">请选择要导出的打样单类型：</p>\r\n        <el-checkbox-group v-model=\"exportOptions\" style=\"display: flex; flex-direction: column;\">\r\n          <el-checkbox label=\"assigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">已分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出已分配给工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n          <el-checkbox label=\"unassigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">未分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出尚未分配工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n        </el-checkbox-group>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"exportDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmExport\" :disabled=\"exportOptions.length === 0\" :loading=\"exportLoading\">\r\n          确认导出\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 驳回对话框 -->\r\n    <el-dialog title=\"驳回打样单\" :visible.sync=\"rejectDialogOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" :rules=\"rejectRules\" label-width=\"100px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"rejectForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"驳回理由\" prop=\"rejectReason\">\r\n          <el-input v-model=\"rejectForm.rejectReason\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入驳回理由\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"rejectDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmReject\" :loading=\"rejectLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 逾期操作对话框 -->\r\n    <el-dialog title=\"逾期操作\" :visible.sync=\"overdueOperationOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"overdueOperationForm\" :model=\"overdueOperationForm\" label-width=\"120px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"overdueOperationForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"预计出样时间\">\r\n          <el-date-picker\r\n            v-model=\"overdueOperationForm.expectedSampleTime\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"未出样原因\">\r\n          <el-input v-model=\"overdueOperationForm.reasonForNoSample\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入未出样原因（非必填）\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"解决方案\">\r\n          <el-input v-model=\"overdueOperationForm.solution\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入解决方案（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"overdueOperationOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOverdueOperation\" :loading=\"overdueOperationLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n    <!-- 添加或修改项目流程进行对话框 -->\r\n    <executionAddOrEdit ref=\"executionAddOrEdit\"></executionAddOrEdit>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listEngineerSampleOrder,\r\n  getEngineerSampleOrder,\r\n  delEngineerSampleOrder,\r\n  addEngineerSampleOrder,\r\n  updateEngineerSampleOrder,\r\n  updateSampleOrderStatus,\r\n  getDashboardStats,\r\n  getResearchDepartments,\r\n  getEngineersByDifficultyLevel,\r\n  changeEngineer,\r\n  getResearchDepartmentsUser,\r\n  getBatchesByOrderId,\r\n  startNewBatch,\r\n  addExperimentToBatch,\r\n  getExperimentsByBatchId,\r\n  exportEngineerSampleOrder,\r\n  getExecutionByOrderInfo,\r\n  getBatchExperimentCodeList,\r\n  getActiveBatches,\r\n  startSingleBatch,\r\n  finishSingleBatch,\r\n  finishAllActiveBatches,\r\n  removeExperimentFromBatch\r\n} from \"@/api/software/engineerSampleOrder\";\r\nimport {exportNrwItem, exportMultipleNrw} from \"@/api/project/projectItemOrder\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\nimport {customerBaseAll} from \"@/api/customer/customer\";\r\nimport {isNull} from \"@/utils/validate\";\r\nimport executionAddOrEdit from \"@/components/Project/components/executionAddOrEdit.vue\";\r\n\r\nexport default {\r\n  name: \"EngineerSampleOrder\",\r\n  components: {\r\n     Treeselect,executionAddOrEdit\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中的完整行数据\r\n      selectedRows: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 工程师打样单关联表格数据\r\n      engineerSampleOrderList: [],\r\n      // 研发部部门树列表\r\n      researchDeptDatas:[],\r\n      // 打样类别字典\r\n      dylbOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userId: null,\r\n        nickName: null,\r\n        sampleOrderCode: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        startDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        deptId: null,\r\n        deptIds: [],\r\n        associationStatus: null,\r\n        customerId: null,\r\n        productName: null,\r\n        confirmCode: null,\r\n        isOverdue: null,  // 增逾期任务过滤参数\r\n        laboratory: null  // 实验室筛选参数\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        userId: [\r\n          { required: true, message: \"工程师ID不能为空\", trigger: \"blur\" }\r\n        ],\r\n        sampleOrderCode: [\r\n          { required: true, message: \"打样单编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        completionStatus: [\r\n          { required: true, message: \"完成情况不能为空\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      statusOptions: [],\r\n      serviceModeOptions: [],\r\n      dateRange: [], // 新增的日期范围筛选\r\n      scheduledDateRange: [],\r\n      startDateRange: [],\r\n      actualStartTimeRange: [],\r\n      actualFinishTimeRange: [],\r\n      // 日期选择器配置\r\n      dataPickerOptions: {\r\n        shortcuts: [{\r\n          text: '今天',\r\n          onClick(picker) {\r\n            const today = new Date();\r\n            picker.$emit('pick', [today, today]);\r\n          }\r\n        }, {\r\n          text: '昨天',\r\n          onClick(picker) {\r\n            const yesterday = new Date();\r\n            yesterday.setTime(yesterday.getTime() - 3600 * 1000 * 24);\r\n            picker.$emit('pick', [yesterday, yesterday]);\r\n          }\r\n        }, {\r\n          text: '最近一周',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近一个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }]\r\n      },\r\n      // 状态更新对话框\r\n      statusOpen: false,\r\n      dashboardStats: {\r\n        \"total\": 0,\r\n        \"completed\": 0,\r\n        \"inProgress\": 0,\r\n        \"overdue\": 0\r\n      },\r\n      // 更改工程师对话框\r\n      changeEngineerOpen: false,\r\n      // 更改工程师表单\r\n      changeEngineerForm: {\r\n        id: null,\r\n        sampleOrderCode: null,\r\n        currentEngineerName: null,\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      },\r\n      // 更改工程师表单校验\r\n      changeEngineerRules: {\r\n        newEngineerId: [\r\n          { required: true, message: \"请选择工程师\", trigger: \"change\" }\r\n        ],\r\n        scheduledDate: [\r\n          { required: true, message: \"请选择日期\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 工程师选项\r\n      engineerOptions: [],\r\n      // 批次管理相关\r\n      batchManagementOpen: false,\r\n      activeBatches: [], // 进行中的批次列表\r\n      allBatches: [], // 所有批次列表\r\n      selectedOrderForBatch: null,\r\n      currentBatchRow: null, // 当前批次管理的行数据\r\n      // 批次详情对话框\r\n      batchDetailOpen: false,\r\n      batchDetailData: null,\r\n      // 结束批次对话框\r\n      finishBatchOpen: false,\r\n      finishBatchForm: {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      },\r\n      currentFinishBatch: null, // 当前要结束的批次（单个批次时使用）\r\n      finishBatchMode: 'all', // 'all' 表示结束所有批次，'single' 表示结束单个批次\r\n      // 添加实验记录对话框\r\n      addExperimentOpen: false,\r\n      addExperimentForm: {\r\n        experimentCode: '',\r\n        experimentNote: '',\r\n        remark: ''\r\n      },\r\n      addExperimentRules: {\r\n        experimentCode: [\r\n          { required: true, message: '实验编号不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      currentBatchForExperiment: null,\r\n      // 未来日期选择器配置\r\n      futureDatePickerOptions: {\r\n        disabledDate(time) {\r\n          return time.getTime() < Date.now() - 8.64e7; // 禁用今天之前的日期\r\n        }\r\n      },\r\n      engineerSelectType: 'specified',\r\n      searchedEngineers: [],\r\n      // 未分配打样单告警\r\n      unassignedOrders: [],\r\n      unassignedQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 8\r\n      },\r\n      unassignedTotal: 0,\r\n      // 未分配面板折叠状态\r\n      isUnassignedPanelCollapsed: true,\r\n      // 完成任务对话框\r\n      finishTaskOpen: false,\r\n      finishTaskLoading: false,\r\n      finishTaskForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      experimentCodeList: [], // 实验室编码列表\r\n      finishTaskRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentFinishRow: null,\r\n      // 更新实验室编码对话框\r\n      updateLaboratoryCodeOpen: false,\r\n      updateLaboratoryCodeLoading: false,\r\n      updateLaboratoryCodeForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      updateLaboratoryCodeRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentUpdateLaboratoryCodeRow: null,\r\n      // 导出选择对话框\r\n      exportDialogOpen: false,\r\n      exportOptions: [],\r\n      exportLoading: false,\r\n      readonly: true,\r\n      // 项目详情相关\r\n      currentProjectType: null,\r\n      confirmItemCodes: [],\r\n      customerOptions: [],\r\n      itemNames: [],\r\n      // 驳回对话框\r\n      rejectDialogOpen: false,\r\n      rejectLoading: false,\r\n      rejectForm: {\r\n        sampleOrderCode: '',\r\n        rejectReason: ''\r\n      },\r\n      // 逾期操作对话框\r\n      overdueOperationOpen: false,\r\n      overdueOperationLoading: false,\r\n      overdueOperationForm: {\r\n        sampleOrderCode: '',\r\n        expectedSampleTime: '',\r\n        reasonForNoSample: '',\r\n        solution: ''\r\n      },\r\n      currentOverdueRow: null,\r\n      rejectRules: {\r\n        rejectReason: [\r\n          { required: true, message: \"驳回理由不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentRejectRow: null,\r\n      // 新增打样单对话框\r\n      addSampleOrderOpen: false,\r\n      addSampleOrderLoading: false,\r\n      addSampleOrderForm: {\r\n        labNo: '',\r\n        categoryText: '',\r\n        dylb: '',\r\n        remark: ''\r\n      },\r\n      addSampleOrderRules: {\r\n        labNo: [\r\n          { required: true, message: \"实验室不能为空\", trigger: \"change\" }\r\n        ],\r\n        categoryText: [\r\n          { required: true, message: \"品类不能为空\", trigger: \"change\" }\r\n        ],\r\n        dylb: [\r\n          { required: true, message: \"打样单难度类别不能为空\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 产品类别选项\r\n      sckxxpgCplbs: []\r\n    };\r\n  },\r\n  computed: {\r\n    /** 计算是否可以编辑批次 - 只有状态为'1'(进行中)的打样单才可以编辑 */\r\n    canEditBatch() {\r\n      return this.selectedOrderForBatch && this.selectedOrderForBatch.completionStatus === 1;\r\n    }\r\n  },\r\n  created() {\r\n    // 设置默认日期范围为当天\r\n    const today = new Date();\r\n    const todayStr = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');\r\n    this.dateRange = [todayStr, todayStr];\r\n\r\n    this.getList();\r\n    customerBaseAll().then(res=> this.customerOptions = res)\r\n\r\n    this.getDicts(\"DYD_GCSZT\").then(response => {\r\n        this.statusOptions = response.data;\r\n    });\r\n    this.getDicts(\"CUSTOMER_SERVICE_MODE\").then(response => {\r\n      this.serviceModeOptions = response.data;\r\n    });\r\n    this.getDicts(\"project_nrw_dylb\").then(response => {\r\n      this.dylbOptions = response.data;\r\n    });\r\n    this.getDicts(\"SCKXXPG_CPLB\").then(response => {\r\n      this.sckxxpgCplbs = response.data;\r\n    });\r\n    this.loadDashboardStats();\r\n    // 获取研发部部门列表\r\n    getResearchDepartments().then(response => {\r\n      this.researchDeptDatas = this.handleTree(response.data, \"deptId\");\r\n    });\r\n    // 获取未分配打样单\r\n    this.handleUnassignedOrders();\r\n  },\r\n  methods: {\r\n    /** 计算产品/项目等级 */\r\n    getProjectLevel(row) {\r\n      const customerLevel = row.customerLevel || '';\r\n      const projectLevel = row.projectLevel || '';\r\n\r\n      // 如果 projectLevel 是空字符串或 \"/\" 就不相加\r\n      if (projectLevel === '' || projectLevel === '/') {\r\n        return customerLevel;\r\n      }\r\n\r\n      return customerLevel + projectLevel;\r\n    },\r\n    /** 查询工程师打样单关联列表 */\r\n    getList() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.loading = true;\r\n      params.associationStatus = 1\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.engineerSampleOrderList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        userId: null,\r\n        sampleOrderCode: null,\r\n        serviceMode: null,\r\n        difficultyLevelId: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        actualManHours: null,\r\n        estimatedManHours: null,\r\n        standardManHours: null,\r\n        qualityEvaluation: null,\r\n        isLocked: 0,\r\n        startDate: null,\r\n        endDate: null,\r\n        checkType: null,\r\n        sampleOrderRemark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.unassignedQueryParams.pageNum = 1;\r\n      this.getList();\r\n      this.loadDashboardStats();\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.dateRange = [] // 重置日期范围\r\n      this.scheduledDateRange = []\r\n      this.startDateRange = []\r\n      this.actualStartTimeRange = []\r\n      this.actualFinishTimeRange = []\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.selectedRows = selection\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加工程师打样单关联\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getEngineerSampleOrder(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改工程师打样单关联\";addEngineerSampleOrder\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          } else {\r\n            addEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除工程师打样单关联编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function () {\r\n        return delEngineerSampleOrder(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.msgSuccess(\"删除成功\");\r\n        this.loadDashboardStats();\r\n      }).catch(() => { });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 重置导出选项并显示选择对话框\r\n      this.exportOptions = [];\r\n      this.exportDialogOpen = true;\r\n    },\r\n    /** 确认导出操作 */\r\n    confirmExport() {\r\n      if (this.exportOptions.length === 0) {\r\n        this.$message.warning('请至少选择一种导出类型');\r\n        return;\r\n      }\r\n      this.exportLoading = true;\r\n      this.executeExports(0);\r\n    },\r\n\r\n    /** 串行执行导出操作 */\r\n    executeExports(index) {\r\n      if (index >= this.exportOptions.length) {\r\n        // 所有导出完成\r\n        this.exportLoading = false;\r\n        this.exportDialogOpen = false;\r\n        this.$message.success(`导出完成，共导出${this.exportOptions.length}个文件`);\r\n        return;\r\n      }\r\n\r\n      const option = this.exportOptions[index];\r\n      const associationStatus = option === 'assigned' ? 1 : 0;\r\n      const typeName = option === 'assigned' ? '已分配' : '未分配';\r\n\r\n      this.doExport(associationStatus, option).then(() => {\r\n        this.$message.success(`${typeName}打样单导出成功`);\r\n        // 继续导出下一个\r\n        this.executeExports(index + 1);\r\n      }).catch((error) => {\r\n        this.exportLoading = false;\r\n        this.$message.error(`${typeName}打样单导出失败: ${error.message || error}`);\r\n      });\r\n    },\r\n    /** 执行导出操作 */\r\n    doExport(associationStatus, exportType) {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = associationStatus;\r\n      params.exportType = exportType;\r\n      return exportEngineerSampleOrder(params).then(response => {\r\n        this.download(response.msg);\r\n      });\r\n    },\r\n    /** 执行导出打样单（已分配/未分配） */\r\n    doExportSampleOrder() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.$confirm('是否确认导出所有（已分配/未分配）打样单关联数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportEngineerSampleOrder(params);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"开始\"按钮操作 */\r\n    handleStart(row) {\r\n      this.$confirm('确定要将打样单：' + row.sampleOrderCode + '标记为进行中吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        updateSampleOrderStatus({\r\n          id: row.id,\r\n          status: '1'\r\n        }).then(response => {\r\n          this.msgSuccess('打样单已设置为已开始');\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      });\r\n    },\r\n    /** 下载打样单操作 */\r\n    handleDownloadSampleOrder(row) {\r\n      this.$confirm('是否确认导出打样单?', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportNrwItem({itemId: row.itemId,projectId: row.projectId})\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n      }).catch(() => {});\r\n    },\r\n    /** 批量导出打样单任务操作 */\r\n    handleBatchExportNrw() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$modal.msgError(\"请选择要导出的打样单任务\");\r\n        return;\r\n      }\r\n\r\n      // 构造批量导出数据，传递itemId和projectId\r\n      const exportData = this.selectedRows.map(row => ({\r\n        itemId: row.itemId,\r\n        projectId: row.projectId\r\n      }));\r\n\r\n      this.$confirm('是否确认导出所选打样单任务的内容物数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportMultipleNrw(exportData);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"完成\"按钮操作 */\r\n    handleFinish(row) {\r\n      this.currentFinishRow = row;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      // 加载实验室编码列表\r\n      this.loadExperimentCodeList(row.id);\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认完成任务 */\r\n    confirmFinishTask() {\r\n      this.$refs.finishTaskForm.validate(valid => {\r\n        // 处理实验室编号字段\r\n        let laboratoryCode = this.finishTaskForm.laboratoryCode;\r\n\r\n        // 如果是数组类型，使用逗号拼接\r\n        if (Array.isArray(laboratoryCode)) {\r\n          laboratoryCode = laboratoryCode.join(\",\");\r\n        }\r\n        if (valid) {\r\n          this.finishTaskLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentFinishRow.id,\r\n            status: '2',\r\n            laboratoryCode: laboratoryCode,\r\n            remark: this.finishTaskForm.remark\r\n          }).then(response => {\r\n            this.finishTaskLoading = false;\r\n            this.finishTaskOpen = false;\r\n            this.msgSuccess('打样单已设置为已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.finishTaskLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 从批次管理对话框完成任务 */\r\n    handleFinishFromBatch() {\r\n      this.currentFinishRow = this.currentBatchRow;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      // 加载实验室编码列表\r\n      this.loadExperimentCodeList(this.currentBatchRow.id);\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 从批次管理对话框开始任务 */\r\n    handleStartFromBatch() {\r\n      const row = this.currentBatchRow;\r\n      this.$confirm('确定要将打样单：' + row.sampleOrderCode + '标记为进行中吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        updateSampleOrderStatus({\r\n          id: row.id,\r\n          status: '1'\r\n        }).then(response => {\r\n          this.msgSuccess('打样单已设置为已开始');\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n          // 更新当前批次行数据的状态\r\n          this.currentBatchRow.completionStatus = '1';\r\n          this.selectedOrderForBatch.completionStatus = 1;\r\n          if (this.selectedOrderForBatch) {\r\n            this.loadBatchData(this.selectedOrderForBatch.id);\r\n          }\r\n        });\r\n      });\r\n    },\r\n    /** 点击\"更新实验室编码\"按钮操作 */\r\n    handleUpdateLaboratoryCode(row) {\r\n      this.currentUpdateLaboratoryCodeRow = row;\r\n      // 回显当前的实验室编码和备注\r\n      this.updateLaboratoryCodeForm.laboratoryCode = row.laboratoryCode || '';\r\n      this.updateLaboratoryCodeForm.remark = row.remark || '';\r\n      this.updateLaboratoryCodeOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.updateLaboratoryCodeForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认更新实验室编码 */\r\n    confirmUpdateLaboratoryCode() {\r\n      this.$refs.updateLaboratoryCodeForm.validate(valid => {\r\n        if (valid) {\r\n          this.updateLaboratoryCodeLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentUpdateLaboratoryCodeRow.id,\r\n            status: this.currentUpdateLaboratoryCodeRow.completionStatus, // 保持当前状态\r\n            laboratoryCode: this.updateLaboratoryCodeForm.laboratoryCode,\r\n            remark: this.updateLaboratoryCodeForm.remark\r\n          }).then(response => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n            this.updateLaboratoryCodeOpen = false;\r\n            this.msgSuccess('实验室编码更新成功');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"驳回\"按钮操作 */\r\n    handleReject(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认驳回 */\r\n    confirmReject() {\r\n      this.$refs.rejectForm.validate(valid => {\r\n        if (valid) {\r\n          this.rejectLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentRejectRow.id,\r\n            status: '3',\r\n            rejectReason: this.rejectForm.rejectReason\r\n          }).then(response => {\r\n            this.rejectLoading = false;\r\n            this.rejectDialogOpen = false;\r\n            this.msgSuccess('打样单已驳回');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n            // 如果是未分配打样单的驳回，也需要刷新未分配列表\r\n            this.handleUnassignedOrders();\r\n          }).catch(() => {\r\n            this.rejectLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"逾期操作\"按钮操作 */\r\n    handleOverdueOperation(row) {\r\n      this.currentOverdueRow = row;\r\n      this.overdueOperationForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.overdueOperationForm.expectedSampleTime = row.expectedSampleTime;\r\n      this.overdueOperationForm.reasonForNoSample = row.reasonForNoSample;\r\n      this.overdueOperationForm.solution = row.solution;\r\n      this.overdueOperationOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.overdueOperationForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认逾期操作 */\r\n    confirmOverdueOperation() {\r\n      this.$refs.overdueOperationForm.validate(valid => {\r\n        if (valid) {\r\n          this.overdueOperationLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentOverdueRow.id,\r\n            status: \"11\", // 特殊处理，只更新“逾期情况”填写的字段\r\n            expectedSampleTime: this.overdueOperationForm.expectedSampleTime,\r\n            reasonForNoSample: this.overdueOperationForm.reasonForNoSample,\r\n            solution: this.overdueOperationForm.solution\r\n          }).then(response => {\r\n            this.overdueOperationLoading = false;\r\n            this.overdueOperationOpen = false;\r\n            this.msgSuccess('逾期操作已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.overdueOperationLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 查看打样单详情 */\r\n    async orderDetail(row) {\r\n      try {\r\n        //获取执行ID\r\n        const orderId = row.projectOrderId;\r\n        let data = await getExecutionByOrderInfo(orderId);\r\n        if(data!=null && !isNull(data.id)){\r\n            let id = data.id;\r\n            this.$refs.executionAddOrEdit.open = true;\r\n            await this.$nextTick()\r\n            this.$refs.executionAddOrEdit.reset()\r\n            this.$refs.executionAddOrEdit.show(id, 1)\r\n        }else{\r\n          this.$message.error('获取数据失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('查看项目详情失败:', error);\r\n        this.$message.error('查看项目详情失败: ' + (error.message || '未知错误'));\r\n      }\r\n    },\r\n\r\n    /** 锁定状态改变处理 */\r\n    handleLockChange(value) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.form.isLocked = 1;\r\n        }).catch(() => {\r\n          this.form.isLocked = 0;\r\n        });\r\n      }\r\n    },\r\n    /** 表格中锁定状态改变处理 */\r\n    handleTableLockChange(value, row) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          // 调用更新接口\r\n          updateEngineerSampleOrder({\r\n            id: row.id,\r\n            isLocked: 1\r\n          }).then(response => {\r\n            this.msgSuccess(\"锁定成功\");\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          });\r\n        }).catch(() => {\r\n          // 取消操作，恢复原值\r\n          row.isLocked = 0;\r\n        });\r\n      } else {\r\n        // 解锁操作\r\n        updateEngineerSampleOrder({\r\n          id: row.id,\r\n          isLocked: 0\r\n        }).then(response => {\r\n          this.msgSuccess(\"解锁成功\");\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      }\r\n    },\r\n    /** 加载统计概览数据 */\r\n    loadDashboardStats() {\r\n      let params = { ...this.queryParams };\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      getDashboardStats(params).then(response => {\r\n        this.dashboardStats = response.data || {};\r\n      });\r\n    },\r\n    /** 更改工程师按钮操作 */\r\n    handleChangeEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: row.nickName,\r\n        oldEngineerId: row.userId,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 提交更改工程师 */\r\n    submitChangeEngineer() {\r\n      this.$refs[\"changeEngineerForm\"].validate(valid => {\r\n        if (valid) {\r\n          const data = {\r\n            sampleOrderId: this.changeEngineerForm.id,\r\n            oldEngineerId: this.changeEngineerForm.oldEngineerId,\r\n            newEngineerId: this.changeEngineerForm.newEngineerId,\r\n            scheduledDate: this.changeEngineerForm.scheduledDate,\r\n            adjustWorkSchedule: this.changeEngineerForm.adjustWorkSchedule\r\n          };\r\n\r\n          // 调用更改工程师的API接口\r\n          changeEngineer(data).then(response => {\r\n            this.msgSuccess(\"更改工程师成功\");\r\n            this.changeEngineerOpen = false;\r\n            this.getList();\r\n            // 获取未分配打样单\r\n            this.handleUnassignedOrders();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.msgError(\"更改工程师失败\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 获取未分配打样单列表 */\r\n    handleUnassignedOrders() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = 0\r\n      params.pageNum = this.unassignedQueryParams.pageNum;\r\n      params.pageSize = this.unassignedQueryParams.pageSize;\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.unassignedOrders = response.rows;\r\n        this.unassignedTotal = response.total;\r\n      });\r\n    },\r\n    /** 分配工程师操作 */\r\n    handleAssignEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: '',\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 删除未分配打样单 */\r\n    handleDeleteUnassigned(row) {\r\n      this.$confirm('是否确认删除打样单编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function() {\r\n        return delEngineerSampleOrder(row.id);\r\n      }).then(() => {\r\n        this.handleUnassignedOrders();\r\n        this.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 驳回未分配打样单 */\r\n    handleRejectUnassigned(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 未分配打样单分页大小改变 */\r\n    handleUnassignedSizeChange(newSize) {\r\n      this.unassignedQueryParams.pageSize = newSize;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 未分配打样单页码改变 */\r\n    handleUnassignedCurrentChange(newPage) {\r\n      this.unassignedQueryParams.pageNum = newPage;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 切换未分配面板显示状态 */\r\n    toggleUnassignedPanel() {\r\n      this.isUnassignedPanelCollapsed = !this.isUnassignedPanelCollapsed;\r\n    },\r\n    /** 处理状态过滤 */\r\n    handleStatusFilter(status) {\r\n      // 清除逾期过滤\r\n      this.queryParams.isOverdue = null;\r\n      this.queryParams.completionStatus = status;\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 处理逾期任务过滤 */\r\n    handleOverdueFilter() {\r\n      // 清除完成状态过滤\r\n      this.queryParams.completionStatus = null;\r\n      // 设置逾期过滤\r\n      this.queryParams.isOverdue = this.queryParams.isOverdue === 1 ? null : 1;\r\n      this.handleQuery();\r\n\r\n      if (this.queryParams.isOverdue === 1) {\r\n        this.$message.info('已筛选显示逾期任务');\r\n      } else {\r\n        this.$message.info('已清除逾期任务筛选');\r\n      }\r\n    },\r\n    /** 获取紧急程度类型 */\r\n    getUrgencyType(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return 'danger'; // 已超过最晚开始时间\r\n      }\r\n\r\n      if (daysToEnd <= 1) {\r\n        return 'danger'; // 紧急：1天内截止\r\n      } else if (daysToEnd <= 3) {\r\n        return 'warning'; // 警告：3天内截止\r\n      } else if (daysToEnd <= 7) {\r\n        return 'primary'; // 一般：7天内截止\r\n      } else {\r\n        return 'success'; // 充足时间\r\n      }\r\n    },\r\n    /** 获取紧急程度文本 */\r\n    getUrgencyText(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return '超期未开始';\r\n      }\r\n\r\n      if (daysToEnd <= 0) {\r\n        return '已逾期';\r\n      } else if (daysToEnd <= 1) {\r\n        return '紧急';\r\n      } else if (daysToEnd <= 3) {\r\n        return '较急';\r\n      } else if (daysToEnd <= 7) {\r\n        return '一般';\r\n      } else {\r\n        return '充足';\r\n      }\r\n    },\r\n    /** 刷新未分配打样单列表 */\r\n    handleRefreshUnassigned() {\r\n      this.handleUnassignedOrders();\r\n      this.$message.success('刷新成功');\r\n    },\r\n    /** 获取逾期信息 */\r\n    getOverdueInfo(row) {\r\n      const now = new Date();\r\n      const endDate = new Date(row.endDate);\r\n      let isOverdue = false;\r\n      let overdueDays = 0;\r\n      // 根据后台SQL逻辑判断逾期\r\n      if (row.completionStatus === 2) {\r\n        // 已完成且逾期：实际完成时间 > 截止日期\r\n        if (row.actualFinishTime) {\r\n          const actualFinishTime = new Date(row.actualFinishTime);\r\n          if (actualFinishTime > endDate) {\r\n            isOverdue = true;\r\n            overdueDays = Math.ceil((actualFinishTime - endDate) / (1000 * 60 * 60 * 24));\r\n          }\r\n        }\r\n      } else if (row.completionStatus === 1) {\r\n        // 进行中且逾期：当前时间 > 截止日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      } else if (row.completionStatus === 0) {\r\n        // 未开始且逾期：未开始但当前时间 > 开始日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      }\r\n\r\n      return {\r\n        isOverdue,\r\n        overdueDays\r\n      };\r\n    },\r\n\r\n    // ==================== 批次管理相关方法 ====================\r\n\r\n    /** 打开批次管理对话框 */\r\n    handleBatchManagement(row) {\r\n      this.selectedOrderForBatch = row;\r\n      this.currentBatchRow = row; // 保存当前行数据\r\n      this.batchManagementOpen = true;\r\n      this.loadBatchData(row.id);\r\n    },\r\n\r\n    /** 加载批次数据 */\r\n    async loadBatchData(engineerSampleOrderId) {\r\n      try {\r\n        // 加载进行中的批次\r\n        const activeBatchesResponse = await this.getActiveBatchesData(engineerSampleOrderId);\r\n        this.activeBatches = activeBatchesResponse.data || [];\r\n\r\n        // 为每个进行中的批次加载实验记录\r\n        for (let batch of this.activeBatches) {\r\n          await this.loadBatchExperiments(batch);\r\n        }\r\n\r\n        // 加载所有批次\r\n        const batchesResponse = await getBatchesByOrderId(engineerSampleOrderId);\r\n        this.allBatches = batchesResponse.data || [];\r\n      } catch (error) {\r\n        console.error('加载批次数据失败:', error);\r\n        this.$message.error('加载批次数据失败');\r\n      }\r\n    },\r\n\r\n    /** 加载批次的实验记录 */\r\n    async loadBatchExperiments(batch) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batch.id);\r\n        this.$set(batch, 'experiments', response.data || []);\r\n      } catch (error) {\r\n        console.error('加载批次实验记录失败:', error);\r\n        this.$set(batch, 'experiments', []);\r\n      }\r\n    },\r\n\r\n    /** 获取进行中的批次 */\r\n    async getActiveBatchesData(engineerSampleOrderId) {\r\n      try {\r\n        // 调用新的API获取进行中的批次\r\n        const response = await getActiveBatches(engineerSampleOrderId);\r\n        return response;\r\n      } catch (error) {\r\n        console.error('获取进行中批次失败:', error);\r\n        return { data: [] };\r\n      }\r\n    },\r\n\r\n    /** 开始新批次 */\r\n    async startNewBatch() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许开始新批次，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$confirm('确认开始新的打样批次？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        const response = await startNewBatch(this.selectedOrderForBatch.id, '');\r\n        this.$message.success('新批次开始成功');\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('开始新批次失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 开始单个批次 */\r\n    async startSingleBatch(batch) {\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许开始批次');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$confirm(`确认开始第${batch.batchIndex}批次？`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        await startSingleBatch(batch.id);\r\n        this.$message.success('批次开始成功');\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n        this.getList();\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('开始批次失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 结束单个批次 */\r\n    finishSingleBatch(batch) {\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许结束批次');\r\n        return;\r\n      }\r\n\r\n      // 设置为单个批次结束模式\r\n      this.finishBatchMode = 'single';\r\n      this.currentFinishBatch = batch;\r\n\r\n      // 重置表单\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 显示对话框\r\n      this.finishBatchOpen = true;\r\n    },\r\n\r\n    /** 结束所有进行中批次 */\r\n    finishAllActiveBatches() {\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许结束批次');\r\n        return;\r\n      }\r\n\r\n      // 设置为结束所有批次模式\r\n      this.finishBatchMode = 'all';\r\n      this.currentFinishBatch = null;\r\n\r\n      // 重置表单\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 显示对话框\r\n      this.finishBatchOpen = true;\r\n    },\r\n\r\n    /** 提交结束批次 */\r\n    async submitFinishBatch() {\r\n      try {\r\n        if (this.finishBatchMode === 'single') {\r\n          // 结束单个批次\r\n          await finishSingleBatch(\r\n            this.currentFinishBatch.id,\r\n            this.finishBatchForm.qualityEvaluation || '',\r\n            this.finishBatchForm.remark || '',\r\n            this.currentFinishBatch.laboratoryCode || ''\r\n          );\r\n          this.$message.success('批次结束成功');\r\n        } else {\r\n          // 结束所有进行中的批次\r\n          await finishAllActiveBatches(this.selectedOrderForBatch.id);\r\n          this.$message.success('所有批次结束成功');\r\n        }\r\n\r\n        this.finishBatchOpen = false;\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        console.error('结束批次失败:', error);\r\n        this.$message.error('结束批次失败: ' + (error.message || error));\r\n      }\r\n    },\r\n\r\n    /** 查看批次详情 */\r\n    async viewBatchDetail(batch) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batch.id);\r\n        const experiments = response.data || [];\r\n\r\n        // 设置批次详情数据\r\n        this.batchDetailData = {\r\n          ...batch,\r\n          experiments: experiments\r\n        };\r\n\r\n        this.batchDetailOpen = true;\r\n      } catch (error) {\r\n        console.error('查看批次详情失败:', error);\r\n        this.$message.error('查看批次详情失败');\r\n      }\r\n    },\r\n\r\n    /** 计算持续时间 */\r\n    calculateDuration(startTime) {\r\n      if (!startTime) return '0小时';\r\n\r\n      const start = new Date(startTime);\r\n      const now = new Date();\r\n      const diffMs = now - start;\r\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\r\n      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\r\n\r\n      if (diffHours > 0) {\r\n        return `${diffHours}小时${diffMinutes}分钟`;\r\n      } else {\r\n        return `${diffMinutes}分钟`;\r\n      }\r\n    },\r\n\r\n    /** 编辑实验室编号 */\r\n    editLaboratoryCode(batch) {\r\n      this.$set(batch, 'editingLab', true);\r\n      this.$nextTick(() => {\r\n        // 聚焦到输入框\r\n        const input = this.$el.querySelector(`input[value=\"${batch.laboratoryCode || ''}\"]`);\r\n        if (input) input.focus();\r\n      });\r\n    },\r\n\r\n    /** 显示添加实验记录对话框 */\r\n    showAddExperimentDialog(batch) {\r\n      this.currentBatchForExperiment = batch;\r\n      this.addExperimentForm = {\r\n        experimentCode: '',\r\n        experimentNote: '',\r\n        remark: ''\r\n      };\r\n      this.addExperimentOpen = true;\r\n    },\r\n\r\n    /** 关闭添加实验记录对话框 */\r\n    closeAddExperiment() {\r\n      this.addExperimentOpen = false;\r\n      this.currentBatchForExperiment = null;\r\n      this.$refs.addExperimentForm && this.$refs.addExperimentForm.resetFields();\r\n    },\r\n\r\n    /** 提交添加实验记录 */\r\n    async submitAddExperiment() {\r\n      try {\r\n        await this.$refs.addExperimentForm.validate();\r\n\r\n        const experimentRecord = {\r\n          batchId: this.currentBatchForExperiment.id,\r\n          experimentCode: this.addExperimentForm.experimentCode,\r\n          experimentNote: this.addExperimentForm.experimentNote || '',\r\n          remark: this.addExperimentForm.remark\r\n        };\r\n\r\n        await addExperimentToBatch(experimentRecord);\r\n\r\n        this.$message.success('实验记录添加成功');\r\n        this.addExperimentOpen = false;\r\n\r\n        // 重新加载当前批次的实验记录\r\n        await this.loadBatchExperiments(this.currentBatchForExperiment);\r\n      } catch (error) {\r\n        this.$message.error('添加实验记录失败: ' + (error.message || error));\r\n      }\r\n    },\r\n\r\n    /** 删除实验记录 */\r\n    async removeExperiment(batch, experiment) {\r\n      try {\r\n        await this.$confirm('确认删除该实验记录吗？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        await removeExperimentFromBatch(experiment.id);\r\n        this.$message.success('实验记录删除成功');\r\n\r\n        // 重新加载当前批次的实验记录\r\n        await this.loadBatchExperiments(batch);\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除实验记录失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 获取批次状态文本 */\r\n    getBatchStatusText(status) {\r\n      const statusMap = {\r\n        0: '未开始',\r\n        1: '进行中',\r\n        2: '已完成',\r\n        3: '已取消'\r\n      };\r\n      return statusMap[status] || '未知';\r\n    },\r\n\r\n    /** 获取批次状态类型 */\r\n    getBatchStatusType(status) {\r\n      const typeMap = {\r\n        0: 'info',\r\n        1: 'success',\r\n        2: 'primary',\r\n        3: 'danger'\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    /** 获取质量评价类型 */\r\n    getQualityEvaluationType(evaluation) {\r\n      if (!evaluation) return '';\r\n\r\n      const lowerEval = evaluation.toLowerCase();\r\n      if (lowerEval.includes('优秀') || lowerEval.includes('良好') || lowerEval.includes('好')) {\r\n        return 'success';\r\n      } else if (lowerEval.includes('一般') || lowerEval.includes('中等')) {\r\n        return 'warning';\r\n      } else if (lowerEval.includes('差') || lowerEval.includes('不合格') || lowerEval.includes('失败')) {\r\n        return 'danger';\r\n      } else {\r\n        return 'info';\r\n      }\r\n    },\r\n\r\n    /** 关闭批次管理对话框并清空数据 */\r\n    closeBatchManagement() {\r\n      // 清空批次管理相关的数据\r\n      this.activeBatches = [];\r\n      this.allBatches = [];\r\n      this.selectedOrderForBatch = null;\r\n      this.currentBatchRow = null; // 清空当前行数据\r\n      this.batchDetailData = null;\r\n\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 关闭所有相关的子对话框\r\n      this.batchDetailOpen = false;\r\n      this.addExperimentOpen = false;\r\n      this.finishBatchOpen = false;\r\n    },\r\n\r\n    /** 关闭批次详情对话框并清空数据 */\r\n    closeBatchDetail() {\r\n      // 清空批次详情数据\r\n      this.batchDetailData = null;\r\n    },\r\n\r\n    /** 关闭结束批次对话框并清空数据 */\r\n    closeFinishBatch() {\r\n      // 清空结束批次表单数据\r\n      this.finishBatchOpen = false;\r\n      this.finishBatchMode = 'all';\r\n      this.currentFinishBatch = null;\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n      // 清除表单验证状态\r\n      if (this.$refs.finishBatchForm) {\r\n        this.$refs.finishBatchForm.clearValidate();\r\n      }\r\n    },\r\n\r\n    /** 刷新批次数据 */\r\n    async refreshBatchData() {\r\n      if (this.selectedOrderForBatch) {\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n        this.$message.success('批次数据已刷新');\r\n      }\r\n    },\r\n\r\n    /** 加载实验室编码列表 */\r\n    async loadExperimentCodeList(engineerSampleOrderId) {\r\n      try {\r\n        const response = await getBatchExperimentCodeList(engineerSampleOrderId);\r\n        this.experimentCodeList = response.data || [];\r\n      } catch (error) {\r\n        console.error('加载实验室编码列表失败:', error);\r\n        this.experimentCodeList = [];\r\n        this.$message.error('加载实验室编码列表失败');\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.stats-row {\r\n  margin-bottom: 20px;\r\n}\r\n.stats-card {\r\n  border: none;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* 批次详情样式 */\r\n.batch-detail-container {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.batch-info-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-value {\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.remark-content {\r\n  background-color: #F5F7FA;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409EFF;\r\n  color: #606266;\r\n  line-height: 1.5;\r\n  margin-top: 8px;\r\n  min-height: 40px;\r\n}\r\n\r\n.experiments-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.empty-state, .empty-experiments {\r\n  background-color: #FAFAFA;\r\n  border-radius: 8px;\r\n  margin: 10px 0;\r\n}\r\n\r\n/* 历史批次表格样式优化 */\r\n.history-batches-card .el-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-batches-card .el-table th {\r\n  background-color: #F5F7FA;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.history-batches-card .el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n.stats-card:not(.total) {\r\n  cursor: pointer;\r\n}\r\n\r\n.stats-card:not(.total):hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stats-card.active {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  position: relative;\r\n}\r\n\r\n.stats-card.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 3px;\r\n  background: currentColor;\r\n}\r\n\r\n.stats-card.completed.active::after {\r\n  background: #67C23A;\r\n}\r\n\r\n.stats-card.in-progress.active::after {\r\n  background: #409EFF;\r\n}\r\n\r\n.stats-card.overdue.active::after {\r\n  background: #F56C6C;\r\n}\r\n\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n\r\n.stats-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n.stats-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n}\r\n.stats-icon i {\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n.stats-info {\r\n  flex: 1;\r\n}\r\n.stats-title {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-bottom: 8px;\r\n}\r\n.stats-number {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n.stats-card.total .stats-icon {\r\n  background: linear-gradient(135deg, #3a7bd5, #3a6073);\r\n}\r\n.stats-card.completed .stats-icon {\r\n  background: linear-gradient(135deg, #E6A23C, #F0C78A);\r\n}\r\n.stats-card.in-progress .stats-icon {\r\n  background: linear-gradient(135deg, #409EFF, #66B1FF);\r\n}\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n@media (max-width: 768px) {\r\n  .stats-card .stats-content {\r\n    padding: 15px;\r\n  }\r\n  .stats-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n    margin-right: 15px;\r\n  }\r\n  .stats-icon i {\r\n    font-size: 20px;\r\n  }\r\n  .stats-number {\r\n    font-size: 24px;\r\n  }\r\n}\r\n.other-engineer-select {\r\n  margin-top: 10px;\r\n}\r\n.select-item {\r\n  width: 100%;\r\n}\r\n\r\n/* 未分配打样单告警样式 */\r\n.alert-card {\r\n  margin-bottom: 20px;\r\n}\r\n.alert-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.alert-title {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n.alert-title span {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #FF1414;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-cards {\r\n  margin-top: 20px;\r\n}\r\n.alert-item {\r\n  margin-bottom: 20px;\r\n  transition: all 0.3s;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n  height: 225px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n.alert-item:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);\r\n  border-color: #409EFF;\r\n}\r\n.alert-item-header {\r\n  margin-bottom: 12px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #f0f2f5;\r\n  background: rgba(64, 158, 255, 0.02);\r\n  margin: -16px -16px 12px -16px;\r\n  padding: 12px 16px;\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n.order-code-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.order-code {\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n.urgency-tag {\r\n  margin-left: 8px;\r\n}\r\n.alert-item-content {\r\n  padding: 0;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  min-height: 0;\r\n}\r\n.info-section {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n}\r\n.info-row {\r\n  margin-bottom: 8px;\r\n}\r\n.info-row-double {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 12px;\r\n}\r\n.info-row-double .info-item {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-row-double .info-item.date-time-item {\r\n  flex: 1.5;\r\n}\r\n.info-row-double .info-item.standard-item {\r\n  flex: 1;\r\n}\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n.info-label {\r\n  margin-left: 6px;\r\n  margin-right: 4px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n.info-value {\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n.difficulty-text {\r\n  cursor: pointer;\r\n  border-bottom: 1px dashed #ccc;\r\n}\r\n.info-reason {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  color: #f56c6c;\r\n  margin-top: 8px;\r\n  padding: 6px 8px;\r\n  background-color: #fef0f0;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #f56c6c;\r\n  font-size: 12px;\r\n}\r\n.reason-text {\r\n  margin-left: 4px;\r\n  line-height: 1.4;\r\n  word-break: break-word;\r\n}\r\n.text-overflow {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-reason span {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.info-item i,\r\n.info-date i,\r\n.info-reason i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n.alert-item-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: auto;\r\n  padding: 12px 0 0 0;\r\n  border-top: 1px solid #f0f2f5;\r\n  flex-shrink: 0;\r\n  background: rgba(250, 251, 252, 0.5);\r\n  border-radius: 0 0 6px 6px;\r\n  margin-left: -16px;\r\n  margin-right: -16px;\r\n  padding-left: 16px;\r\n  padding-right: 16px;\r\n}\r\n.alert-item-footer .el-button {\r\n  border-radius: 4px;\r\n  font-weight: 500;\r\n}\r\n.alert-item-footer .el-button--primary {\r\n  background: #409EFF;\r\n  border-color: #409EFF;\r\n  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);\r\n}\r\n.alert-item-footer .el-button--primary:hover {\r\n  background: #66b1ff;\r\n  border-color: #66b1ff;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);\r\n}\r\n.alert-item-footer .el-button--text {\r\n  color: #909399;\r\n  transition: all 0.3s;\r\n}\r\n.alert-item-footer .el-button--text:hover {\r\n  color: #f56c6c;\r\n  background: rgba(245, 108, 108, 0.1);\r\n}\r\n.delete-btn:hover {\r\n  color: #f56c6c !important;\r\n}\r\n\r\n/* 确保卡片内容区域的统一布局 */\r\n.alert-item .el-card__body {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 16px;\r\n  position: relative;\r\n}\r\n\r\n/* 确保内容区域占据剩余空间 */\r\n.alert-item .alert-item-header {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.alert-item .alert-item-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n}\r\n\r\n.alert-item .alert-item-footer {\r\n  flex-shrink: 0;\r\n  margin-top: auto;\r\n}\r\n\r\n.alert-item .info-group {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.alert-item .info-date {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .alert-item {\r\n    height: auto;\r\n    min-height: 200px;\r\n  }\r\n\r\n  .info-row-double {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .alert-item-footer {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .alert-item-footer .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 批次管理相关样式 */\r\n.batch-management-container {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.current-batch-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-info-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.batch-info-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.experiment-section {\r\n  border-top: 1px solid #ebeef5;\r\n  padding-top: 15px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.section-header span {\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.history-batches-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.new-batch-section {\r\n  padding: 40px 0;\r\n  text-align: center;\r\n  background-color: #fafafa;\r\n  border: 2px dashed #dcdfe6;\r\n  border-radius: 6px;\r\n}\r\n\r\n.new-batch-section .el-button {\r\n  padding: 12px 30px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .batch-management-container {\r\n    max-height: 500px;\r\n  }\r\n\r\n  .batch-info-item {\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .section-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .section-header .el-button {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n\r\n/* 备注文本省略样式 */\r\n.remark-text-ellipsis {\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  line-clamp: 2;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  line-height: 1.4;\r\n  max-height: 2.8em; /* 2行的高度，基于line-height */\r\n  word-break: break-word;\r\n  white-space: normal;\r\n  cursor: pointer;\r\n}\r\n\r\n.remark-text-ellipsis:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 导出选择对话框样式 */\r\n.export-options {\r\n  padding: 10px 0;\r\n}\r\n\r\n.export-options .el-checkbox {\r\n  width: 100%;\r\n  margin-right: 0;\r\n  padding: 15px;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.export-options .el-checkbox:hover {\r\n  border-color: #409eff;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.export-options .el-checkbox.is-checked {\r\n  border-color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.export-options .el-checkbox__label {\r\n  width: 100%;\r\n  padding-left: 10px;\r\n}\r\n\r\n/* 实验记录相关样式 */\r\n.experiment-codes {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 4px;\r\n  min-height: 32px;\r\n}\r\n\r\n.experiment-codes .el-tag {\r\n  margin: 2px;\r\n  max-width: 120px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.experiment-codes .el-button {\r\n  margin: 2px;\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n}\r\n</style>\r\n"]}]}