{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue?vue&type=template&id=b582a56c&scoped=true", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue", "mtime": 1754285363614}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}