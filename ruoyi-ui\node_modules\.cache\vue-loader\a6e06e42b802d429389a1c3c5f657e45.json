{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue", "mtime": 1754285363614}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0RW5naW5lZXJTYW1wbGVPcmRlciwNCiAgZ2V0RW5naW5lZXJTYW1wbGVPcmRlciwNCiAgZGVsRW5naW5lZXJTYW1wbGVPcmRlciwNCiAgYWRkRW5naW5lZXJTYW1wbGVPcmRlciwNCiAgdXBkYXRlRW5naW5lZXJTYW1wbGVPcmRlciwNCiAgdXBkYXRlU2FtcGxlT3JkZXJTdGF0dXMsDQogIGdldERhc2hib2FyZFN0YXRzLA0KICBnZXRSZXNlYXJjaERlcGFydG1lbnRzLA0KICBnZXRFbmdpbmVlcnNCeURpZmZpY3VsdHlMZXZlbCwNCiAgY2hhbmdlRW5naW5lZXIsDQogIGdldFJlc2VhcmNoRGVwYXJ0bWVudHNVc2VyLA0KICBnZXRCYXRjaGVzQnlPcmRlcklkLA0KICBzdGFydE5ld0JhdGNoLA0KICBhZGRFeHBlcmltZW50VG9CYXRjaCwNCiAgZ2V0RXhwZXJpbWVudHNCeUJhdGNoSWQsDQogIGV4cG9ydEVuZ2luZWVyU2FtcGxlT3JkZXIsDQogIGdldEV4ZWN1dGlvbkJ5T3JkZXJJbmZvLA0KICBnZXRCYXRjaEV4cGVyaW1lbnRDb2RlTGlzdCwNCiAgZ2V0QWN0aXZlQmF0Y2hlcywNCiAgc3RhcnRTaW5nbGVCYXRjaCwNCiAgZmluaXNoU2luZ2xlQmF0Y2gsDQogIGZpbmlzaEFsbEFjdGl2ZUJhdGNoZXMsDQogIHJlbW92ZUV4cGVyaW1lbnRGcm9tQmF0Y2gNCn0gZnJvbSAiQC9hcGkvc29mdHdhcmUvZW5naW5lZXJTYW1wbGVPcmRlciI7DQppbXBvcnQge2V4cG9ydE5yd0l0ZW0sIGV4cG9ydE11bHRpcGxlTnJ3fSBmcm9tICJAL2FwaS9wcm9qZWN0L3Byb2plY3RJdGVtT3JkZXIiOw0KaW1wb3J0IFRyZWVzZWxlY3QgZnJvbSAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QiOw0KaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7DQppbXBvcnQge2N1c3RvbWVyQmFzZUFsbH0gZnJvbSAiQC9hcGkvY3VzdG9tZXIvY3VzdG9tZXIiOw0KaW1wb3J0IHtpc051bGx9IGZyb20gIkAvdXRpbHMvdmFsaWRhdGUiOw0KaW1wb3J0IGV4ZWN1dGlvbkFkZE9yRWRpdCBmcm9tICJAL2NvbXBvbmVudHMvUHJvamVjdC9jb21wb25lbnRzL2V4ZWN1dGlvbkFkZE9yRWRpdC52dWUiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJFbmdpbmVlclNhbXBsZU9yZGVyIiwNCiAgY29tcG9uZW50czogew0KICAgICBUcmVlc2VsZWN0LGV4ZWN1dGlvbkFkZE9yRWRpdA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpgInkuK3nmoTlrozmlbTooYzmlbDmja4NCiAgICAgIHNlbGVjdGVkUm93czogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g5bel56iL5biI5omT5qC35Y2V5YWz6IGU6KGo5qC85pWw5o2uDQogICAgICBlbmdpbmVlclNhbXBsZU9yZGVyTGlzdDogW10sDQogICAgICAvLyDnoJTlj5Hpg6jpg6jpl6jmoJHliJfooagNCiAgICAgIHJlc2VhcmNoRGVwdERhdGFzOltdLA0KICAgICAgLy8g5omT5qC357G75Yir5a2X5YW4DQogICAgICBkeWxiT3B0aW9uczogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgdXNlcklkOiBudWxsLA0KICAgICAgICBuaWNrTmFtZTogbnVsbCwNCiAgICAgICAgc2FtcGxlT3JkZXJDb2RlOiBudWxsLA0KICAgICAgICBjb21wbGV0aW9uU3RhdHVzOiBudWxsLA0KICAgICAgICBzY2hlZHVsZWREYXRlOiBudWxsLA0KICAgICAgICBzdGFydERhdGU6IG51bGwsDQogICAgICAgIGFjdHVhbFN0YXJ0VGltZTogbnVsbCwNCiAgICAgICAgYWN0dWFsRmluaXNoVGltZTogbnVsbCwNCiAgICAgICAgZGVwdElkOiBudWxsLA0KICAgICAgICBkZXB0SWRzOiBbXSwNCiAgICAgICAgYXNzb2NpYXRpb25TdGF0dXM6IG51bGwsDQogICAgICAgIGN1c3RvbWVySWQ6IG51bGwsDQogICAgICAgIHByb2R1Y3ROYW1lOiBudWxsLA0KICAgICAgICBjb25maXJtQ29kZTogbnVsbCwNCiAgICAgICAgaXNPdmVyZHVlOiBudWxsLCAgLy8g5aKe6YC+5pyf5Lu75Yqh6L+H5ruk5Y+C5pWwDQogICAgICAgIGxhYm9yYXRvcnk6IG51bGwgIC8vIOWunumqjOWupOetm+mAieWPguaVsA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIHVzZXJJZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlt6XnqIvluIhJROS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIHNhbXBsZU9yZGVyQ29kZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiZPmoLfljZXnvJblj7fkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBjb21wbGV0aW9uU3RhdHVzOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWujOaIkOaDheWGteS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIHN0YXR1c09wdGlvbnM6IFtdLA0KICAgICAgc2VydmljZU1vZGVPcHRpb25zOiBbXSwNCiAgICAgIGRhdGVSYW5nZTogW10sIC8vIOaWsOWinueahOaXpeacn+iMg+WbtOetm+mAiQ0KICAgICAgc2NoZWR1bGVkRGF0ZVJhbmdlOiBbXSwNCiAgICAgIHN0YXJ0RGF0ZVJhbmdlOiBbXSwNCiAgICAgIGFjdHVhbFN0YXJ0VGltZVJhbmdlOiBbXSwNCiAgICAgIGFjdHVhbEZpbmlzaFRpbWVSYW5nZTogW10sDQogICAgICAvLyDml6XmnJ/pgInmi6nlmajphY3nva4NCiAgICAgIGRhdGFQaWNrZXJPcHRpb25zOiB7DQogICAgICAgIHNob3J0Y3V0czogW3sNCiAgICAgICAgICB0ZXh0OiAn5LuK5aSpJywNCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpOw0KICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3RvZGF5LCB0b2RheV0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSwgew0KICAgICAgICAgIHRleHQ6ICfmmKjlpKknLA0KICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICBjb25zdCB5ZXN0ZXJkYXkgPSBuZXcgRGF0ZSgpOw0KICAgICAgICAgICAgeWVzdGVyZGF5LnNldFRpbWUoeWVzdGVyZGF5LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQpOw0KICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3llc3RlcmRheSwgeWVzdGVyZGF5XSk7DQogICAgICAgICAgfQ0KICAgICAgICB9LCB7DQogICAgICAgICAgdGV4dDogJ+acgOi/keS4gOWRqCcsDQogICAgICAgICAgb25DbGljayhwaWNrZXIpIHsNCiAgICAgICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCk7DQogICAgICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCk7DQogICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiA3KTsNCiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIFtzdGFydCwgZW5kXSk7DQogICAgICAgICAgfQ0KICAgICAgICB9LCB7DQogICAgICAgICAgdGV4dDogJ+acgOi/keS4gOS4quaciCcsDQogICAgICAgICAgb25DbGljayhwaWNrZXIpIHsNCiAgICAgICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCk7DQogICAgICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCk7DQogICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiAzMCk7DQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfV0NCiAgICAgIH0sDQogICAgICAvLyDnirbmgIHmm7TmlrDlr7nor53moYYNCiAgICAgIHN0YXR1c09wZW46IGZhbHNlLA0KICAgICAgZGFzaGJvYXJkU3RhdHM6IHsNCiAgICAgICAgInRvdGFsIjogMCwNCiAgICAgICAgImNvbXBsZXRlZCI6IDAsDQogICAgICAgICJpblByb2dyZXNzIjogMCwNCiAgICAgICAgIm92ZXJkdWUiOiAwDQogICAgICB9LA0KICAgICAgLy8g5pu05pS55bel56iL5biI5a+56K+d5qGGDQogICAgICBjaGFuZ2VFbmdpbmVlck9wZW46IGZhbHNlLA0KICAgICAgLy8g5pu05pS55bel56iL5biI6KGo5Y2VDQogICAgICBjaGFuZ2VFbmdpbmVlckZvcm06IHsNCiAgICAgICAgaWQ6IG51bGwsDQogICAgICAgIHNhbXBsZU9yZGVyQ29kZTogbnVsbCwNCiAgICAgICAgY3VycmVudEVuZ2luZWVyTmFtZTogbnVsbCwNCiAgICAgICAgb2xkRW5naW5lZXJJZDogbnVsbCwNCiAgICAgICAgbmV3RW5naW5lZXJJZDogbnVsbCwNCiAgICAgICAgc2NoZWR1bGVkRGF0ZTogbnVsbCwNCiAgICAgICAgYWRqdXN0V29ya1NjaGVkdWxlOiAwDQogICAgICB9LA0KICAgICAgLy8g5pu05pS55bel56iL5biI6KGo5Y2V5qCh6aqMDQogICAgICBjaGFuZ2VFbmdpbmVlclJ1bGVzOiB7DQogICAgICAgIG5ld0VuZ2luZWVySWQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5bel56iL5biIIiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdLA0KICAgICAgICBzY2hlZHVsZWREYXRlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeaXpeacnyIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIC8vIOW3peeoi+W4iOmAiemhuQ0KICAgICAgZW5naW5lZXJPcHRpb25zOiBbXSwNCiAgICAgIC8vIOaJueasoeeuoeeQhuebuOWFsw0KICAgICAgYmF0Y2hNYW5hZ2VtZW50T3BlbjogZmFsc2UsDQogICAgICBhY3RpdmVCYXRjaGVzOiBbXSwgLy8g6L+b6KGM5Lit55qE5om55qyh5YiX6KGoDQogICAgICBhbGxCYXRjaGVzOiBbXSwgLy8g5omA5pyJ5om55qyh5YiX6KGoDQogICAgICBzZWxlY3RlZE9yZGVyRm9yQmF0Y2g6IG51bGwsDQogICAgICBjdXJyZW50QmF0Y2hSb3c6IG51bGwsIC8vIOW9k+WJjeaJueasoeeuoeeQhueahOihjOaVsOaNrg0KICAgICAgLy8g5om55qyh6K+m5oOF5a+56K+d5qGGDQogICAgICBiYXRjaERldGFpbE9wZW46IGZhbHNlLA0KICAgICAgYmF0Y2hEZXRhaWxEYXRhOiBudWxsLA0KICAgICAgLy8g57uT5p2f5om55qyh5a+56K+d5qGGDQogICAgICBmaW5pc2hCYXRjaE9wZW46IGZhbHNlLA0KICAgICAgZmluaXNoQmF0Y2hGb3JtOiB7DQogICAgICAgIHF1YWxpdHlFdmFsdWF0aW9uOiAnJywNCiAgICAgICAgcmVtYXJrOiAnJw0KICAgICAgfSwNCiAgICAgIGN1cnJlbnRGaW5pc2hCYXRjaDogbnVsbCwgLy8g5b2T5YmN6KaB57uT5p2f55qE5om55qyh77yI5Y2V5Liq5om55qyh5pe25L2/55So77yJDQogICAgICBmaW5pc2hCYXRjaE1vZGU6ICdhbGwnLCAvLyAnYWxsJyDooajnpLrnu5PmnZ/miYDmnInmibnmrKHvvIwnc2luZ2xlJyDooajnpLrnu5PmnZ/ljZXkuKrmibnmrKENCiAgICAgIC8vIOa3u+WKoOWunumqjOiusOW9leWvueivneahhg0KICAgICAgYWRkRXhwZXJpbWVudE9wZW46IGZhbHNlLA0KICAgICAgYWRkRXhwZXJpbWVudEZvcm06IHsNCiAgICAgICAgZXhwZXJpbWVudENvZGU6ICcnLA0KICAgICAgICBleHBlcmltZW50Tm90ZTogJycsDQogICAgICAgIHJlbWFyazogJycNCiAgICAgIH0sDQogICAgICBhZGRFeHBlcmltZW50UnVsZXM6IHsNCiAgICAgICAgZXhwZXJpbWVudENvZGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5a6e6aqM57yW5Y+35LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIGN1cnJlbnRCYXRjaEZvckV4cGVyaW1lbnQ6IG51bGwsDQogICAgICAvLyDmnKrmnaXml6XmnJ/pgInmi6nlmajphY3nva4NCiAgICAgIGZ1dHVyZURhdGVQaWNrZXJPcHRpb25zOiB7DQogICAgICAgIGRpc2FibGVkRGF0ZSh0aW1lKSB7DQogICAgICAgICAgcmV0dXJuIHRpbWUuZ2V0VGltZSgpIDwgRGF0ZS5ub3coKSAtIDguNjRlNzsgLy8g56aB55So5LuK5aSp5LmL5YmN55qE5pel5pyfDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBlbmdpbmVlclNlbGVjdFR5cGU6ICdzcGVjaWZpZWQnLA0KICAgICAgc2VhcmNoZWRFbmdpbmVlcnM6IFtdLA0KICAgICAgLy8g5pyq5YiG6YWN5omT5qC35Y2V5ZGK6K2mDQogICAgICB1bmFzc2lnbmVkT3JkZXJzOiBbXSwNCiAgICAgIHVuYXNzaWduZWRRdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogOA0KICAgICAgfSwNCiAgICAgIHVuYXNzaWduZWRUb3RhbDogMCwNCiAgICAgIC8vIOacquWIhumFjemdouadv+aKmOWPoOeKtuaAgQ0KICAgICAgaXNVbmFzc2lnbmVkUGFuZWxDb2xsYXBzZWQ6IHRydWUsDQogICAgICAvLyDlrozmiJDku7vliqHlr7nor53moYYNCiAgICAgIGZpbmlzaFRhc2tPcGVuOiBmYWxzZSwNCiAgICAgIGZpbmlzaFRhc2tMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGZpbmlzaFRhc2tGb3JtOiB7DQogICAgICAgIGxhYm9yYXRvcnlDb2RlOiAnJywNCiAgICAgICAgcmVtYXJrOiAnJw0KICAgICAgfSwNCiAgICAgIGV4cGVyaW1lbnRDb2RlTGlzdDogW10sIC8vIOWunumqjOWupOe8lueggeWIl+ihqA0KICAgICAgZmluaXNoVGFza1J1bGVzOiB7DQogICAgICAgIGxhYm9yYXRvcnlDb2RlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWunumqjOWupOe8lueggeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICBjdXJyZW50RmluaXNoUm93OiBudWxsLA0KICAgICAgLy8g5pu05paw5a6e6aqM5a6k57yW56CB5a+56K+d5qGGDQogICAgICB1cGRhdGVMYWJvcmF0b3J5Q29kZU9wZW46IGZhbHNlLA0KICAgICAgdXBkYXRlTGFib3JhdG9yeUNvZGVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHVwZGF0ZUxhYm9yYXRvcnlDb2RlRm9ybTogew0KICAgICAgICBsYWJvcmF0b3J5Q29kZTogJycsDQogICAgICAgIHJlbWFyazogJycNCiAgICAgIH0sDQogICAgICB1cGRhdGVMYWJvcmF0b3J5Q29kZVJ1bGVzOiB7DQogICAgICAgIGxhYm9yYXRvcnlDb2RlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWunumqjOWupOe8lueggeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICBjdXJyZW50VXBkYXRlTGFib3JhdG9yeUNvZGVSb3c6IG51bGwsDQogICAgICAvLyDlr7zlh7rpgInmi6nlr7nor53moYYNCiAgICAgIGV4cG9ydERpYWxvZ09wZW46IGZhbHNlLA0KICAgICAgZXhwb3J0T3B0aW9uczogW10sDQogICAgICBleHBvcnRMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHJlYWRvbmx5OiB0cnVlLA0KICAgICAgLy8g6aG555uu6K+m5oOF55u45YWzDQogICAgICBjdXJyZW50UHJvamVjdFR5cGU6IG51bGwsDQogICAgICBjb25maXJtSXRlbUNvZGVzOiBbXSwNCiAgICAgIGN1c3RvbWVyT3B0aW9uczogW10sDQogICAgICBpdGVtTmFtZXM6IFtdLA0KICAgICAgLy8g6amz5Zue5a+56K+d5qGGDQogICAgICByZWplY3REaWFsb2dPcGVuOiBmYWxzZSwNCiAgICAgIHJlamVjdExvYWRpbmc6IGZhbHNlLA0KICAgICAgcmVqZWN0Rm9ybTogew0KICAgICAgICBzYW1wbGVPcmRlckNvZGU6ICcnLA0KICAgICAgICByZWplY3RSZWFzb246ICcnDQogICAgICB9LA0KICAgICAgLy8g6YC+5pyf5pON5L2c5a+56K+d5qGGDQogICAgICBvdmVyZHVlT3BlcmF0aW9uT3BlbjogZmFsc2UsDQogICAgICBvdmVyZHVlT3BlcmF0aW9uTG9hZGluZzogZmFsc2UsDQogICAgICBvdmVyZHVlT3BlcmF0aW9uRm9ybTogew0KICAgICAgICBzYW1wbGVPcmRlckNvZGU6ICcnLA0KICAgICAgICBleHBlY3RlZFNhbXBsZVRpbWU6ICcnLA0KICAgICAgICByZWFzb25Gb3JOb1NhbXBsZTogJycsDQogICAgICAgIHNvbHV0aW9uOiAnJw0KICAgICAgfSwNCiAgICAgIGN1cnJlbnRPdmVyZHVlUm93OiBudWxsLA0KICAgICAgcmVqZWN0UnVsZXM6IHsNCiAgICAgICAgcmVqZWN0UmVhc29uOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumps+WbnueQhueUseS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICBjdXJyZW50UmVqZWN0Um93OiBudWxsLA0KICAgICAgLy8g5paw5aKe5omT5qC35Y2V5a+56K+d5qGGDQogICAgICBhZGRTYW1wbGVPcmRlck9wZW46IGZhbHNlLA0KICAgICAgYWRkU2FtcGxlT3JkZXJMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGFkZFNhbXBsZU9yZGVyRm9ybTogew0KICAgICAgICBsYWJObzogJycsDQogICAgICAgIGNhdGVnb3J5VGV4dDogJycsDQogICAgICAgIGR5bGI6ICcnLA0KICAgICAgICByZW1hcms6ICcnDQogICAgICB9LA0KICAgICAgYWRkU2FtcGxlT3JkZXJSdWxlczogew0KICAgICAgICBsYWJObzogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlrp7pqozlrqTkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIGNhdGVnb3J5VGV4dDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlk4HnsbvkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIGR5bGI6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5omT5qC35Y2V6Zq+5bqm57G75Yir5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgLy8g5Lqn5ZOB57G75Yir6YCJ6aG5DQogICAgICBzY2t4eHBnQ3BsYnM6IFtdDQogICAgfTsNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvKiog6K6h566X5piv5ZCm5Y+v5Lul57yW6L6R5om55qyhIC0g5Y+q5pyJ54q25oCB5Li6JzEnKOi/m+ihjOS4rSnnmoTmiZPmoLfljZXmiY3lj6/ku6XnvJbovpEgKi8NCiAgICBjYW5FZGl0QmF0Y2goKSB7DQogICAgICByZXR1cm4gdGhpcy5zZWxlY3RlZE9yZGVyRm9yQmF0Y2ggJiYgdGhpcy5zZWxlY3RlZE9yZGVyRm9yQmF0Y2guY29tcGxldGlvblN0YXR1cyA9PT0gMTsNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgLy8g6K6+572u6buY6K6k5pel5pyf6IyD5Zu05Li65b2T5aSpDQogICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpOw0KICAgIGNvbnN0IHRvZGF5U3RyID0gdG9kYXkuZ2V0RnVsbFllYXIoKSArICctJyArIFN0cmluZyh0b2RheS5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKSArICctJyArIFN0cmluZyh0b2RheS5nZXREYXRlKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgdGhpcy5kYXRlUmFuZ2UgPSBbdG9kYXlTdHIsIHRvZGF5U3RyXTsNCg0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIGN1c3RvbWVyQmFzZUFsbCgpLnRoZW4ocmVzPT4gdGhpcy5jdXN0b21lck9wdGlvbnMgPSByZXMpDQoNCiAgICB0aGlzLmdldERpY3RzKCJEWURfR0NTWlQiKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5zdGF0dXNPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICB9KTsNCiAgICB0aGlzLmdldERpY3RzKCJDVVNUT01FUl9TRVJWSUNFX01PREUiKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIHRoaXMuc2VydmljZU1vZGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICB9KTsNCiAgICB0aGlzLmdldERpY3RzKCJwcm9qZWN0X25yd19keWxiIikudGhlbihyZXNwb25zZSA9PiB7DQogICAgICB0aGlzLmR5bGJPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICB9KTsNCiAgICB0aGlzLmdldERpY3RzKCJTQ0tYWFBHX0NQTEIiKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIHRoaXMuc2NreHhwZ0NwbGJzID0gcmVzcG9uc2UuZGF0YTsNCiAgICB9KTsNCiAgICB0aGlzLmxvYWREYXNoYm9hcmRTdGF0cygpOw0KICAgIC8vIOiOt+WPlueglOWPkemDqOmDqOmXqOWIl+ihqA0KICAgIGdldFJlc2VhcmNoRGVwYXJ0bWVudHMoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIHRoaXMucmVzZWFyY2hEZXB0RGF0YXMgPSB0aGlzLmhhbmRsZVRyZWUocmVzcG9uc2UuZGF0YSwgImRlcHRJZCIpOw0KICAgIH0pOw0KICAgIC8vIOiOt+WPluacquWIhumFjeaJk+agt+WNlQ0KICAgIHRoaXMuaGFuZGxlVW5hc3NpZ25lZE9yZGVycygpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOiuoeeul+S6p+WTgS/pobnnm67nrYnnuqcgKi8NCiAgICBnZXRQcm9qZWN0TGV2ZWwocm93KSB7DQogICAgICBjb25zdCBjdXN0b21lckxldmVsID0gcm93LmN1c3RvbWVyTGV2ZWwgfHwgJyc7DQogICAgICBjb25zdCBwcm9qZWN0TGV2ZWwgPSByb3cucHJvamVjdExldmVsIHx8ICcnOw0KDQogICAgICAvLyDlpoLmnpwgcHJvamVjdExldmVsIOaYr+epuuWtl+espuS4suaIliAiLyIg5bCx5LiN55u45YqgDQogICAgICBpZiAocHJvamVjdExldmVsID09PSAnJyB8fCBwcm9qZWN0TGV2ZWwgPT09ICcvJykgew0KICAgICAgICByZXR1cm4gY3VzdG9tZXJMZXZlbDsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIGN1c3RvbWVyTGV2ZWwgKyBwcm9qZWN0TGV2ZWw7DQogICAgfSwNCiAgICAvKiog5p+l6K+i5bel56iL5biI5omT5qC35Y2V5YWz6IGU5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7IC4uLnRoaXMucXVlcnlQYXJhbXMgfTsNCiAgICAgIHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHBhcmFtcywgdGhpcy5zY2hlZHVsZWREYXRlUmFuZ2UsJ1NjaGVkdWxlZERhdGUnKQ0KICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLnN0YXJ0RGF0ZVJhbmdlLCdTdGFydERhdGUnKQ0KICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLmFjdHVhbFN0YXJ0VGltZVJhbmdlLCdBY3R1YWxTdGFydFRpbWUnKQ0KICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLmFjdHVhbEZpbmlzaFRpbWVSYW5nZSwnQWN0dWFsRmluaXNoVGltZScpDQogICAgICAvLyDmuIXnqbrkuYvliY3nmoTml6XmnJ/ojIPlm7Tlj4LmlbDvvIzmoLnmja7lvZPliY3nirbmgIHph43mlrDorr7nva4NCiAgICAgIGRlbGV0ZSBwYXJhbXMuYmVnaW5EYXRlUmFuZ2U7DQogICAgICBkZWxldGUgcGFyYW1zLmVuZERhdGVSYW5nZTsNCiAgICAgIC8vIOa3u+WKoOaXpeacn+iMg+WbtOetm+mAieWPguaVsA0KICAgICAgaWYgKHRoaXMuZGF0ZVJhbmdlICYmIHRoaXMuZGF0ZVJhbmdlLmxlbmd0aCA9PT0gMikgew0KICAgICAgICBwYXJhbXMuYmVnaW5EYXRlUmFuZ2UgPSB0aGlzLmRhdGVSYW5nZVswXTsNCiAgICAgICAgcGFyYW1zLmVuZERhdGVSYW5nZSA9IHRoaXMuZGF0ZVJhbmdlWzFdOw0KICAgICAgfQ0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIHBhcmFtcy5hc3NvY2lhdGlvblN0YXR1cyA9IDENCiAgICAgIGxpc3RFbmdpbmVlclNhbXBsZU9yZGVyKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZW5naW5lZXJTYW1wbGVPcmRlckxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgdXNlcklkOiBudWxsLA0KICAgICAgICBzYW1wbGVPcmRlckNvZGU6IG51bGwsDQogICAgICAgIHNlcnZpY2VNb2RlOiBudWxsLA0KICAgICAgICBkaWZmaWN1bHR5TGV2ZWxJZDogbnVsbCwNCiAgICAgICAgY29tcGxldGlvblN0YXR1czogbnVsbCwNCiAgICAgICAgc2NoZWR1bGVkRGF0ZTogbnVsbCwNCiAgICAgICAgYWN0dWFsU3RhcnRUaW1lOiBudWxsLA0KICAgICAgICBhY3R1YWxGaW5pc2hUaW1lOiBudWxsLA0KICAgICAgICBhY3R1YWxNYW5Ib3VyczogbnVsbCwNCiAgICAgICAgZXN0aW1hdGVkTWFuSG91cnM6IG51bGwsDQogICAgICAgIHN0YW5kYXJkTWFuSG91cnM6IG51bGwsDQogICAgICAgIHF1YWxpdHlFdmFsdWF0aW9uOiBudWxsLA0KICAgICAgICBpc0xvY2tlZDogMCwNCiAgICAgICAgc3RhcnREYXRlOiBudWxsLA0KICAgICAgICBlbmREYXRlOiBudWxsLA0KICAgICAgICBjaGVja1R5cGU6IG51bGwsDQogICAgICAgIHNhbXBsZU9yZGVyUmVtYXJrOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgIHJlbWFyazogbnVsbA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy51bmFzc2lnbmVkUXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgIHRoaXMubG9hZERhc2hib2FyZFN0YXRzKCk7DQogICAgICB0aGlzLmhhbmRsZVVuYXNzaWduZWRPcmRlcnMoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbXSAvLyDph43nva7ml6XmnJ/ojIPlm7QNCiAgICAgIHRoaXMuc2NoZWR1bGVkRGF0ZVJhbmdlID0gW10NCiAgICAgIHRoaXMuc3RhcnREYXRlUmFuZ2UgPSBbXQ0KICAgICAgdGhpcy5hY3R1YWxTdGFydFRpbWVSYW5nZSA9IFtdDQogICAgICB0aGlzLmFjdHVhbEZpbmlzaFRpbWVSYW5nZSA9IFtdDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQ0KICAgICAgdGhpcy5zZWxlY3RlZFJvd3MgPSBzZWxlY3Rpb24NCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5bel56iL5biI5omT5qC35Y2V5YWz6IGUIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkcw0KICAgICAgZ2V0RW5naW5lZXJTYW1wbGVPcmRlcihpZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55bel56iL5biI5omT5qC35Y2V5YWz6IGUIjthZGRFbmdpbmVlclNhbXBsZU9yZGVyDQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVFbmdpbmVlclNhbXBsZU9yZGVyKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgdGhpcy5sb2FkRGFzaGJvYXJkU3RhdHMoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRFbmdpbmVlclNhbXBsZU9yZGVyKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgdGhpcy5sb2FkRGFzaGJvYXJkU3RhdHMoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5bel56iL5biI5omT5qC35Y2V5YWz6IGU57yW5Y+35Li6IicgKyByb3cuc2FtcGxlT3JkZXJDb2RlICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGRlbEVuZ2luZWVyU2FtcGxlT3JkZXIoaWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgICAgdGhpcy5sb2FkRGFzaGJvYXJkU3RhdHMoKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsgfSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgLy8g6YeN572u5a+85Ye66YCJ6aG55bm25pi+56S66YCJ5oup5a+56K+d5qGGDQogICAgICB0aGlzLmV4cG9ydE9wdGlvbnMgPSBbXTsNCiAgICAgIHRoaXMuZXhwb3J0RGlhbG9nT3BlbiA9IHRydWU7DQogICAgfSwNCiAgICAvKiog56Gu6K6k5a+85Ye65pON5L2cICovDQogICAgY29uZmlybUV4cG9ydCgpIHsNCiAgICAgIGlmICh0aGlzLmV4cG9ydE9wdGlvbnMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36Iez5bCR6YCJ5oup5LiA56eN5a+85Ye657G75Z6LJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IHRydWU7DQogICAgICB0aGlzLmV4ZWN1dGVFeHBvcnRzKDApOw0KICAgIH0sDQoNCiAgICAvKiog5Liy6KGM5omn6KGM5a+85Ye65pON5L2cICovDQogICAgZXhlY3V0ZUV4cG9ydHMoaW5kZXgpIHsNCiAgICAgIGlmIChpbmRleCA+PSB0aGlzLmV4cG9ydE9wdGlvbnMubGVuZ3RoKSB7DQogICAgICAgIC8vIOaJgOacieWvvOWHuuWujOaIkA0KICAgICAgICB0aGlzLmV4cG9ydExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5leHBvcnREaWFsb2dPcGVuID0gZmFsc2U7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5a+85Ye65a6M5oiQ77yM5YWx5a+85Ye6JHt0aGlzLmV4cG9ydE9wdGlvbnMubGVuZ3RofeS4quaWh+S7tmApOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGNvbnN0IG9wdGlvbiA9IHRoaXMuZXhwb3J0T3B0aW9uc1tpbmRleF07DQogICAgICBjb25zdCBhc3NvY2lhdGlvblN0YXR1cyA9IG9wdGlvbiA9PT0gJ2Fzc2lnbmVkJyA/IDEgOiAwOw0KICAgICAgY29uc3QgdHlwZU5hbWUgPSBvcHRpb24gPT09ICdhc3NpZ25lZCcgPyAn5bey5YiG6YWNJyA6ICfmnKrliIbphY0nOw0KDQogICAgICB0aGlzLmRvRXhwb3J0KGFzc29jaWF0aW9uU3RhdHVzLCBvcHRpb24pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYCR7dHlwZU5hbWV95omT5qC35Y2V5a+85Ye65oiQ5YqfYCk7DQogICAgICAgIC8vIOe7p+e7reWvvOWHuuS4i+S4gOS4qg0KICAgICAgICB0aGlzLmV4ZWN1dGVFeHBvcnRzKGluZGV4ICsgMSk7DQogICAgICB9KS5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYCR7dHlwZU5hbWV95omT5qC35Y2V5a+85Ye65aSx6LSlOiAke2Vycm9yLm1lc3NhZ2UgfHwgZXJyb3J9YCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmiafooYzlr7zlh7rmk43kvZwgKi8NCiAgICBkb0V4cG9ydChhc3NvY2lhdGlvblN0YXR1cywgZXhwb3J0VHlwZSkgew0KICAgICAgbGV0IHBhcmFtcyA9IHsgLi4udGhpcy5xdWVyeVBhcmFtcyB9Ow0KICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLnNjaGVkdWxlZERhdGVSYW5nZSwnU2NoZWR1bGVkRGF0ZScpDQogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuc3RhcnREYXRlUmFuZ2UsJ1N0YXJ0RGF0ZScpDQogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuYWN0dWFsU3RhcnRUaW1lUmFuZ2UsJ0FjdHVhbFN0YXJ0VGltZScpDQogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuYWN0dWFsRmluaXNoVGltZVJhbmdlLCdBY3R1YWxGaW5pc2hUaW1lJykNCiAgICAgIC8vIOa4heepuuS5i+WJjeeahOaXpeacn+iMg+WbtOWPguaVsO+8jOagueaNruW9k+WJjeeKtuaAgemHjeaWsOiuvue9rg0KICAgICAgZGVsZXRlIHBhcmFtcy5iZWdpbkRhdGVSYW5nZTsNCiAgICAgIGRlbGV0ZSBwYXJhbXMuZW5kRGF0ZVJhbmdlOw0KICAgICAgLy8g5re75Yqg5pel5pyf6IyD5Zu0562b6YCJ5Y+C5pWwDQogICAgICBpZiAodGhpcy5kYXRlUmFuZ2UgJiYgdGhpcy5kYXRlUmFuZ2UubGVuZ3RoID09PSAyKSB7DQogICAgICAgIHBhcmFtcy5iZWdpbkRhdGVSYW5nZSA9IHRoaXMuZGF0ZVJhbmdlWzBdOw0KICAgICAgICBwYXJhbXMuZW5kRGF0ZVJhbmdlID0gdGhpcy5kYXRlUmFuZ2VbMV07DQogICAgICB9DQogICAgICBwYXJhbXMuYXNzb2NpYXRpb25TdGF0dXMgPSBhc3NvY2lhdGlvblN0YXR1czsNCiAgICAgIHBhcmFtcy5leHBvcnRUeXBlID0gZXhwb3J0VHlwZTsNCiAgICAgIHJldHVybiBleHBvcnRFbmdpbmVlclNhbXBsZU9yZGVyKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZG93bmxvYWQocmVzcG9uc2UubXNnKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaJp+ihjOWvvOWHuuaJk+agt+WNle+8iOW3suWIhumFjS/mnKrliIbphY3vvIkgKi8NCiAgICBkb0V4cG9ydFNhbXBsZU9yZGVyKCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHsgLi4udGhpcy5xdWVyeVBhcmFtcyB9Ow0KICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLnNjaGVkdWxlZERhdGVSYW5nZSwnU2NoZWR1bGVkRGF0ZScpDQogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuc3RhcnREYXRlUmFuZ2UsJ1N0YXJ0RGF0ZScpDQogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuYWN0dWFsU3RhcnRUaW1lUmFuZ2UsJ0FjdHVhbFN0YXJ0VGltZScpDQogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuYWN0dWFsRmluaXNoVGltZVJhbmdlLCdBY3R1YWxGaW5pc2hUaW1lJykNCiAgICAgIC8vIOa4heepuuS5i+WJjeeahOaXpeacn+iMg+WbtOWPguaVsO+8jOagueaNruW9k+WJjeeKtuaAgemHjeaWsOiuvue9rg0KICAgICAgZGVsZXRlIHBhcmFtcy5iZWdpbkRhdGVSYW5nZTsNCiAgICAgIGRlbGV0ZSBwYXJhbXMuZW5kRGF0ZVJhbmdlOw0KICAgICAgLy8g5re75Yqg5pel5pyf6IyD5Zu0562b6YCJ5Y+C5pWwDQogICAgICBpZiAodGhpcy5kYXRlUmFuZ2UgJiYgdGhpcy5kYXRlUmFuZ2UubGVuZ3RoID09PSAyKSB7DQogICAgICAgIHBhcmFtcy5iZWdpbkRhdGVSYW5nZSA9IHRoaXMuZGF0ZVJhbmdlWzBdOw0KICAgICAgICBwYXJhbXMuZW5kRGF0ZVJhbmdlID0gdGhpcy5kYXRlUmFuZ2VbMV07DQogICAgICB9DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInvvIjlt7LliIbphY0v5pyq5YiG6YWN77yJ5omT5qC35Y2V5YWz6IGU5pWw5o2u77yfJywgIuaPkOekuiIsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgdHlwZTogIndhcm5pbmciDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgcmV0dXJuIGV4cG9ydEVuZ2luZWVyU2FtcGxlT3JkZXIocGFyYW1zKTsNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7DQogICAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWvvOWHuuaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLmV4cG9ydExvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOeCueWHuyLlvIDlp4si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlU3RhcnQocm93KSB7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHlsIbmiZPmoLfljZXvvJonICsgcm93LnNhbXBsZU9yZGVyQ29kZSArICfmoIforrDkuLrov5vooYzkuK3lkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB1cGRhdGVTYW1wbGVPcmRlclN0YXR1cyh7DQogICAgICAgICAgaWQ6IHJvdy5pZCwNCiAgICAgICAgICBzdGF0dXM6ICcxJw0KICAgICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoJ+aJk+agt+WNleW3suiuvue9ruS4uuW3suW8gOWniycpOw0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMubG9hZERhc2hib2FyZFN0YXRzKCk7DQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5LiL6L295omT5qC35Y2V5pON5L2cICovDQogICAgaGFuZGxlRG93bmxvYWRTYW1wbGVPcmRlcihyb3cpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWvvOWHuuaJk+agt+WNlT8nLCAi5o+Q56S6Iiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmV4cG9ydExvYWRpbmcgPSB0cnVlOw0KICAgICAgICByZXR1cm4gZXhwb3J0TnJ3SXRlbSh7aXRlbUlkOiByb3cuaXRlbUlkLHByb2plY3RJZDogcm93LnByb2plY3RJZH0pDQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5kb3dubG9hZChyZXNwb25zZS5tc2cpOw0KICAgICAgICB0aGlzLmV4cG9ydExvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDmibnph4/lr7zlh7rmiZPmoLfljZXku7vliqHmk43kvZwgKi8NCiAgICBoYW5kbGVCYXRjaEV4cG9ydE5ydygpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkUm93cy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqeimgeWvvOWHuueahOaJk+agt+WNleS7u+WKoSIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOaehOmAoOaJuemHj+WvvOWHuuaVsOaNru+8jOS8oOmAkml0ZW1JZOWSjHByb2plY3RJZA0KICAgICAgY29uc3QgZXhwb3J0RGF0YSA9IHRoaXMuc2VsZWN0ZWRSb3dzLm1hcChyb3cgPT4gKHsNCiAgICAgICAgaXRlbUlkOiByb3cuaXRlbUlkLA0KICAgICAgICBwcm9qZWN0SWQ6IHJvdy5wcm9qZWN0SWQNCiAgICAgIH0pKTsNCg0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA6YCJ5omT5qC35Y2V5Lu75Yqh55qE5YaF5a6554mp5pWw5o2u77yfJywgIuaPkOekuiIsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgdHlwZTogIndhcm5pbmciDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgcmV0dXJuIGV4cG9ydE11bHRpcGxlTnJ3KGV4cG9ydERhdGEpOw0KICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZG93bmxvYWQocmVzcG9uc2UubXNnKTsNCiAgICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5a+85Ye65oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog54K55Ye7IuWujOaIkCLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVGaW5pc2gocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRGaW5pc2hSb3cgPSByb3c7DQogICAgICB0aGlzLmZpbmlzaFRhc2tGb3JtLmxhYm9yYXRvcnlDb2RlID0gJyc7DQogICAgICB0aGlzLmZpbmlzaFRhc2tGb3JtLnJlbWFyayA9ICcnOw0KICAgICAgdGhpcy5maW5pc2hUYXNrT3BlbiA9IHRydWU7DQogICAgICAvLyDliqDovb3lrp7pqozlrqTnvJbnoIHliJfooagNCiAgICAgIHRoaXMubG9hZEV4cGVyaW1lbnRDb2RlTGlzdChyb3cuaWQpOw0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLiRyZWZzLmZpbmlzaFRhc2tGb3JtLmNsZWFyVmFsaWRhdGUoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOehruiupOWujOaIkOS7u+WKoSAqLw0KICAgIGNvbmZpcm1GaW5pc2hUYXNrKCkgew0KICAgICAgdGhpcy4kcmVmcy5maW5pc2hUYXNrRm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIC8vIOWkhOeQhuWunumqjOWupOe8luWPt+Wtl+autQ0KICAgICAgICBsZXQgbGFib3JhdG9yeUNvZGUgPSB0aGlzLmZpbmlzaFRhc2tGb3JtLmxhYm9yYXRvcnlDb2RlOw0KDQogICAgICAgIC8vIOWmguaenOaYr+aVsOe7hOexu+Wei++8jOS9v+eUqOmAl+WPt+aLvOaOpQ0KICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShsYWJvcmF0b3J5Q29kZSkpIHsNCiAgICAgICAgICBsYWJvcmF0b3J5Q29kZSA9IGxhYm9yYXRvcnlDb2RlLmpvaW4oIiwiKTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLmZpbmlzaFRhc2tMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICB1cGRhdGVTYW1wbGVPcmRlclN0YXR1cyh7DQogICAgICAgICAgICBpZDogdGhpcy5jdXJyZW50RmluaXNoUm93LmlkLA0KICAgICAgICAgICAgc3RhdHVzOiAnMicsDQogICAgICAgICAgICBsYWJvcmF0b3J5Q29kZTogbGFib3JhdG9yeUNvZGUsDQogICAgICAgICAgICByZW1hcms6IHRoaXMuZmluaXNoVGFza0Zvcm0ucmVtYXJrDQogICAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLmZpbmlzaFRhc2tMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLmZpbmlzaFRhc2tPcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoJ+aJk+agt+WNleW3suiuvue9ruS4uuW3suWujOaIkCcpOw0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB0aGlzLmxvYWREYXNoYm9hcmRTdGF0cygpOw0KICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuZmluaXNoVGFza0xvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5LuO5om55qyh566h55CG5a+56K+d5qGG5a6M5oiQ5Lu75YqhICovDQogICAgaGFuZGxlRmluaXNoRnJvbUJhdGNoKCkgew0KICAgICAgdGhpcy5jdXJyZW50RmluaXNoUm93ID0gdGhpcy5jdXJyZW50QmF0Y2hSb3c7DQogICAgICB0aGlzLmZpbmlzaFRhc2tGb3JtLmxhYm9yYXRvcnlDb2RlID0gJyc7DQogICAgICB0aGlzLmZpbmlzaFRhc2tGb3JtLnJlbWFyayA9ICcnOw0KICAgICAgdGhpcy5maW5pc2hUYXNrT3BlbiA9IHRydWU7DQogICAgICAvLyDliqDovb3lrp7pqozlrqTnvJbnoIHliJfooagNCiAgICAgIHRoaXMubG9hZEV4cGVyaW1lbnRDb2RlTGlzdCh0aGlzLmN1cnJlbnRCYXRjaFJvdy5pZCk7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnMuZmluaXNoVGFza0Zvcm0uY2xlYXJWYWxpZGF0ZSgpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5LuO5om55qyh566h55CG5a+56K+d5qGG5byA5aeL5Lu75YqhICovDQogICAgaGFuZGxlU3RhcnRGcm9tQmF0Y2goKSB7DQogICAgICBjb25zdCByb3cgPSB0aGlzLmN1cnJlbnRCYXRjaFJvdzsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuimgeWwhuaJk+agt+WNle+8micgKyByb3cuc2FtcGxlT3JkZXJDb2RlICsgJ+agh+iusOS4uui/m+ihjOS4reWQl++8nycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHVwZGF0ZVNhbXBsZU9yZGVyU3RhdHVzKHsNCiAgICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICAgIHN0YXR1czogJzEnDQogICAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygn5omT5qC35Y2V5bey6K6+572u5Li65bey5byA5aeLJyk7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgdGhpcy5sb2FkRGFzaGJvYXJkU3RhdHMoKTsNCiAgICAgICAgICAvLyDmm7TmlrDlvZPliY3mibnmrKHooYzmlbDmja7nmoTnirbmgIENCiAgICAgICAgICB0aGlzLmN1cnJlbnRCYXRjaFJvdy5jb21wbGV0aW9uU3RhdHVzID0gJzEnOw0KICAgICAgICAgIHRoaXMuc2VsZWN0ZWRPcmRlckZvckJhdGNoLmNvbXBsZXRpb25TdGF0dXMgPSAxOw0KICAgICAgICAgIGlmICh0aGlzLnNlbGVjdGVkT3JkZXJGb3JCYXRjaCkgew0KICAgICAgICAgICAgdGhpcy5sb2FkQmF0Y2hEYXRhKHRoaXMuc2VsZWN0ZWRPcmRlckZvckJhdGNoLmlkKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog54K55Ye7IuabtOaWsOWunumqjOWupOe8lueggSLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGVMYWJvcmF0b3J5Q29kZShyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudFVwZGF0ZUxhYm9yYXRvcnlDb2RlUm93ID0gcm93Ow0KICAgICAgLy8g5Zue5pi+5b2T5YmN55qE5a6e6aqM5a6k57yW56CB5ZKM5aSH5rOoDQogICAgICB0aGlzLnVwZGF0ZUxhYm9yYXRvcnlDb2RlRm9ybS5sYWJvcmF0b3J5Q29kZSA9IHJvdy5sYWJvcmF0b3J5Q29kZSB8fCAnJzsNCiAgICAgIHRoaXMudXBkYXRlTGFib3JhdG9yeUNvZGVGb3JtLnJlbWFyayA9IHJvdy5yZW1hcmsgfHwgJyc7DQogICAgICB0aGlzLnVwZGF0ZUxhYm9yYXRvcnlDb2RlT3BlbiA9IHRydWU7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnMudXBkYXRlTGFib3JhdG9yeUNvZGVGb3JtLmNsZWFyVmFsaWRhdGUoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOehruiupOabtOaWsOWunumqjOWupOe8lueggSAqLw0KICAgIGNvbmZpcm1VcGRhdGVMYWJvcmF0b3J5Q29kZSgpIHsNCiAgICAgIHRoaXMuJHJlZnMudXBkYXRlTGFib3JhdG9yeUNvZGVGb3JtLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgdGhpcy51cGRhdGVMYWJvcmF0b3J5Q29kZUxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgIHVwZGF0ZVNhbXBsZU9yZGVyU3RhdHVzKHsNCiAgICAgICAgICAgIGlkOiB0aGlzLmN1cnJlbnRVcGRhdGVMYWJvcmF0b3J5Q29kZVJvdy5pZCwNCiAgICAgICAgICAgIHN0YXR1czogdGhpcy5jdXJyZW50VXBkYXRlTGFib3JhdG9yeUNvZGVSb3cuY29tcGxldGlvblN0YXR1cywgLy8g5L+d5oyB5b2T5YmN54q25oCBDQogICAgICAgICAgICBsYWJvcmF0b3J5Q29kZTogdGhpcy51cGRhdGVMYWJvcmF0b3J5Q29kZUZvcm0ubGFib3JhdG9yeUNvZGUsDQogICAgICAgICAgICByZW1hcms6IHRoaXMudXBkYXRlTGFib3JhdG9yeUNvZGVGb3JtLnJlbWFyaw0KICAgICAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgdGhpcy51cGRhdGVMYWJvcmF0b3J5Q29kZUxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMudXBkYXRlTGFib3JhdG9yeUNvZGVPcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoJ+WunumqjOWupOe8lueggeabtOaWsOaIkOWKnycpOw0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB0aGlzLmxvYWREYXNoYm9hcmRTdGF0cygpOw0KICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMudXBkYXRlTGFib3JhdG9yeUNvZGVMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOeCueWHuyLpqbPlm54i5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUmVqZWN0KHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50UmVqZWN0Um93ID0gcm93Ow0KICAgICAgdGhpcy5yZWplY3RGb3JtLnNhbXBsZU9yZGVyQ29kZSA9IHJvdy5zYW1wbGVPcmRlckNvZGU7DQogICAgICB0aGlzLnJlamVjdEZvcm0ucmVqZWN0UmVhc29uID0gJyc7DQogICAgICB0aGlzLnJlamVjdERpYWxvZ09wZW4gPSB0cnVlOw0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLiRyZWZzLnJlamVjdEZvcm0uY2xlYXJWYWxpZGF0ZSgpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog56Gu6K6k6amz5ZueICovDQogICAgY29uZmlybVJlamVjdCgpIHsNCiAgICAgIHRoaXMuJHJlZnMucmVqZWN0Rm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMucmVqZWN0TG9hZGluZyA9IHRydWU7DQogICAgICAgICAgdXBkYXRlU2FtcGxlT3JkZXJTdGF0dXMoew0KICAgICAgICAgICAgaWQ6IHRoaXMuY3VycmVudFJlamVjdFJvdy5pZCwNCiAgICAgICAgICAgIHN0YXR1czogJzMnLA0KICAgICAgICAgICAgcmVqZWN0UmVhc29uOiB0aGlzLnJlamVjdEZvcm0ucmVqZWN0UmVhc29uDQogICAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLnJlamVjdExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMucmVqZWN0RGlhbG9nT3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCfmiZPmoLfljZXlt7LpqbPlm54nKTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgdGhpcy5sb2FkRGFzaGJvYXJkU3RhdHMoKTsNCiAgICAgICAgICAgIC8vIOWmguaenOaYr+acquWIhumFjeaJk+agt+WNleeahOmps+Wbnu+8jOS5n+mcgOimgeWIt+aWsOacquWIhumFjeWIl+ihqA0KICAgICAgICAgICAgdGhpcy5oYW5kbGVVbmFzc2lnbmVkT3JkZXJzKCk7DQogICAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5yZWplY3RMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOeCueWHuyLpgL7mnJ/mk43kvZwi5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlT3ZlcmR1ZU9wZXJhdGlvbihyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudE92ZXJkdWVSb3cgPSByb3c7DQogICAgICB0aGlzLm92ZXJkdWVPcGVyYXRpb25Gb3JtLnNhbXBsZU9yZGVyQ29kZSA9IHJvdy5zYW1wbGVPcmRlckNvZGU7DQogICAgICB0aGlzLm92ZXJkdWVPcGVyYXRpb25Gb3JtLmV4cGVjdGVkU2FtcGxlVGltZSA9IHJvdy5leHBlY3RlZFNhbXBsZVRpbWU7DQogICAgICB0aGlzLm92ZXJkdWVPcGVyYXRpb25Gb3JtLnJlYXNvbkZvck5vU2FtcGxlID0gcm93LnJlYXNvbkZvck5vU2FtcGxlOw0KICAgICAgdGhpcy5vdmVyZHVlT3BlcmF0aW9uRm9ybS5zb2x1dGlvbiA9IHJvdy5zb2x1dGlvbjsNCiAgICAgIHRoaXMub3ZlcmR1ZU9wZXJhdGlvbk9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLiRyZWZzLm92ZXJkdWVPcGVyYXRpb25Gb3JtLmNsZWFyVmFsaWRhdGUoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOehruiupOmAvuacn+aTjeS9nCAqLw0KICAgIGNvbmZpcm1PdmVyZHVlT3BlcmF0aW9uKCkgew0KICAgICAgdGhpcy4kcmVmcy5vdmVyZHVlT3BlcmF0aW9uRm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMub3ZlcmR1ZU9wZXJhdGlvbkxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgIHVwZGF0ZVNhbXBsZU9yZGVyU3RhdHVzKHsNCiAgICAgICAgICAgIGlkOiB0aGlzLmN1cnJlbnRPdmVyZHVlUm93LmlkLA0KICAgICAgICAgICAgc3RhdHVzOiAiMTEiLCAvLyDnibnmrorlpITnkIbvvIzlj6rmm7TmlrDigJzpgL7mnJ/mg4XlhrXigJ3loavlhpnnmoTlrZfmrrUNCiAgICAgICAgICAgIGV4cGVjdGVkU2FtcGxlVGltZTogdGhpcy5vdmVyZHVlT3BlcmF0aW9uRm9ybS5leHBlY3RlZFNhbXBsZVRpbWUsDQogICAgICAgICAgICByZWFzb25Gb3JOb1NhbXBsZTogdGhpcy5vdmVyZHVlT3BlcmF0aW9uRm9ybS5yZWFzb25Gb3JOb1NhbXBsZSwNCiAgICAgICAgICAgIHNvbHV0aW9uOiB0aGlzLm92ZXJkdWVPcGVyYXRpb25Gb3JtLnNvbHV0aW9uDQogICAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLm92ZXJkdWVPcGVyYXRpb25Mb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLm92ZXJkdWVPcGVyYXRpb25PcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoJ+mAvuacn+aTjeS9nOW3suWujOaIkCcpOw0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB0aGlzLmxvYWREYXNoYm9hcmRTdGF0cygpOw0KICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMub3ZlcmR1ZU9wZXJhdGlvbkxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5p+l55yL5omT5qC35Y2V6K+m5oOFICovDQogICAgYXN5bmMgb3JkZXJEZXRhaWwocm93KSB7DQogICAgICB0cnkgew0KICAgICAgICAvL+iOt+WPluaJp+ihjElEDQogICAgICAgIGNvbnN0IG9yZGVySWQgPSByb3cucHJvamVjdE9yZGVySWQ7DQogICAgICAgIGxldCBkYXRhID0gYXdhaXQgZ2V0RXhlY3V0aW9uQnlPcmRlckluZm8ob3JkZXJJZCk7DQogICAgICAgIGlmKGRhdGEhPW51bGwgJiYgIWlzTnVsbChkYXRhLmlkKSl7DQogICAgICAgICAgICBsZXQgaWQgPSBkYXRhLmlkOw0KICAgICAgICAgICAgdGhpcy4kcmVmcy5leGVjdXRpb25BZGRPckVkaXQub3BlbiA9IHRydWU7DQogICAgICAgICAgICBhd2FpdCB0aGlzLiRuZXh0VGljaygpDQogICAgICAgICAgICB0aGlzLiRyZWZzLmV4ZWN1dGlvbkFkZE9yRWRpdC5yZXNldCgpDQogICAgICAgICAgICB0aGlzLiRyZWZzLmV4ZWN1dGlvbkFkZE9yRWRpdC5zaG93KGlkLCAxKQ0KICAgICAgICB9ZWxzZXsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bmlbDmja7lpLHotKUnKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5p+l55yL6aG555uu6K+m5oOF5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5p+l55yL6aG555uu6K+m5oOF5aSx6LSlOiAnICsgKGVycm9yLm1lc3NhZ2UgfHwgJ+acquefpemUmeivrycpKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOmUgeWumueKtuaAgeaUueWPmOWkhOeQhiAqLw0KICAgIGhhbmRsZUxvY2tDaGFuZ2UodmFsdWUpIHsNCiAgICAgIGlmICh2YWx1ZSA9PT0gMSkgew0KICAgICAgICB0aGlzLiRjb25maXJtKCfplIHlrprlkI7lsIbml6Dms5Xoh6rliqjosIPmlbTmraTljZXnmoTmjpLljZXml6XmnJ/vvIzmmK/lkKbnoa7orqTplIHlrprvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5mb3JtLmlzTG9ja2VkID0gMTsNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuZm9ybS5pc0xvY2tlZCA9IDA7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOihqOagvOS4remUgeWumueKtuaAgeaUueWPmOWkhOeQhiAqLw0KICAgIGhhbmRsZVRhYmxlTG9ja0NoYW5nZSh2YWx1ZSwgcm93KSB7DQogICAgICBpZiAodmFsdWUgPT09IDEpIHsNCiAgICAgICAgdGhpcy4kY29uZmlybSgn6ZSB5a6a5ZCO5bCG5peg5rOV6Ieq5Yqo6LCD5pW05q2k5Y2V55qE5o6S5Y2V5pel5pyf77yM5piv5ZCm56Gu6K6k6ZSB5a6a77yfJywgJ+aPkOekuicsIHsNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIC8vIOiwg+eUqOabtOaWsOaOpeWPow0KICAgICAgICAgIHVwZGF0ZUVuZ2luZWVyU2FtcGxlT3JkZXIoew0KICAgICAgICAgICAgaWQ6IHJvdy5pZCwNCiAgICAgICAgICAgIGlzTG9ja2VkOiAxDQogICAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIumUgeWumuaIkOWKnyIpOw0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB0aGlzLmxvYWREYXNoYm9hcmRTdGF0cygpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgLy8g5Y+W5raI5pON5L2c77yM5oGi5aSN5Y6f5YC8DQogICAgICAgICAgcm93LmlzTG9ja2VkID0gMDsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDop6PplIHmk43kvZwNCiAgICAgICAgdXBkYXRlRW5naW5lZXJTYW1wbGVPcmRlcih7DQogICAgICAgICAgaWQ6IHJvdy5pZCwNCiAgICAgICAgICBpc0xvY2tlZDogMA0KICAgICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuino+mUgeaIkOWKnyIpOw0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMubG9hZERhc2hib2FyZFN0YXRzKCk7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOWKoOi9vee7n+iuoeamguiniOaVsOaNriAqLw0KICAgIGxvYWREYXNoYm9hcmRTdGF0cygpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7IC4uLnRoaXMucXVlcnlQYXJhbXMgfTsNCiAgICAgIC8vIOa3u+WKoOaXpeacn+iMg+WbtOetm+mAieWPguaVsA0KICAgICAgaWYgKHRoaXMuZGF0ZVJhbmdlICYmIHRoaXMuZGF0ZVJhbmdlLmxlbmd0aCA9PT0gMikgew0KICAgICAgICBwYXJhbXMuYmVnaW5EYXRlUmFuZ2UgPSB0aGlzLmRhdGVSYW5nZVswXTsNCiAgICAgICAgcGFyYW1zLmVuZERhdGVSYW5nZSA9IHRoaXMuZGF0ZVJhbmdlWzFdOw0KICAgICAgfQ0KICAgICAgZ2V0RGFzaGJvYXJkU3RhdHMocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5kYXNoYm9hcmRTdGF0cyA9IHJlc3BvbnNlLmRhdGEgfHwge307DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmm7TmlLnlt6XnqIvluIjmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVDaGFuZ2VFbmdpbmVlcihyb3cpIHsNCiAgICAgIHRoaXMuY2hhbmdlRW5naW5lZXJGb3JtID0gew0KICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICBzYW1wbGVPcmRlckNvZGU6IHJvdy5zYW1wbGVPcmRlckNvZGUsDQogICAgICAgIGN1cnJlbnRFbmdpbmVlck5hbWU6IHJvdy5uaWNrTmFtZSwNCiAgICAgICAgb2xkRW5naW5lZXJJZDogcm93LnVzZXJJZCwNCiAgICAgICAgbmV3RW5naW5lZXJJZDogbnVsbCwNCiAgICAgICAgc2NoZWR1bGVkRGF0ZTogbnVsbCwNCiAgICAgICAgYWRqdXN0V29ya1NjaGVkdWxlOiAwDQogICAgICB9Ow0KDQogICAgICAvLyDojrflj5blj6/nlKjlt6XnqIvluIjliJfooagNCiAgICAgIGdldEVuZ2luZWVyc0J5RGlmZmljdWx0eUxldmVsKHsNCiAgICAgICAgZGlmZmljdWx0eUxldmVsSWQ6IHJvdy5kaWZmaWN1bHR5TGV2ZWxJZCwNCiAgICAgICAgY2F0ZWdvcnlJZDogcm93LmNhdGVnb3J5SWQNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmVuZ2luZWVyT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGEgfHwgW107DQogICAgICB9KTsNCiAgICAgIC8vIOiOt+WPlueglOWPkeaJgOacieW3peeoi+W4iOWIl+ihqA0KICAgICAgZ2V0UmVzZWFyY2hEZXBhcnRtZW50c1VzZXIoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5zZWFyY2hlZEVuZ2luZWVycyA9IHJlc3BvbnNlLmRhdGEgfHwgW107DQogICAgICB9KTsNCiAgICAgIHRoaXMuY2hhbmdlRW5naW5lZXJPcGVuID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmm7TmlLnlt6XnqIvluIggKi8NCiAgICBzdWJtaXRDaGFuZ2VFbmdpbmVlcigpIHsNCiAgICAgIHRoaXMuJHJlZnNbImNoYW5nZUVuZ2luZWVyRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgY29uc3QgZGF0YSA9IHsNCiAgICAgICAgICAgIHNhbXBsZU9yZGVySWQ6IHRoaXMuY2hhbmdlRW5naW5lZXJGb3JtLmlkLA0KICAgICAgICAgICAgb2xkRW5naW5lZXJJZDogdGhpcy5jaGFuZ2VFbmdpbmVlckZvcm0ub2xkRW5naW5lZXJJZCwNCiAgICAgICAgICAgIG5ld0VuZ2luZWVySWQ6IHRoaXMuY2hhbmdlRW5naW5lZXJGb3JtLm5ld0VuZ2luZWVySWQsDQogICAgICAgICAgICBzY2hlZHVsZWREYXRlOiB0aGlzLmNoYW5nZUVuZ2luZWVyRm9ybS5zY2hlZHVsZWREYXRlLA0KICAgICAgICAgICAgYWRqdXN0V29ya1NjaGVkdWxlOiB0aGlzLmNoYW5nZUVuZ2luZWVyRm9ybS5hZGp1c3RXb3JrU2NoZWR1bGUNCiAgICAgICAgICB9Ow0KDQogICAgICAgICAgLy8g6LCD55So5pu05pS55bel56iL5biI55qEQVBJ5o6l5Y+jDQogICAgICAgICAgY2hhbmdlRW5naW5lZXIoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuabtOaUueW3peeoi+W4iOaIkOWKnyIpOw0KICAgICAgICAgICAgdGhpcy5jaGFuZ2VFbmdpbmVlck9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgLy8g6I635Y+W5pyq5YiG6YWN5omT5qC35Y2VDQogICAgICAgICAgICB0aGlzLmhhbmRsZVVuYXNzaWduZWRPcmRlcnMoKTsNCiAgICAgICAgICAgIHRoaXMubG9hZERhc2hib2FyZFN0YXRzKCk7DQogICAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5tc2dFcnJvcigi5pu05pS55bel56iL5biI5aSx6LSlIik7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOiOt+WPluacquWIhumFjeaJk+agt+WNleWIl+ihqCAqLw0KICAgIGhhbmRsZVVuYXNzaWduZWRPcmRlcnMoKSB7DQogICAgICBsZXQgcGFyYW1zID0geyAuLi50aGlzLnF1ZXJ5UGFyYW1zIH07DQogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuc2NoZWR1bGVkRGF0ZVJhbmdlLCdTY2hlZHVsZWREYXRlJykNCiAgICAgIHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHBhcmFtcywgdGhpcy5zdGFydERhdGVSYW5nZSwnU3RhcnREYXRlJykNCiAgICAgIHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHBhcmFtcywgdGhpcy5hY3R1YWxTdGFydFRpbWVSYW5nZSwnQWN0dWFsU3RhcnRUaW1lJykNCiAgICAgIHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHBhcmFtcywgdGhpcy5hY3R1YWxGaW5pc2hUaW1lUmFuZ2UsJ0FjdHVhbEZpbmlzaFRpbWUnKQ0KICAgICAgLy8g5riF56m65LmL5YmN55qE5pel5pyf6IyD5Zu05Y+C5pWw77yM5qC55o2u5b2T5YmN54q25oCB6YeN5paw6K6+572uDQogICAgICBkZWxldGUgcGFyYW1zLmJlZ2luRGF0ZVJhbmdlOw0KICAgICAgZGVsZXRlIHBhcmFtcy5lbmREYXRlUmFuZ2U7DQogICAgICAvLyDmt7vliqDml6XmnJ/ojIPlm7TnrZvpgInlj4LmlbANCiAgICAgIGlmICh0aGlzLmRhdGVSYW5nZSAmJiB0aGlzLmRhdGVSYW5nZS5sZW5ndGggPT09IDIpIHsNCiAgICAgICAgcGFyYW1zLmJlZ2luRGF0ZVJhbmdlID0gdGhpcy5kYXRlUmFuZ2VbMF07DQogICAgICAgIHBhcmFtcy5lbmREYXRlUmFuZ2UgPSB0aGlzLmRhdGVSYW5nZVsxXTsNCiAgICAgIH0NCiAgICAgIHBhcmFtcy5hc3NvY2lhdGlvblN0YXR1cyA9IDANCiAgICAgIHBhcmFtcy5wYWdlTnVtID0gdGhpcy51bmFzc2lnbmVkUXVlcnlQYXJhbXMucGFnZU51bTsNCiAgICAgIHBhcmFtcy5wYWdlU2l6ZSA9IHRoaXMudW5hc3NpZ25lZFF1ZXJ5UGFyYW1zLnBhZ2VTaXplOw0KICAgICAgbGlzdEVuZ2luZWVyU2FtcGxlT3JkZXIocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy51bmFzc2lnbmVkT3JkZXJzID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy51bmFzc2lnbmVkVG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIhumFjeW3peeoi+W4iOaTjeS9nCAqLw0KICAgIGhhbmRsZUFzc2lnbkVuZ2luZWVyKHJvdykgew0KICAgICAgdGhpcy5jaGFuZ2VFbmdpbmVlckZvcm0gPSB7DQogICAgICAgIGlkOiByb3cuaWQsDQogICAgICAgIHNhbXBsZU9yZGVyQ29kZTogcm93LnNhbXBsZU9yZGVyQ29kZSwNCiAgICAgICAgY3VycmVudEVuZ2luZWVyTmFtZTogJycsDQogICAgICAgIG9sZEVuZ2luZWVySWQ6IG51bGwsDQogICAgICAgIG5ld0VuZ2luZWVySWQ6IG51bGwsDQogICAgICAgIHNjaGVkdWxlZERhdGU6IG51bGwsDQogICAgICAgIGFkanVzdFdvcmtTY2hlZHVsZTogMA0KICAgICAgfTsNCg0KICAgICAgLy8g6I635Y+W5Y+v55So5bel56iL5biI5YiX6KGoDQogICAgICBnZXRFbmdpbmVlcnNCeURpZmZpY3VsdHlMZXZlbCh7DQogICAgICAgIGRpZmZpY3VsdHlMZXZlbElkOiByb3cuZGlmZmljdWx0eUxldmVsSWQsDQogICAgICAgIGNhdGVnb3J5SWQ6IHJvdy5jYXRlZ29yeUlkDQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5lbmdpbmVlck9wdGlvbnMgPSByZXNwb25zZS5kYXRhIHx8IFtdOw0KICAgICAgfSk7DQogICAgICAvLyDojrflj5bnoJTlj5HmiYDmnInlt6XnqIvluIjliJfooagNCiAgICAgIGdldFJlc2VhcmNoRGVwYXJ0bWVudHNVc2VyKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuc2VhcmNoZWRFbmdpbmVlcnMgPSByZXNwb25zZS5kYXRhIHx8IFtdOw0KICAgICAgfSk7DQogICAgICB0aGlzLmNoYW5nZUVuZ2luZWVyT3BlbiA9IHRydWU7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5pyq5YiG6YWN5omT5qC35Y2VICovDQogICAgaGFuZGxlRGVsZXRlVW5hc3NpZ25lZChyb3cpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOaJk+agt+WNlee8luWPt+S4uiInICsgcm93LnNhbXBsZU9yZGVyQ29kZSArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbEVuZ2luZWVyU2FtcGxlT3JkZXIocm93LmlkKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmhhbmRsZVVuYXNzaWduZWRPcmRlcnMoKTsNCiAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDpqbPlm57mnKrliIbphY3miZPmoLfljZUgKi8NCiAgICBoYW5kbGVSZWplY3RVbmFzc2lnbmVkKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50UmVqZWN0Um93ID0gcm93Ow0KICAgICAgdGhpcy5yZWplY3RGb3JtLnNhbXBsZU9yZGVyQ29kZSA9IHJvdy5zYW1wbGVPcmRlckNvZGU7DQogICAgICB0aGlzLnJlamVjdEZvcm0ucmVqZWN0UmVhc29uID0gJyc7DQogICAgICB0aGlzLnJlamVjdERpYWxvZ09wZW4gPSB0cnVlOw0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLiRyZWZzLnJlamVjdEZvcm0uY2xlYXJWYWxpZGF0ZSgpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5pyq5YiG6YWN5omT5qC35Y2V5YiG6aG15aSn5bCP5pS55Y+YICovDQogICAgaGFuZGxlVW5hc3NpZ25lZFNpemVDaGFuZ2UobmV3U2l6ZSkgew0KICAgICAgdGhpcy51bmFzc2lnbmVkUXVlcnlQYXJhbXMucGFnZVNpemUgPSBuZXdTaXplOw0KICAgICAgdGhpcy5oYW5kbGVVbmFzc2lnbmVkT3JkZXJzKCk7DQogICAgfSwNCiAgICAvKiog5pyq5YiG6YWN5omT5qC35Y2V6aG156CB5pS55Y+YICovDQogICAgaGFuZGxlVW5hc3NpZ25lZEN1cnJlbnRDaGFuZ2UobmV3UGFnZSkgew0KICAgICAgdGhpcy51bmFzc2lnbmVkUXVlcnlQYXJhbXMucGFnZU51bSA9IG5ld1BhZ2U7DQogICAgICB0aGlzLmhhbmRsZVVuYXNzaWduZWRPcmRlcnMoKTsNCiAgICB9LA0KICAgIC8qKiDliIfmjaLmnKrliIbphY3pnaLmnb/mmL7npLrnirbmgIEgKi8NCiAgICB0b2dnbGVVbmFzc2lnbmVkUGFuZWwoKSB7DQogICAgICB0aGlzLmlzVW5hc3NpZ25lZFBhbmVsQ29sbGFwc2VkID0gIXRoaXMuaXNVbmFzc2lnbmVkUGFuZWxDb2xsYXBzZWQ7DQogICAgfSwNCiAgICAvKiog5aSE55CG54q25oCB6L+H5rukICovDQogICAgaGFuZGxlU3RhdHVzRmlsdGVyKHN0YXR1cykgew0KICAgICAgLy8g5riF6Zmk6YC+5pyf6L+H5rukDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlzT3ZlcmR1ZSA9IG51bGw7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvbXBsZXRpb25TdGF0dXMgPSBzdGF0dXM7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCg0KICAgIC8qKiDlpITnkIbpgL7mnJ/ku7vliqHov4fmu6QgKi8NCiAgICBoYW5kbGVPdmVyZHVlRmlsdGVyKCkgew0KICAgICAgLy8g5riF6Zmk5a6M5oiQ54q25oCB6L+H5rukDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvbXBsZXRpb25TdGF0dXMgPSBudWxsOw0KICAgICAgLy8g6K6+572u6YC+5pyf6L+H5rukDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlzT3ZlcmR1ZSA9IHRoaXMucXVlcnlQYXJhbXMuaXNPdmVyZHVlID09PSAxID8gbnVsbCA6IDE7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQoNCiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLmlzT3ZlcmR1ZSA9PT0gMSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+W3suetm+mAieaYvuekuumAvuacn+S7u+WKoScpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7LmuIXpmaTpgL7mnJ/ku7vliqHnrZvpgIknKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDojrflj5bntKfmgKXnqIvluqbnsbvlnosgKi8NCiAgICBnZXRVcmdlbmN5VHlwZShlbmREYXRlLCBsYXRlc3RTdGFydFRpbWUpIHsNCiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7DQogICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZShlbmREYXRlKTsNCiAgICAgIGNvbnN0IGxhdGVzdCA9IGxhdGVzdFN0YXJ0VGltZSA/IG5ldyBEYXRlKGxhdGVzdFN0YXJ0VGltZSkgOiBudWxsOw0KDQogICAgICAvLyDorqHnrpfot53nprvmiKrmraLml6XmnJ/nmoTlpKnmlbANCiAgICAgIGNvbnN0IGRheXNUb0VuZCA9IE1hdGguY2VpbCgoZW5kIC0gbm93KSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSk7DQoNCiAgICAgIC8vIOWmguaenOacieacgOaZmuW8gOWni+aXtumXtO+8jOajgOafpeaYr+WQpuW3sue7j+i2hei/hw0KICAgICAgaWYgKGxhdGVzdCAmJiBub3cgPiBsYXRlc3QpIHsNCiAgICAgICAgcmV0dXJuICdkYW5nZXInOyAvLyDlt7LotoXov4fmnIDmmZrlvIDlp4vml7bpl7QNCiAgICAgIH0NCg0KICAgICAgaWYgKGRheXNUb0VuZCA8PSAxKSB7DQogICAgICAgIHJldHVybiAnZGFuZ2VyJzsgLy8g57Sn5oCl77yaMeWkqeWGheaIquatog0KICAgICAgfSBlbHNlIGlmIChkYXlzVG9FbmQgPD0gMykgew0KICAgICAgICByZXR1cm4gJ3dhcm5pbmcnOyAvLyDorablkYrvvJoz5aSp5YaF5oiq5q2iDQogICAgICB9IGVsc2UgaWYgKGRheXNUb0VuZCA8PSA3KSB7DQogICAgICAgIHJldHVybiAncHJpbWFyeSc7IC8vIOS4gOiIrO+8mjflpKnlhoXmiKrmraINCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiAnc3VjY2Vzcyc7IC8vIOWFhei2s+aXtumXtA0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOiOt+WPlue0p+aApeeoi+W6puaWh+acrCAqLw0KICAgIGdldFVyZ2VuY3lUZXh0KGVuZERhdGUsIGxhdGVzdFN0YXJ0VGltZSkgew0KICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsNCiAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKGVuZERhdGUpOw0KICAgICAgY29uc3QgbGF0ZXN0ID0gbGF0ZXN0U3RhcnRUaW1lID8gbmV3IERhdGUobGF0ZXN0U3RhcnRUaW1lKSA6IG51bGw7DQogICAgICAvLyDorqHnrpfot53nprvmiKrmraLml6XmnJ/nmoTlpKnmlbANCiAgICAgIGNvbnN0IGRheXNUb0VuZCA9IE1hdGguY2VpbCgoZW5kIC0gbm93KSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSk7DQogICAgICAvLyDlpoLmnpzmnInmnIDmmZrlvIDlp4vml7bpl7TvvIzmo4Dmn6XmmK/lkKblt7Lnu4/otoXov4cNCiAgICAgIGlmIChsYXRlc3QgJiYgbm93ID4gbGF0ZXN0KSB7DQogICAgICAgIHJldHVybiAn6LaF5pyf5pyq5byA5aeLJzsNCiAgICAgIH0NCg0KICAgICAgaWYgKGRheXNUb0VuZCA8PSAwKSB7DQogICAgICAgIHJldHVybiAn5bey6YC+5pyfJzsNCiAgICAgIH0gZWxzZSBpZiAoZGF5c1RvRW5kIDw9IDEpIHsNCiAgICAgICAgcmV0dXJuICfntKfmgKUnOw0KICAgICAgfSBlbHNlIGlmIChkYXlzVG9FbmQgPD0gMykgew0KICAgICAgICByZXR1cm4gJ+i+g+aApSc7DQogICAgICB9IGVsc2UgaWYgKGRheXNUb0VuZCA8PSA3KSB7DQogICAgICAgIHJldHVybiAn5LiA6IisJzsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiAn5YWF6LazJzsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDliLfmlrDmnKrliIbphY3miZPmoLfljZXliJfooaggKi8NCiAgICBoYW5kbGVSZWZyZXNoVW5hc3NpZ25lZCgpIHsNCiAgICAgIHRoaXMuaGFuZGxlVW5hc3NpZ25lZE9yZGVycygpOw0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliLfmlrDmiJDlip8nKTsNCiAgICB9LA0KICAgIC8qKiDojrflj5bpgL7mnJ/kv6Hmga8gKi8NCiAgICBnZXRPdmVyZHVlSW5mbyhyb3cpIHsNCiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7DQogICAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUocm93LmVuZERhdGUpOw0KICAgICAgbGV0IGlzT3ZlcmR1ZSA9IGZhbHNlOw0KICAgICAgbGV0IG92ZXJkdWVEYXlzID0gMDsNCiAgICAgIC8vIOagueaNruWQjuWPsFNRTOmAu+i+keWIpOaWremAvuacnw0KICAgICAgaWYgKHJvdy5jb21wbGV0aW9uU3RhdHVzID09PSAyKSB7DQogICAgICAgIC8vIOW3suWujOaIkOS4lOmAvuacn++8muWunumZheWujOaIkOaXtumXtCA+IOaIquatouaXpeacnw0KICAgICAgICBpZiAocm93LmFjdHVhbEZpbmlzaFRpbWUpIHsNCiAgICAgICAgICBjb25zdCBhY3R1YWxGaW5pc2hUaW1lID0gbmV3IERhdGUocm93LmFjdHVhbEZpbmlzaFRpbWUpOw0KICAgICAgICAgIGlmIChhY3R1YWxGaW5pc2hUaW1lID4gZW5kRGF0ZSkgew0KICAgICAgICAgICAgaXNPdmVyZHVlID0gdHJ1ZTsNCiAgICAgICAgICAgIG92ZXJkdWVEYXlzID0gTWF0aC5jZWlsKChhY3R1YWxGaW5pc2hUaW1lIC0gZW5kRGF0ZSkgLyAoMTAwMCAqIDYwICogNjAgKiAyNCkpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIGlmIChyb3cuY29tcGxldGlvblN0YXR1cyA9PT0gMSkgew0KICAgICAgICAvLyDov5vooYzkuK3kuJTpgL7mnJ/vvJrlvZPliY3ml7bpl7QgPiDmiKrmraLml6XmnJ8NCiAgICAgICAgaWYgKG5vdyA+IGVuZERhdGUpIHsNCiAgICAgICAgICBpc092ZXJkdWUgPSB0cnVlOw0KICAgICAgICAgIG92ZXJkdWVEYXlzID0gTWF0aC5jZWlsKChub3cgLSBlbmREYXRlKSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSk7DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSBpZiAocm93LmNvbXBsZXRpb25TdGF0dXMgPT09IDApIHsNCiAgICAgICAgLy8g5pyq5byA5aeL5LiU6YC+5pyf77ya5pyq5byA5aeL5L2G5b2T5YmN5pe26Ze0ID4g5byA5aeL5pel5pyfDQogICAgICAgIGlmIChub3cgPiBlbmREYXRlKSB7DQogICAgICAgICAgaXNPdmVyZHVlID0gdHJ1ZTsNCiAgICAgICAgICBvdmVyZHVlRGF5cyA9IE1hdGguY2VpbCgobm93IC0gZW5kRGF0ZSkgLyAoMTAwMCAqIDYwICogNjAgKiAyNCkpOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIHJldHVybiB7DQogICAgICAgIGlzT3ZlcmR1ZSwNCiAgICAgICAgb3ZlcmR1ZURheXMNCiAgICAgIH07DQogICAgfSwNCg0KICAgIC8vID09PT09PT09PT09PT09PT09PT09IOaJueasoeeuoeeQhuebuOWFs+aWueazlSA9PT09PT09PT09PT09PT09PT09PQ0KDQogICAgLyoqIOaJk+W8gOaJueasoeeuoeeQhuWvueivneahhiAqLw0KICAgIGhhbmRsZUJhdGNoTWFuYWdlbWVudChyb3cpIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRPcmRlckZvckJhdGNoID0gcm93Ow0KICAgICAgdGhpcy5jdXJyZW50QmF0Y2hSb3cgPSByb3c7IC8vIOS/neWtmOW9k+WJjeihjOaVsOaNrg0KICAgICAgdGhpcy5iYXRjaE1hbmFnZW1lbnRPcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMubG9hZEJhdGNoRGF0YShyb3cuaWQpOw0KICAgIH0sDQoNCiAgICAvKiog5Yqg6L295om55qyh5pWw5o2uICovDQogICAgYXN5bmMgbG9hZEJhdGNoRGF0YShlbmdpbmVlclNhbXBsZU9yZGVySWQpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOWKoOi9vei/m+ihjOS4reeahOaJueasoQ0KICAgICAgICBjb25zdCBhY3RpdmVCYXRjaGVzUmVzcG9uc2UgPSBhd2FpdCB0aGlzLmdldEFjdGl2ZUJhdGNoZXNEYXRhKGVuZ2luZWVyU2FtcGxlT3JkZXJJZCk7DQogICAgICAgIHRoaXMuYWN0aXZlQmF0Y2hlcyA9IGFjdGl2ZUJhdGNoZXNSZXNwb25zZS5kYXRhIHx8IFtdOw0KDQogICAgICAgIC8vIOS4uuavj+S4qui/m+ihjOS4reeahOaJueasoeWKoOi9veWunumqjOiusOW9lQ0KICAgICAgICBmb3IgKGxldCBiYXRjaCBvZiB0aGlzLmFjdGl2ZUJhdGNoZXMpIHsNCiAgICAgICAgICBhd2FpdCB0aGlzLmxvYWRCYXRjaEV4cGVyaW1lbnRzKGJhdGNoKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWKoOi9veaJgOacieaJueasoQ0KICAgICAgICBjb25zdCBiYXRjaGVzUmVzcG9uc2UgPSBhd2FpdCBnZXRCYXRjaGVzQnlPcmRlcklkKGVuZ2luZWVyU2FtcGxlT3JkZXJJZCk7DQogICAgICAgIHRoaXMuYWxsQmF0Y2hlcyA9IGJhdGNoZXNSZXNwb25zZS5kYXRhIHx8IFtdOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295om55qyh5pWw5o2u5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yqg6L295om55qyh5pWw5o2u5aSx6LSlJyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDliqDovb3mibnmrKHnmoTlrp7pqozorrDlvZUgKi8NCiAgICBhc3luYyBsb2FkQmF0Y2hFeHBlcmltZW50cyhiYXRjaCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRFeHBlcmltZW50c0J5QmF0Y2hJZChiYXRjaC5pZCk7DQogICAgICAgIHRoaXMuJHNldChiYXRjaCwgJ2V4cGVyaW1lbnRzJywgcmVzcG9uc2UuZGF0YSB8fCBbXSk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3mibnmrKHlrp7pqozorrDlvZXlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRzZXQoYmF0Y2gsICdleHBlcmltZW50cycsIFtdKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOiOt+WPlui/m+ihjOS4reeahOaJueasoSAqLw0KICAgIGFzeW5jIGdldEFjdGl2ZUJhdGNoZXNEYXRhKGVuZ2luZWVyU2FtcGxlT3JkZXJJZCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g6LCD55So5paw55qEQVBJ6I635Y+W6L+b6KGM5Lit55qE5om55qyhDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0QWN0aXZlQmF0Y2hlcyhlbmdpbmVlclNhbXBsZU9yZGVySWQpOw0KICAgICAgICByZXR1cm4gcmVzcG9uc2U7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bov5vooYzkuK3mibnmrKHlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICByZXR1cm4geyBkYXRhOiBbXSB9Ow0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5byA5aeL5paw5om55qyhICovDQogICAgYXN5bmMgc3RhcnROZXdCYXRjaCgpIHsNCiAgICAgIC8vIOajgOafpee8lui+keadg+mZkA0KICAgICAgaWYgKCF0aGlzLmNhbkVkaXRCYXRjaCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+W9k+WJjeaJk+agt+WNleeKtuaAgeS4jeWFgeiuuOW8gOWni+aWsOaJueasoe+8jOWPquacieeKtuaAgeS4uiLov5vooYzkuK0i55qE5omT5qC35Y2V5omN5Y+v5Lul57yW6L6R5om55qyh5L+h5oGvJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgdHJ5IHsNCiAgICAgICAgYXdhaXQgdGhpcy4kY29uZmlybSgn56Gu6K6k5byA5aeL5paw55qE5omT5qC35om55qyh77yfJywgJ+aPkOekuicsIHsNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pOw0KDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgc3RhcnROZXdCYXRjaCh0aGlzLnNlbGVjdGVkT3JkZXJGb3JCYXRjaC5pZCwgJycpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWsOaJueasoeW8gOWni+aIkOWKnycpOw0KDQogICAgICAgIC8vIOmHjeaWsOWKoOi9veaJueasoeaVsOaNrg0KICAgICAgICBhd2FpdCB0aGlzLmxvYWRCYXRjaERhdGEodGhpcy5zZWxlY3RlZE9yZGVyRm9yQmF0Y2guaWQpOw0KDQogICAgICAgIC8vIOWIt+aWsOS4u+WIl+ihqA0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGlmIChlcnJvciAhPT0gJ2NhbmNlbCcpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflvIDlp4vmlrDmibnmrKHlpLHotKU6ICcgKyAoZXJyb3IubWVzc2FnZSB8fCBlcnJvcikpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDlvIDlp4vljZXkuKrmibnmrKEgKi8NCiAgICBhc3luYyBzdGFydFNpbmdsZUJhdGNoKGJhdGNoKSB7DQogICAgICBpZiAoIXRoaXMuY2FuRWRpdEJhdGNoKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5b2T5YmN5omT5qC35Y2V54q25oCB5LiN5YWB6K645byA5aeL5om55qyhJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgdHJ5IHsNCiAgICAgICAgYXdhaXQgdGhpcy4kY29uZmlybShg56Gu6K6k5byA5aeL56ysJHtiYXRjaC5iYXRjaEluZGV4feaJueasoe+8n2AsICfmj5DnpLonLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KTsNCg0KICAgICAgICBhd2FpdCBzdGFydFNpbmdsZUJhdGNoKGJhdGNoLmlkKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmibnmrKHlvIDlp4vmiJDlip8nKTsNCg0KICAgICAgICAvLyDph43mlrDliqDovb3mibnmrKHmlbDmja4NCiAgICAgICAgYXdhaXQgdGhpcy5sb2FkQmF0Y2hEYXRhKHRoaXMuc2VsZWN0ZWRPcmRlckZvckJhdGNoLmlkKTsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBpZiAoZXJyb3IgIT09ICdjYW5jZWwnKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5byA5aeL5om55qyh5aSx6LSlOiAnICsgKGVycm9yLm1lc3NhZ2UgfHwgZXJyb3IpKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog57uT5p2f5Y2V5Liq5om55qyhICovDQogICAgZmluaXNoU2luZ2xlQmF0Y2goYmF0Y2gpIHsNCiAgICAgIGlmICghdGhpcy5jYW5FZGl0QmF0Y2gpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCflvZPliY3miZPmoLfljZXnirbmgIHkuI3lhYHorrjnu5PmnZ/mibnmrKEnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDorr7nva7kuLrljZXkuKrmibnmrKHnu5PmnZ/mqKHlvI8NCiAgICAgIHRoaXMuZmluaXNoQmF0Y2hNb2RlID0gJ3NpbmdsZSc7DQogICAgICB0aGlzLmN1cnJlbnRGaW5pc2hCYXRjaCA9IGJhdGNoOw0KDQogICAgICAvLyDph43nva7ooajljZUNCiAgICAgIHRoaXMuZmluaXNoQmF0Y2hGb3JtID0gew0KICAgICAgICBxdWFsaXR5RXZhbHVhdGlvbjogJycsDQogICAgICAgIHJlbWFyazogJycNCiAgICAgIH07DQoNCiAgICAgIC8vIOaYvuekuuWvueivneahhg0KICAgICAgdGhpcy5maW5pc2hCYXRjaE9wZW4gPSB0cnVlOw0KICAgIH0sDQoNCiAgICAvKiog57uT5p2f5omA5pyJ6L+b6KGM5Lit5om55qyhICovDQogICAgZmluaXNoQWxsQWN0aXZlQmF0Y2hlcygpIHsNCiAgICAgIGlmICghdGhpcy5jYW5FZGl0QmF0Y2gpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCflvZPliY3miZPmoLfljZXnirbmgIHkuI3lhYHorrjnu5PmnZ/mibnmrKEnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDorr7nva7kuLrnu5PmnZ/miYDmnInmibnmrKHmqKHlvI8NCiAgICAgIHRoaXMuZmluaXNoQmF0Y2hNb2RlID0gJ2FsbCc7DQogICAgICB0aGlzLmN1cnJlbnRGaW5pc2hCYXRjaCA9IG51bGw7DQoNCiAgICAgIC8vIOmHjee9ruihqOWNlQ0KICAgICAgdGhpcy5maW5pc2hCYXRjaEZvcm0gPSB7DQogICAgICAgIHF1YWxpdHlFdmFsdWF0aW9uOiAnJywNCiAgICAgICAgcmVtYXJrOiAnJw0KICAgICAgfTsNCg0KICAgICAgLy8g5pi+56S65a+56K+d5qGGDQogICAgICB0aGlzLmZpbmlzaEJhdGNoT3BlbiA9IHRydWU7DQogICAgfSwNCg0KICAgIC8qKiDmj5DkuqTnu5PmnZ/mibnmrKEgKi8NCiAgICBhc3luYyBzdWJtaXRGaW5pc2hCYXRjaCgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGlmICh0aGlzLmZpbmlzaEJhdGNoTW9kZSA9PT0gJ3NpbmdsZScpIHsNCiAgICAgICAgICAvLyDnu5PmnZ/ljZXkuKrmibnmrKENCiAgICAgICAgICBhd2FpdCBmaW5pc2hTaW5nbGVCYXRjaCgNCiAgICAgICAgICAgIHRoaXMuY3VycmVudEZpbmlzaEJhdGNoLmlkLA0KICAgICAgICAgICAgdGhpcy5maW5pc2hCYXRjaEZvcm0ucXVhbGl0eUV2YWx1YXRpb24gfHwgJycsDQogICAgICAgICAgICB0aGlzLmZpbmlzaEJhdGNoRm9ybS5yZW1hcmsgfHwgJycsDQogICAgICAgICAgICB0aGlzLmN1cnJlbnRGaW5pc2hCYXRjaC5sYWJvcmF0b3J5Q29kZSB8fCAnJw0KICAgICAgICAgICk7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmibnmrKHnu5PmnZ/miJDlip8nKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDnu5PmnZ/miYDmnInov5vooYzkuK3nmoTmibnmrKENCiAgICAgICAgICBhd2FpdCBmaW5pc2hBbGxBY3RpdmVCYXRjaGVzKHRoaXMuc2VsZWN0ZWRPcmRlckZvckJhdGNoLmlkKTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aJgOacieaJueasoee7k+adn+aIkOWKnycpOw0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy5maW5pc2hCYXRjaE9wZW4gPSBmYWxzZTsNCg0KICAgICAgICAvLyDph43mlrDliqDovb3mibnmrKHmlbDmja4NCiAgICAgICAgYXdhaXQgdGhpcy5sb2FkQmF0Y2hEYXRhKHRoaXMuc2VsZWN0ZWRPcmRlckZvckJhdGNoLmlkKTsNCg0KICAgICAgICAvLyDliLfmlrDkuLvliJfooagNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfnu5PmnZ/mibnmrKHlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnu5PmnZ/mibnmrKHlpLHotKU6ICcgKyAoZXJyb3IubWVzc2FnZSB8fCBlcnJvcikpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5p+l55yL5om55qyh6K+m5oOFICovDQogICAgYXN5bmMgdmlld0JhdGNoRGV0YWlsKGJhdGNoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldEV4cGVyaW1lbnRzQnlCYXRjaElkKGJhdGNoLmlkKTsNCiAgICAgICAgY29uc3QgZXhwZXJpbWVudHMgPSByZXNwb25zZS5kYXRhIHx8IFtdOw0KDQogICAgICAgIC8vIOiuvue9ruaJueasoeivpuaDheaVsOaNrg0KICAgICAgICB0aGlzLmJhdGNoRGV0YWlsRGF0YSA9IHsNCiAgICAgICAgICAuLi5iYXRjaCwNCiAgICAgICAgICBleHBlcmltZW50czogZXhwZXJpbWVudHMNCiAgICAgICAgfTsNCg0KICAgICAgICB0aGlzLmJhdGNoRGV0YWlsT3BlbiA9IHRydWU7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfmn6XnnIvmibnmrKHor6bmg4XlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmn6XnnIvmibnmrKHor6bmg4XlpLHotKUnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOiuoeeul+aMgee7reaXtumXtCAqLw0KICAgIGNhbGN1bGF0ZUR1cmF0aW9uKHN0YXJ0VGltZSkgew0KICAgICAgaWYgKCFzdGFydFRpbWUpIHJldHVybiAnMOWwj+aXtic7DQoNCiAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoc3RhcnRUaW1lKTsNCiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7DQogICAgICBjb25zdCBkaWZmTXMgPSBub3cgLSBzdGFydDsNCiAgICAgIGNvbnN0IGRpZmZIb3VycyA9IE1hdGguZmxvb3IoZGlmZk1zIC8gKDEwMDAgKiA2MCAqIDYwKSk7DQogICAgICBjb25zdCBkaWZmTWludXRlcyA9IE1hdGguZmxvb3IoKGRpZmZNcyAlICgxMDAwICogNjAgKiA2MCkpIC8gKDEwMDAgKiA2MCkpOw0KDQogICAgICBpZiAoZGlmZkhvdXJzID4gMCkgew0KICAgICAgICByZXR1cm4gYCR7ZGlmZkhvdXJzfeWwj+aXtiR7ZGlmZk1pbnV0ZXN95YiG6ZKfYDsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiBgJHtkaWZmTWludXRlc33liIbpkp9gOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog57yW6L6R5a6e6aqM5a6k57yW5Y+3ICovDQogICAgZWRpdExhYm9yYXRvcnlDb2RlKGJhdGNoKSB7DQogICAgICB0aGlzLiRzZXQoYmF0Y2gsICdlZGl0aW5nTGFiJywgdHJ1ZSk7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIC8vIOiBmueEpuWIsOi+k+WFpeahhg0KICAgICAgICBjb25zdCBpbnB1dCA9IHRoaXMuJGVsLnF1ZXJ5U2VsZWN0b3IoYGlucHV0W3ZhbHVlPSIke2JhdGNoLmxhYm9yYXRvcnlDb2RlIHx8ICcnfSJdYCk7DQogICAgICAgIGlmIChpbnB1dCkgaW5wdXQuZm9jdXMoKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5pi+56S65re75Yqg5a6e6aqM6K6w5b2V5a+56K+d5qGGICovDQogICAgc2hvd0FkZEV4cGVyaW1lbnREaWFsb2coYmF0Y2gpIHsNCiAgICAgIHRoaXMuY3VycmVudEJhdGNoRm9yRXhwZXJpbWVudCA9IGJhdGNoOw0KICAgICAgdGhpcy5hZGRFeHBlcmltZW50Rm9ybSA9IHsNCiAgICAgICAgZXhwZXJpbWVudENvZGU6ICcnLA0KICAgICAgICBleHBlcmltZW50Tm90ZTogJycsDQogICAgICAgIHJlbWFyazogJycNCiAgICAgIH07DQogICAgICB0aGlzLmFkZEV4cGVyaW1lbnRPcGVuID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgLyoqIOWFs+mXrea3u+WKoOWunumqjOiusOW9leWvueivneahhiAqLw0KICAgIGNsb3NlQWRkRXhwZXJpbWVudCgpIHsNCiAgICAgIHRoaXMuYWRkRXhwZXJpbWVudE9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMuY3VycmVudEJhdGNoRm9yRXhwZXJpbWVudCA9IG51bGw7DQogICAgICB0aGlzLiRyZWZzLmFkZEV4cGVyaW1lbnRGb3JtICYmIHRoaXMuJHJlZnMuYWRkRXhwZXJpbWVudEZvcm0ucmVzZXRGaWVsZHMoKTsNCiAgICB9LA0KDQogICAgLyoqIOaPkOS6pOa3u+WKoOWunumqjOiusOW9lSAqLw0KICAgIGFzeW5jIHN1Ym1pdEFkZEV4cGVyaW1lbnQoKSB7DQogICAgICB0cnkgew0KICAgICAgICBhd2FpdCB0aGlzLiRyZWZzLmFkZEV4cGVyaW1lbnRGb3JtLnZhbGlkYXRlKCk7DQoNCiAgICAgICAgY29uc3QgZXhwZXJpbWVudFJlY29yZCA9IHsNCiAgICAgICAgICBiYXRjaElkOiB0aGlzLmN1cnJlbnRCYXRjaEZvckV4cGVyaW1lbnQuaWQsDQogICAgICAgICAgZXhwZXJpbWVudENvZGU6IHRoaXMuYWRkRXhwZXJpbWVudEZvcm0uZXhwZXJpbWVudENvZGUsDQogICAgICAgICAgZXhwZXJpbWVudE5vdGU6IHRoaXMuYWRkRXhwZXJpbWVudEZvcm0uZXhwZXJpbWVudE5vdGUgfHwgJycsDQogICAgICAgICAgcmVtYXJrOiB0aGlzLmFkZEV4cGVyaW1lbnRGb3JtLnJlbWFyaw0KICAgICAgICB9Ow0KDQogICAgICAgIGF3YWl0IGFkZEV4cGVyaW1lbnRUb0JhdGNoKGV4cGVyaW1lbnRSZWNvcmQpOw0KDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5a6e6aqM6K6w5b2V5re75Yqg5oiQ5YqfJyk7DQogICAgICAgIHRoaXMuYWRkRXhwZXJpbWVudE9wZW4gPSBmYWxzZTsNCg0KICAgICAgICAvLyDph43mlrDliqDovb3lvZPliY3mibnmrKHnmoTlrp7pqozorrDlvZUNCiAgICAgICAgYXdhaXQgdGhpcy5sb2FkQmF0Y2hFeHBlcmltZW50cyh0aGlzLmN1cnJlbnRCYXRjaEZvckV4cGVyaW1lbnQpOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5re75Yqg5a6e6aqM6K6w5b2V5aSx6LSlOiAnICsgKGVycm9yLm1lc3NhZ2UgfHwgZXJyb3IpKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOWIoOmZpOWunumqjOiusOW9lSAqLw0KICAgIGFzeW5jIHJlbW92ZUV4cGVyaW1lbnQoYmF0Y2gsIGV4cGVyaW1lbnQpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGF3YWl0IHRoaXMuJGNvbmZpcm0oJ+ehruiupOWIoOmZpOivpeWunumqjOiusOW9leWQl++8nycsICfmj5DnpLonLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KTsNCg0KICAgICAgICBhd2FpdCByZW1vdmVFeHBlcmltZW50RnJvbUJhdGNoKGV4cGVyaW1lbnQuaWQpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WunumqjOiusOW9leWIoOmZpOaIkOWKnycpOw0KDQogICAgICAgIC8vIOmHjeaWsOWKoOi9veW9k+WJjeaJueasoeeahOWunumqjOiusOW9lQ0KICAgICAgICBhd2FpdCB0aGlzLmxvYWRCYXRjaEV4cGVyaW1lbnRzKGJhdGNoKTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGlmIChlcnJvciAhPT0gJ2NhbmNlbCcpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliKDpmaTlrp7pqozorrDlvZXlpLHotKU6ICcgKyAoZXJyb3IubWVzc2FnZSB8fCBlcnJvcikpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDojrflj5bmibnmrKHnirbmgIHmlofmnKwgKi8NCiAgICBnZXRCYXRjaFN0YXR1c1RleHQoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgIDA6ICfmnKrlvIDlp4snLA0KICAgICAgICAxOiAn6L+b6KGM5LitJywNCiAgICAgICAgMjogJ+W3suWujOaIkCcsDQogICAgICAgIDM6ICflt7Llj5bmtognDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICfmnKrnn6UnOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5om55qyh54q25oCB57G75Z6LICovDQogICAgZ2V0QmF0Y2hTdGF0dXNUeXBlKHN0YXR1cykgew0KICAgICAgY29uc3QgdHlwZU1hcCA9IHsNCiAgICAgICAgMDogJ2luZm8nLA0KICAgICAgICAxOiAnc3VjY2VzcycsDQogICAgICAgIDI6ICdwcmltYXJ5JywNCiAgICAgICAgMzogJ2RhbmdlcicNCiAgICAgIH07DQogICAgICByZXR1cm4gdHlwZU1hcFtzdGF0dXNdIHx8ICdpbmZvJzsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPlui0qOmHj+ivhOS7t+exu+WeiyAqLw0KICAgIGdldFF1YWxpdHlFdmFsdWF0aW9uVHlwZShldmFsdWF0aW9uKSB7DQogICAgICBpZiAoIWV2YWx1YXRpb24pIHJldHVybiAnJzsNCg0KICAgICAgY29uc3QgbG93ZXJFdmFsID0gZXZhbHVhdGlvbi50b0xvd2VyQ2FzZSgpOw0KICAgICAgaWYgKGxvd2VyRXZhbC5pbmNsdWRlcygn5LyY56eAJykgfHwgbG93ZXJFdmFsLmluY2x1ZGVzKCfoia/lpb0nKSB8fCBsb3dlckV2YWwuaW5jbHVkZXMoJ+WlvScpKSB7DQogICAgICAgIHJldHVybiAnc3VjY2Vzcyc7DQogICAgICB9IGVsc2UgaWYgKGxvd2VyRXZhbC5pbmNsdWRlcygn5LiA6IisJykgfHwgbG93ZXJFdmFsLmluY2x1ZGVzKCfkuK3nrYknKSkgew0KICAgICAgICByZXR1cm4gJ3dhcm5pbmcnOw0KICAgICAgfSBlbHNlIGlmIChsb3dlckV2YWwuaW5jbHVkZXMoJ+W3ricpIHx8IGxvd2VyRXZhbC5pbmNsdWRlcygn5LiN5ZCI5qC8JykgfHwgbG93ZXJFdmFsLmluY2x1ZGVzKCflpLHotKUnKSkgew0KICAgICAgICByZXR1cm4gJ2Rhbmdlcic7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJ2luZm8nOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5YWz6Zet5om55qyh566h55CG5a+56K+d5qGG5bm25riF56m65pWw5o2uICovDQogICAgY2xvc2VCYXRjaE1hbmFnZW1lbnQoKSB7DQogICAgICAvLyDmuIXnqbrmibnmrKHnrqHnkIbnm7jlhbPnmoTmlbDmja4NCiAgICAgIHRoaXMuYWN0aXZlQmF0Y2hlcyA9IFtdOw0KICAgICAgdGhpcy5hbGxCYXRjaGVzID0gW107DQogICAgICB0aGlzLnNlbGVjdGVkT3JkZXJGb3JCYXRjaCA9IG51bGw7DQogICAgICB0aGlzLmN1cnJlbnRCYXRjaFJvdyA9IG51bGw7IC8vIOa4heepuuW9k+WJjeihjOaVsOaNrg0KICAgICAgdGhpcy5iYXRjaERldGFpbERhdGEgPSBudWxsOw0KDQogICAgICB0aGlzLmZpbmlzaEJhdGNoRm9ybSA9IHsNCiAgICAgICAgcXVhbGl0eUV2YWx1YXRpb246ICcnLA0KICAgICAgICByZW1hcms6ICcnDQogICAgICB9Ow0KDQogICAgICAvLyDlhbPpl63miYDmnInnm7jlhbPnmoTlrZDlr7nor53moYYNCiAgICAgIHRoaXMuYmF0Y2hEZXRhaWxPcGVuID0gZmFsc2U7DQogICAgICB0aGlzLmFkZEV4cGVyaW1lbnRPcGVuID0gZmFsc2U7DQogICAgICB0aGlzLmZpbmlzaEJhdGNoT3BlbiA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICAvKiog5YWz6Zet5om55qyh6K+m5oOF5a+56K+d5qGG5bm25riF56m65pWw5o2uICovDQogICAgY2xvc2VCYXRjaERldGFpbCgpIHsNCiAgICAgIC8vIOa4heepuuaJueasoeivpuaDheaVsOaNrg0KICAgICAgdGhpcy5iYXRjaERldGFpbERhdGEgPSBudWxsOw0KICAgIH0sDQoNCiAgICAvKiog5YWz6Zet57uT5p2f5om55qyh5a+56K+d5qGG5bm25riF56m65pWw5o2uICovDQogICAgY2xvc2VGaW5pc2hCYXRjaCgpIHsNCiAgICAgIC8vIOa4heepuue7k+adn+aJueasoeihqOWNleaVsOaNrg0KICAgICAgdGhpcy5maW5pc2hCYXRjaE9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMuZmluaXNoQmF0Y2hNb2RlID0gJ2FsbCc7DQogICAgICB0aGlzLmN1cnJlbnRGaW5pc2hCYXRjaCA9IG51bGw7DQogICAgICB0aGlzLmZpbmlzaEJhdGNoRm9ybSA9IHsNCiAgICAgICAgcXVhbGl0eUV2YWx1YXRpb246ICcnLA0KICAgICAgICByZW1hcms6ICcnDQogICAgICB9Ow0KICAgICAgLy8g5riF6Zmk6KGo5Y2V6aqM6K+B54q25oCBDQogICAgICBpZiAodGhpcy4kcmVmcy5maW5pc2hCYXRjaEZvcm0pIHsNCiAgICAgICAgdGhpcy4kcmVmcy5maW5pc2hCYXRjaEZvcm0uY2xlYXJWYWxpZGF0ZSgpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5Yi35paw5om55qyh5pWw5o2uICovDQogICAgYXN5bmMgcmVmcmVzaEJhdGNoRGF0YSgpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkT3JkZXJGb3JCYXRjaCkgew0KICAgICAgICBhd2FpdCB0aGlzLmxvYWRCYXRjaERhdGEodGhpcy5zZWxlY3RlZE9yZGVyRm9yQmF0Y2guaWQpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aJueasoeaVsOaNruW3suWIt+aWsCcpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5Yqg6L295a6e6aqM5a6k57yW56CB5YiX6KGoICovDQogICAgYXN5bmMgbG9hZEV4cGVyaW1lbnRDb2RlTGlzdChlbmdpbmVlclNhbXBsZU9yZGVySWQpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0QmF0Y2hFeHBlcmltZW50Q29kZUxpc3QoZW5naW5lZXJTYW1wbGVPcmRlcklkKTsNCiAgICAgICAgdGhpcy5leHBlcmltZW50Q29kZUxpc3QgPSByZXNwb25zZS5kYXRhIHx8IFtdOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295a6e6aqM5a6k57yW56CB5YiX6KGo5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy5leHBlcmltZW50Q29kZUxpc3QgPSBbXTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yqg6L295a6e6aqM5a6k57yW56CB5YiX6KGo5aSx6LSlJyk7DQogICAgICB9DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAg9CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/software/engineerSampleOrder", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 统计概览 -->\r\n    <el-row :gutter=\"20\" class=\"stats-row\" style=\"margin-bottom: 20px;\">\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card total\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-s-order\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">总任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.total || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card completed\" @click.native=\"handleStatusFilter('2')\" :class=\"{'active': queryParams.completionStatus === '2'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-check\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">已完成</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.completed || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card in-progress\" @click.native=\"handleStatusFilter('1')\" :class=\"{'active': queryParams.completionStatus === '1'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-loading\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">进行中</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.inProgress || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card overdue\" @click.native=\"handleOverdueFilter\" :class=\"{'active': queryParams.isOverdue === 1}\" style=\"cursor: pointer;\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-warning\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">逾期任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.overdue || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 未分配工程师的打样单告警 -->\r\n    <el-card class=\"alert-card\">\r\n      <div slot=\"header\" class=\"alert-header\">\r\n        <div class=\"alert-title\" @click=\"toggleUnassignedPanel\">\r\n          <span><i class=\"el-icon-warning-outline\"></i>待分配打样单({{ unassignedTotal }}个)</span>\r\n        </div>\r\n        <div class=\"alert-actions\">\r\n          <el-button\r\n            type=\"text\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"handleRefreshUnassigned\"\r\n            style=\"margin-right: 10px\"\r\n            title=\"刷新列表\">\r\n            刷新\r\n          </el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            @click=\"toggleUnassignedPanel\"\r\n            style=\"margin-right: 10px\">\r\n            {{ isUnassignedPanelCollapsed ? '展开' : '收起' }}\r\n            <i :class=\"isUnassignedPanelCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\r\n          </el-button>\r\n          <el-pagination\r\n            @size-change=\"handleUnassignedSizeChange\"\r\n            @current-change=\"handleUnassignedCurrentChange\"\r\n            :current-page=\"unassignedQueryParams.pageNum\"\r\n            :page-sizes=\"[8, 16, 24, 32]\"\r\n            :page-size=\"unassignedQueryParams.pageSize\"\r\n            layout=\"total, sizes, prev, pager, next\"\r\n            :total=\"unassignedTotal\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n      <el-collapse-transition>\r\n        <div v-show=\"!isUnassignedPanelCollapsed\">\r\n          <el-row :gutter=\"20\" class=\"alert-cards\">\r\n            <el-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\" v-for=\"item in unassignedOrders\" :key=\"item.id\">\r\n              <el-card shadow=\"hover\" class=\"alert-item\">\r\n                <div class=\"alert-item-header\">\r\n                  <div class=\"order-code-section\">\r\n                    <span style=\"color: #00afff;cursor: pointer\"  @click=\"orderDetail(item)\" class=\"order-code\">{{ item.sampleOrderCode }}</span>\r\n                    <el-tag\r\n                      :type=\"getUrgencyType(item.endDate, item.latestStartTime)\"\r\n                      size=\"mini\"\r\n                      class=\"urgency-tag\">\r\n                      {{ getUrgencyText(item.endDate, item.latestStartTime) }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n                <div class=\"alert-item-content\">\r\n                  <div class=\"info-section\">\r\n                    <!-- 预估工时和难度等级 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\" v-if=\"item.latestStartTime\">\r\n                        <i class=\"el-icon-alarm-clock\" style=\"color: #909399;\"></i>\r\n                        <span class=\"info-label\">最晚开始:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-time\" style=\"color: #409EFF;\"></i>\r\n                        <span class=\"info-label\">预估工时:</span>\r\n                        <span class=\"info-value\">{{ item.estimatedManHours || '-' }}H</span>\r\n                      </div>\r\n                    </div>\r\n                    <!-- 最晚开始日期和截止日期排 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\">\r\n                        <i class=\"el-icon-date\" style=\"color: #F56C6C;\"></i>\r\n                        <span class=\"info-label\">截止日期:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.endDate, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-star-on\" style=\"color: #E6A23C;\"></i>\r\n                        <span class=\"info-label\">难度等级:</span>\r\n                        <el-tooltip :content=\"selectDictLabel(dylbOptions, item.difficultyLevelId)\" placement=\"top\">\r\n                          <span class=\"info-value difficulty-text\">{{selectDictLabel(dylbOptions,item.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n                        </el-tooltip>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <el-tooltip v-if=\"item.failureReason\" :content=\"item.failureReason\" placement=\"top\">\r\n                    <div class=\"info-reason\">\r\n                      <i class=\"el-icon-warning-outline\"></i>\r\n                      <span class=\"reason-text\">{{ item.failureReason }}</span>\r\n                    </div>\r\n                  </el-tooltip>\r\n                </div>\r\n                <div class=\"alert-item-footer\">\r\n                  <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-user-solid\" @click=\"handleAssignEngineer(item)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\">\r\n                    分配工程师\r\n                  </el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-close\" @click=\"handleRejectUnassigned(item)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" class=\"reject-btn\" style=\"color: #F56C6C;\">\r\n                    驳回\r\n                  </el-button>\r\n                </div>\r\n              </el-card>\r\n           </el-col>\r\n          </el-row>\r\n         </div>\r\n      </el-collapse-transition>\r\n    </el-card>\r\n\r\n    <!-- 筛选条件 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"客户\" prop=\"customerId\">\r\n        <el-select v-model=\"queryParams.customerId\" filterable placeholder=\"客户\" clearable size=\"small\">\r\n          <el-option\r\n            v-for=\"dict in customerOptions\"\r\n            :key=\"dict.id\"\r\n            :label=\"dict.name\"\r\n            :value=\"dict.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"日期范围\" prop=\"dateRange\" label-width=\"80px\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"\r\n          style=\"width: 240px\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n        <el-date-picker\r\n          v-model=\"scheduledDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"申请日期\" prop=\"startDate\">\r\n        <el-date-picker\r\n          v-model=\"startDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"研发组别\" prop=\"deptIds\">\r\n        <el-select v-model=\"queryParams.deptIds\" multiple filterable placeholder=\"请选择对应研发组别\">\r\n          <el-option\r\n            v-for=\"dept in researchDeptDatas\"\r\n            :key=\"dept.deptId\"\r\n            :label=\"dept.deptName\"\r\n            :value=\"dept.deptId\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"服务模式\" prop=\"serviceMode\">\r\n        <el-select v-model=\"queryParams.serviceMode\" placeholder=\"请选择客户服务模式\" clearable>\r\n          <el-option\r\n            v-for=\"dict in serviceModeOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"实验室\" prop=\"laboratory\">\r\n        <el-select v-model=\"queryParams.laboratory\" placeholder=\"请选择实验室\" clearable>\r\n          <el-option label=\"宜侬\" value=\"0\" />\r\n          <el-option label=\"瀛彩\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"确认编码\" prop=\"confirmCode\">\r\n        <el-input\r\n          v-model.trim=\"queryParams.confirmCode\"\r\n          placeholder=\"请输入确认编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"产品名称\" prop=\"productName\">\r\n        <el-input\r\n          v-model.trim=\"queryParams.productName\"\r\n          placeholder=\"请输入产品名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"姓名\" prop=\"nickName\" label-width=\"50px\">\r\n        <el-input v-model.trim=\"queryParams.nickName\" placeholder=\"请输入工程师姓名\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"打样单编号\" label-width=\"90px\" prop=\"sampleOrderCode\">\r\n        <el-input v-model.trim=\"queryParams.sampleOrderCode\" placeholder=\"请输入打样单编号\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n        <el-select v-model=\"queryParams.completionStatus\" placeholder=\"请选择完成情况\" clearable>\r\n          <el-option\r\n            v-for=\"dict in statusOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualStartTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualFinishTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:add']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:edit']\">修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:remove']\">删除</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"doExportSampleOrder\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleBatchExportNrw\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出打样单任务</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAddSampleOrder\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:add']\">新增打样单</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 主要内容区域 -->\r\n     <!-- 工单列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"engineerSampleOrderList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"主键ID\" align=\"center\" prop=\"id\" /> -->\r\n<!--      <el-table-column label=\"工程师ID\" align=\"center\" prop=\"userId\" />-->\r\n      <el-table-column align=\"center\" prop=\"sampleOrderCode\" width=\"160\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          打样单编号\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              点击编号可查看详细信息\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"orderDetail(scope.row)\">{{ scope.row.sampleOrderCode }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"startDate\" width=\"140\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          申请日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              客户提交打样申请的日期时间\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"scheduledDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          排单日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              工程师被安排处理此打样单的日期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.scheduledDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"latestStartTime\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚开始日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              必须在此日期前开始工作，否则可能影响交期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"endDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚截至日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作必须在此日期前完成\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\" prop=\"completionStatus\">\r\n        <template slot=\"header\">\r\n          完成情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              当前打样单的状态\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"scope.row.completionStatus == '0' ? 'info' :\r\n                   scope.row.completionStatus == '1' ? 'primary' :\r\n                   scope.row.completionStatus == '2' ? 'success' : 'info'\"\r\n            size=\"mini\"\r\n          >\r\n            {{selectDictLabel(statusOptions,scope.row.completionStatus)}}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          批次信息\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              显示当前批次总数\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.totalBatches > 0\">\r\n            <el-tag size=\"mini\" type=\"primary\">\r\n              {{ scope.row.totalBatches || 0 }}\r\n            </el-tag>\r\n          </div>\r\n          <div v-else>\r\n            -\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          逾期情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              根据截止日期判断是否逾期及逾期天数\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"getOverdueInfo(scope.row).isOverdue\">\r\n            <el-tag type=\"danger\" size=\"mini\" style=\"margin-bottom: 2px;\">\r\n              逾期 {{ getOverdueInfo(scope.row).overdueDays }}天\r\n            </el-tag>\r\n          </div>\r\n          <el-tag v-else type=\"success\" size=\"mini\">\r\n            正常\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"nickName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          跟进人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的工程师姓名及职级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.nickName }}{{ scope.row.rank ? '-' + scope.row.rank : '' }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"assistantName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          协助人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              此打样单难度高于工程师能力范围\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.assistantName }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"isLocked\" width=\"90\">\r\n        <template slot=\"header\">\r\n          锁定状态\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              锁定后不允许更换工程师，防止误操作\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.isLocked\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            @change=\"(val) => handleTableLockChange(val, scope.row)\">\r\n          </el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratoryCode\" width=\"150\">\r\n        <template slot=\"header\">\r\n          实验编码\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              实验室分配的唯一编码标识\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"difficultyLevelId\" width=\"80\">\r\n        <template slot=\"header\">\r\n          难度\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作的技术难度等级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"selectDictLabel(dylbOptions,scope.row.difficultyLevelId)\" placement=\"top\">\r\n            <span>{{selectDictLabel(dylbOptions,scope.row.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"标准工时\" align=\"center\" prop=\"standardManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.standardManHours\">{{ scope.row.standardManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratory\" width=\"90\">\r\n        <template slot=\"header\">\r\n          实验室\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的实验室分支\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{\r\n            scope.row.laboratory === '0' ? '宜侬' :\r\n            scope.row.laboratory === '1' ? '瀛彩' :\r\n            ''\r\n          }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"categoryName\" width=\"70\">\r\n        <template slot=\"header\">\r\n          品类\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              产品所属的分类类别\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"SKU数\" align=\"center\" prop=\"sku\" width=\"70\" />\r\n      <el-table-column align=\"center\" prop=\"applicant\" width=\"90\">\r\n        <template slot=\"header\">\r\n          申请人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              提交此打样申请的人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"sales\" width=\"70\">\r\n        <template slot=\"header\">\r\n          销售\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此项目的销售人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"customerName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          客户名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              委托打样的客户公司名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"产品/项目等级\" align=\"center\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getProjectLevel(scope.row) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"productName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          产品名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              需要打样的产品名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工作时间\" align=\"center\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div v-if=\"scope.row.actualStartTime\">开始: {{ parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <div v-if=\"scope.row.actualFinishTime\">完成: {{ parseTime(scope.row.actualFinishTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <span v-if=\"!scope.row.actualStartTime && !scope.row.actualFinishTime\">-</span>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工时\" align=\"center\" prop=\"actualManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.actualManHours\">{{ scope.row.actualManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预估工时\" align=\"center\" prop=\"estimatedManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.estimatedManHours\">{{ scope.row.estimatedManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"服务模式\" align=\"center\" prop=\"serviceMode\" width=\"150\" />\r\n      <el-table-column label=\"计算类型\" align=\"center\" prop=\"checkType\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.checkType == 1\">客户等级</span>\r\n          <span v-else>打样单难度</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"完成质量\" align=\"center\" prop=\"qualityEvaluation\" />\r\n<!--      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"140\">-->\r\n<!--        <template slot-scope=\"scope\">-->\r\n<!--          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n      <el-table-column label=\"打样单备注\" align=\"center\" prop=\"sampleOrderRemark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.sampleOrderRemark\" placement=\"top\" :disabled=\"!scope.row.sampleOrderRemark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.sampleOrderRemark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.remark\" placement=\"top\" :disabled=\"!scope.row.remark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.remark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"未出样原因\" align=\"center\" prop=\"reasonForNoSample\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.reasonForNoSample\" placement=\"top\" :disabled=\"!scope.row.reasonForNoSample\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.reasonForNoSample || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"解决方案\" align=\"center\" prop=\"solution\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.solution\" placement=\"top\" :disabled=\"!scope.row.solution\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.solution || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"驳回原因\" align=\"center\" prop=\"rejectReason\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.rejectReason\" placement=\"top\" :disabled=\"!scope.row.rejectReason\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.rejectReason || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"fixed-width\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- <el-tooltip content=\"编辑\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:edit']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"删除\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:remove']\" />\r\n          </el-tooltip> -->\r\n          <el-tooltip  v-if=\"scope.row.completionStatus==0\" content=\"更换工程师或更改日期\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-user\" v-if=\"scope.row.isLocked === 0\" @click=\"handleChangeEngineer(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\" />\r\n          </el-tooltip>\r\n\r\n\r\n          <el-tooltip content=\"批次管理\" v-if=\"[0,1,2].includes(scope.row.completionStatus)\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-collection\" @click=\"handleBatchManagement(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:batchManagement']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"逾期操作\" v-if=\"getOverdueInfo(scope.row).isOverdue\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-warning\" @click=\"handleOverdueOperation(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:overdueOperation']\" style=\"color: #E6A23C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"驳回\" v-if=\"scope.row.completionStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-close\" @click=\"handleReject(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" style=\"color: #F56C6C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"更新实验室编码\" v-if=\"scope.row.completionStatus == '2' && scope.row.itemStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit-outline\" @click=\"handleUpdateLaboratoryCode(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:editSampleOrderCode']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"下载打样单\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-download\" @click=\"handleDownloadSampleOrder(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:export']\" />\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      :auto-scroll=\"false\" @pagination=\"getList\" />\r\n\r\n    <!-- 添加或修改工程师打样单关联对话框 -->\r\n    <!-- <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"650px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程师ID\" prop=\"userId\">\r\n              <el-input v-model=\"form.userId\" placeholder=\"请输入工程师ID\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"打样单编号\" prop=\"sampleOrderCode\">\r\n              <el-input v-model=\"form.sampleOrderCode\" placeholder=\"请输入打样单编号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"难度\" prop=\"difficultyLevelId\">\r\n              <el-select v-model=\"form.difficultyLevelId\" placeholder=\"请选择打样难度等级\">\r\n                  <el-option\r\n                    v-for=\"dict in dylbOptions\"\r\n                    :key=\"dict.dictValue\"\r\n                    :label=\"dict.dictLabel\"\r\n                    :value=\"dict.dictValue\"\r\n                  ></el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n              <el-select v-model=\"form.completionStatus\" placeholder=\"请选择完成情况\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in statusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n              <el-date-picker\r\n                v-model=\"form.scheduledDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择排单日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"最晚截至日期\" prop=\"endDate\">\r\n              <el-date-picker\r\n                v-model=\"form.endDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择最晚截至日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"锁定状态\" prop=\"isLocked\">\r\n              <el-switch\r\n                v-model=\"form.isLocked\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n                active-text=\"已锁定\"\r\n                inactive-text=\"未锁定\"\r\n                @change=\"handleLockChange\">\r\n              </el-switch>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualStartTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择开始时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualFinishTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择完成时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际工时\" prop=\"actualManHours\">\r\n              <el-input-number v-model=\"form.actualManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入实际工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"预估工时\" prop=\"estimatedManHours\">\r\n              <el-input-number v-model=\"form.estimatedManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入预估工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"完成质量\" prop=\"qualityEvaluation\">\r\n              <el-input v-model=\"form.qualityEvaluation\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入完成质量情况\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"打样单备注\" prop=\"sampleOrderRemark\">\r\n              <el-input v-model=\"form.sampleOrderRemark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入打样单备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n\r\n    <!-- 新增打样单对话框 -->\r\n    <el-dialog title=\"新增打样单\" :visible.sync=\"addSampleOrderOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"addSampleOrderForm\" :model=\"addSampleOrderForm\" :rules=\"addSampleOrderRules\" label-width=\"100px\">\r\n        <el-form-item label=\"实验室\" prop=\"labNo\">\r\n          <el-select v-model=\"addSampleOrderForm.labNo\" placeholder=\"请选择实验室\" style=\"width: 100%\">\r\n            <el-option label=\"宜侬\" value=\"0\" />\r\n            <el-option label=\"瀛彩\" value=\"1\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"类别\" prop=\"categoryText\">\r\n          <el-select v-model=\"addSampleOrderForm.categoryText\" filterable placeholder=\"请选择类别\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in sckxxpgCplbs\"\r\n              :key=\"dict.dictValue\"\r\n              :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"难度\" prop=\"dylb\">\r\n          <el-select v-model=\"addSampleOrderForm.dylb\" placeholder=\"请选择难度\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in dylbOptions\"\r\n              :key=\"dict.dictValue\"\r\n              :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"addSampleOrderForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAddSampleOrder\" :loading=\"addSampleOrderLoading\">确 定</el-button>\r\n        <el-button @click=\"cancelAddSampleOrder\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 更改工程师对话框 -->\r\n    <el-dialog title=\"更改工程师\" :visible.sync=\"changeEngineerOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"changeEngineerForm\" :model=\"changeEngineerForm\" :rules=\"changeEngineerRules\" label-width=\"150px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"changeEngineerForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"当前工程师\">\r\n          <el-input v-model=\"changeEngineerForm.currentEngineerName\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"选择工程师\" prop=\"newEngineerId\">\r\n          <el-radio-group v-model=\"engineerSelectType\" style=\"margin-bottom: 15px;\">\r\n            <el-radio label=\"specified\">指定部门工程师</el-radio>\r\n            <el-radio label=\"other\">其他部门工程师</el-radio>\r\n          </el-radio-group>\r\n\r\n          <!-- 指定工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'specified'\">\r\n            <el-select\r\n              v-model=\"changeEngineerForm.newEngineerId\"\r\n              placeholder=\"请选择工程师\"\r\n              style=\"width: 100%\"\r\n              filterable>\r\n              <el-option\r\n                v-for=\"engineer in engineerOptions\"\r\n                :key=\"engineer.userId\"\r\n                :label=\"engineer.nickName\"\r\n                :value=\"engineer.userId\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n\r\n          <!-- 其他工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'other'\" class=\"other-engineer-select\">\r\n            <div class=\"select-item\" style=\"margin-top: 10px;\">\r\n              <el-select\r\n                v-model=\"changeEngineerForm.newEngineerId\"\r\n                placeholder=\"请选择工程师\"\r\n                style=\"width: 100%\"\r\n                filterable\r\n                remote>\r\n                <el-option\r\n                  v-for=\"engineer in searchedEngineers\"\r\n                  :key=\"engineer.userId\"\r\n                  :label=\"engineer.nickName\"\r\n                  :value=\"engineer.userId\"\r\n                />\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"选择指定日期\" prop=\"scheduledDate\">\r\n          <el-date-picker\r\n            v-model=\"changeEngineerForm.scheduledDate\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"重新调整工作安排\">\r\n          <el-switch\r\n            v-model=\"changeEngineerForm.adjustWorkSchedule\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            active-text=\"是\"\r\n            inactive-text=\"否\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitChangeEngineer\">确 定</el-button>\r\n        <el-button @click=\"changeEngineerOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次管理对话框 -->\r\n    <el-dialog title=\"批次管理\" :visible.sync=\"batchManagementOpen\" width=\"900px\" append-to-body @close=\"closeBatchManagement\">\r\n      <div class=\"batch-management-container\">\r\n        <!-- 状态提示信息 -->\r\n        <el-alert\r\n          v-if=\"!canEditBatch\"\r\n          title=\"当前打样单状态不允许编辑批次信息\"\r\n          type=\"warning\"\r\n          description=\"只有状态为'进行中'的打样单才可以编辑和添加测试批次内容，其他状态只能查看批次信息。\"\r\n          show-icon\r\n          :closable=\"false\"\r\n          style=\"margin-bottom: 20px;\">\r\n        </el-alert>\r\n\r\n        <!-- 进行中批次信息 -->\r\n        <el-card class=\"active-batches-card\" v-if=\"activeBatches && activeBatches.length > 0\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>进行中批次 ({{ activeBatches.length }}个)</span>\r\n            <el-button\r\n              v-if=\"canEditBatch\"\r\n              style=\"float: right; padding: 3px 0\"\r\n              type=\"text\"\r\n              @click=\"finishAllActiveBatches\">\r\n              结束所有批次\r\n            </el-button>\r\n            <el-tag\r\n              v-else\r\n              style=\"float: right;\"\r\n              type=\"info\"\r\n              size=\"mini\">\r\n              只读模式\r\n            </el-tag>\r\n          </div>\r\n          <!-- 进行中批次列表 -->\r\n          <el-table :data=\"activeBatches\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"batchIndex\" label=\"批次序号\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"primary\">第{{ scope.row.batchIndex }}批次</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"已用时长\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ calculateDuration(scope.row.startTime) }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"实验室编号\" width=\"200\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"experiment-codes\">\r\n                  <el-tag\r\n                    v-for=\"experiment in scope.row.experiments || []\"\r\n                    :key=\"experiment.id\"\r\n                    size=\"mini\"\r\n                    closable\r\n                    @close=\"removeExperiment(scope.row, experiment)\"\r\n                    style=\"margin: 2px;\"\r\n                  >\r\n                    {{ experiment.experimentCode }}\r\n                  </el-tag>\r\n                  <el-button\r\n                    v-if=\"canEditBatch\"\r\n                    type=\"text\"\r\n                    size=\"mini\"\r\n                    @click=\"showAddExperimentDialog(scope.row)\"\r\n                    icon=\"el-icon-plus\"\r\n                  >\r\n                    添加编号\r\n                  </el-button>\r\n                  <span v-if=\"(!scope.row.experiments || scope.row.experiments.length === 0) && !canEditBatch\" style=\"color: #C0C4CC;\">未设置</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"状态\" width=\"80\" align=\"center\">\r\n              <template>\r\n                <el-tag type=\"success\" size=\"mini\">进行中</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"150\" align=\"center\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"canEditBatch\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"finishSingleBatch(scope.row)\">\r\n                  结束批次\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"viewBatchDetail(scope.row)\" icon=\"el-icon-view\">\r\n                  详情\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n        </el-card>\r\n\r\n        <!-- 开始新批次按钮 -->\r\n        <div class=\"new-batch-section\" style=\"text-align: center; margin: 20px 0;\">\r\n          <el-button\r\n            v-if=\"canEditBatch\"\r\n            type=\"primary\"\r\n            size=\"medium\"\r\n            @click=\"startNewBatch\">\r\n            开始新批次\r\n          </el-button>\r\n          <div v-else style=\"text-align: center; color: #909399;\">\r\n            <i class=\"el-icon-info\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"margin-top: 16px;\">当前状态不允许开始新批次</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 所有批次列表 -->\r\n        <el-card class=\"all-batches-card\" style=\"margin-top: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>所有批次 ({{ allBatches.length }}个)</span>\r\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshBatchData\">刷新</el-button>\r\n          </div>\r\n          <el-table :data=\"allBatches\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"batchIndex\" label=\"批次序号\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"primary\">第{{ scope.row.batchIndex }}批次</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.endTime\">{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n                <span v-else style=\"color: #909399;\">未结束</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"batchStatus\" label=\"状态\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag :type=\"getBatchStatusType(scope.row.batchStatus)\" size=\"mini\">\r\n                  {{ getBatchStatusText(scope.row.batchStatus) }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"laboratoryCode\" label=\"实验室编号\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.laboratoryCode\" style=\"color: #606266;\">{{ scope.row.laboratoryCode }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">未设置</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"actualManHours\" label=\"实际工时\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" :type=\"scope.row.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ scope.row.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"qualityEvaluation\" label=\"质量评价\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag v-if=\"scope.row.qualityEvaluation\" size=\"mini\"\r\n                        :type=\"getQualityEvaluationType(scope.row.qualityEvaluation)\">\r\n                  {{ scope.row.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else style=\"color: #909399;\">未评价</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remark\" label=\"批次备注\" min-width=\"150\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.remark\" style=\"color: #606266;\">{{ scope.row.remark }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"canEditBatch && scope.row.batchStatus === 0\"\r\n                  size=\"mini\"\r\n                  type=\"success\"\r\n                  @click=\"startSingleBatch(scope.row)\">\r\n                  开始\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"canEditBatch && scope.row.batchStatus === 1\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"finishSingleBatch(scope.row)\">\r\n                  结束\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"viewBatchDetail(scope.row)\" icon=\"el-icon-view\">\r\n                  详情\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 空状态 -->\r\n          <div v-if=\"!allBatches || allBatches.length === 0\" class=\"empty-state\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-document\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">暂无批次记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button\r\n          v-if=\"currentBatchRow && currentBatchRow.completionStatus == '0'\"\r\n          type=\"success\"\r\n          icon=\"el-icon-video-play\"\r\n          @click=\"handleStartFromBatch\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:startRask']\">\r\n          开始任务\r\n        </el-button>\r\n        <el-button\r\n          v-if=\"currentBatchRow && currentBatchRow.completionStatus == '1'\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-check\"\r\n          @click=\"handleFinishFromBatch\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:endRask']\">\r\n          完成任务\r\n        </el-button>\r\n        <el-button @click=\"batchManagementOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次详情对话框 -->\r\n    <el-dialog title=\"批次详情\" :visible.sync=\"batchDetailOpen\" width=\"800px\" append-to-body @close=\"closeBatchDetail\">\r\n      <div v-if=\"batchDetailData\" class=\"batch-detail-container\">\r\n        <!-- 批次基本信息 -->\r\n        <el-card class=\"batch-info-card\" style=\"margin-bottom: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>批次基本信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>批次序号：</label>\r\n                <el-tag type=\"primary\">第{{ batchDetailData.batchIndex }}批次</el-tag>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>开始时间：</label>\r\n                <span class=\"info-value\">{{ parseTime(batchDetailData.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>结束时间：</label>\r\n                <span class=\"info-value\">\r\n                  {{ batchDetailData.endTime ? parseTime(batchDetailData.endTime, '{y}-{m}-{d} {h}:{i}:{s}') : '未结束' }}\r\n                </span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>实际工时：</label>\r\n                <el-tag :type=\"batchDetailData.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ batchDetailData.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>质量评价：</label>\r\n                <el-tag v-if=\"batchDetailData.qualityEvaluation\"\r\n                        :type=\"getQualityEvaluationType(batchDetailData.qualityEvaluation)\">\r\n                  {{ batchDetailData.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else class=\"info-value\">未评价</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row style=\"margin-top: 15px;\">\r\n            <el-col :span=\"24\">\r\n              <div class=\"info-item\">\r\n                <label>批次备注：</label>\r\n                <div class=\"remark-content\">\r\n                  {{ batchDetailData.remark || '无备注' }}\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n\r\n        <!-- 实验记录详情 -->\r\n        <el-card class=\"experiments-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>实验记录详情</span>\r\n            <el-tag size=\"mini\" style=\"margin-left: 10px;\">\r\n              共{{ batchDetailData.experiments ? batchDetailData.experiments.length : 0 }}条记录\r\n            </el-tag>\r\n          </div>\r\n          <el-table :data=\"batchDetailData.experiments\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"experimentCode\" label=\"实验编号\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span style=\"color: #409EFF; font-weight: bold;\">{{ scope.row.experimentCode }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"experimentNote\" label=\"实验备注\" min-width=\"200\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.experimentNote\">{{ scope.row.experimentNote }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"info\">{{ scope.row.createBy || '系统' }}</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 实验记录空状态 -->\r\n          <div v-if=\"!batchDetailData.experiments || batchDetailData.experiments.length === 0\"\r\n               class=\"empty-experiments\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-data-line\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">该批次暂无实验记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"batchDetailOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加实验记录对话框 -->\r\n    <el-dialog title=\"添加实验室编号\" :visible.sync=\"addExperimentOpen\" width=\"500px\" append-to-body @close=\"closeAddExperiment\">\r\n      <el-form :model=\"addExperimentForm\" ref=\"addExperimentForm\" label-width=\"100px\" :rules=\"addExperimentRules\">\r\n        <el-form-item label=\"实验编号\" prop=\"experimentCode\">\r\n          <el-input v-model=\"addExperimentForm.experimentCode\" placeholder=\"请输入实验编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"实验备注\">\r\n          <el-input v-model=\"addExperimentForm.experimentNote\" type=\"textarea\" placeholder=\"请输入实验备注\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"addExperimentForm.remark\" type=\"textarea\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAddExperiment\">确 定</el-button>\r\n        <el-button @click=\"addExperimentOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 结束批次对话框 -->\r\n    <el-dialog :title=\"finishBatchMode === 'single' ? `结束第${currentFinishBatch && currentFinishBatch.batchIndex || ''}批次` : '结束当前批次'\" :visible.sync=\"finishBatchOpen\" width=\"500px\" append-to-body @close=\"closeFinishBatch\">\r\n      <el-form :model=\"finishBatchForm\" ref=\"finishBatchForm\" label-width=\"100px\">\r\n        <el-form-item label=\"质量评价\">\r\n          <el-input v-model=\"finishBatchForm.qualityEvaluation\" type=\"textarea\" placeholder=\"请输入质量评价\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishBatchForm.remark\" type=\"textarea\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFinishBatch\">确 定</el-button>\r\n        <el-button @click=\"finishBatchOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 完成任务对话框 -->\r\n    <el-dialog title=\"完成任务\" :visible.sync=\"finishTaskOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"finishTaskForm\" :model=\"finishTaskForm\" :rules=\"finishTaskRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-select v-model=\"finishTaskForm.laboratoryCode\" multiple filterable placeholder=\"请选择实验室编码\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"item in experimentCodeList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.experimentCode\"\r\n              :value=\"item.experimentCode\">\r\n              <span style=\"float: left\">{{ item.experimentCode }}</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 13px\" v-if=\"item.experimentNote\">{{ item.experimentNote }}</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishTaskForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"finishTaskOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmFinishTask\" :loading=\"finishTaskLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 更新实验室编码对话框 -->\r\n    <el-dialog title=\"更新实验室编码\" :visible.sync=\"updateLaboratoryCodeOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"updateLaboratoryCodeForm\" :model=\"updateLaboratoryCodeForm\" :rules=\"updateLaboratoryCodeRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.laboratoryCode\" placeholder=\"请输入实验室编码(多个使用,分割)\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"updateLaboratoryCodeOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmUpdateLaboratoryCode\" :loading=\"updateLaboratoryCodeLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导出选择对话框 -->\r\n    <el-dialog title=\"选择导出内容\" :visible.sync=\"exportDialogOpen\" width=\"400px\" append-to-body>\r\n      <div class=\"export-options\">\r\n        <p style=\"margin-bottom: 20px; color: #606266;\">请选择要导出的打样单类型：</p>\r\n        <el-checkbox-group v-model=\"exportOptions\" style=\"display: flex; flex-direction: column;\">\r\n          <el-checkbox label=\"assigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">已分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出已分配给工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n          <el-checkbox label=\"unassigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">未分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出尚未分配工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n        </el-checkbox-group>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"exportDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmExport\" :disabled=\"exportOptions.length === 0\" :loading=\"exportLoading\">\r\n          确认导出\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 驳回对话框 -->\r\n    <el-dialog title=\"驳回打样单\" :visible.sync=\"rejectDialogOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" :rules=\"rejectRules\" label-width=\"100px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"rejectForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"驳回理由\" prop=\"rejectReason\">\r\n          <el-input v-model=\"rejectForm.rejectReason\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入驳回理由\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"rejectDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmReject\" :loading=\"rejectLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 逾期操作对话框 -->\r\n    <el-dialog title=\"逾期操作\" :visible.sync=\"overdueOperationOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"overdueOperationForm\" :model=\"overdueOperationForm\" label-width=\"120px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"overdueOperationForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"预计出样时间\">\r\n          <el-date-picker\r\n            v-model=\"overdueOperationForm.expectedSampleTime\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"未出样原因\">\r\n          <el-input v-model=\"overdueOperationForm.reasonForNoSample\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入未出样原因（非必填）\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"解决方案\">\r\n          <el-input v-model=\"overdueOperationForm.solution\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入解决方案（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"overdueOperationOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOverdueOperation\" :loading=\"overdueOperationLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n    <!-- 添加或修改项目流程进行对话框 -->\r\n    <executionAddOrEdit ref=\"executionAddOrEdit\"></executionAddOrEdit>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listEngineerSampleOrder,\r\n  getEngineerSampleOrder,\r\n  delEngineerSampleOrder,\r\n  addEngineerSampleOrder,\r\n  updateEngineerSampleOrder,\r\n  updateSampleOrderStatus,\r\n  getDashboardStats,\r\n  getResearchDepartments,\r\n  getEngineersByDifficultyLevel,\r\n  changeEngineer,\r\n  getResearchDepartmentsUser,\r\n  getBatchesByOrderId,\r\n  startNewBatch,\r\n  addExperimentToBatch,\r\n  getExperimentsByBatchId,\r\n  exportEngineerSampleOrder,\r\n  getExecutionByOrderInfo,\r\n  getBatchExperimentCodeList,\r\n  getActiveBatches,\r\n  startSingleBatch,\r\n  finishSingleBatch,\r\n  finishAllActiveBatches,\r\n  removeExperimentFromBatch\r\n} from \"@/api/software/engineerSampleOrder\";\r\nimport {exportNrwItem, exportMultipleNrw} from \"@/api/project/projectItemOrder\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\nimport {customerBaseAll} from \"@/api/customer/customer\";\r\nimport {isNull} from \"@/utils/validate\";\r\nimport executionAddOrEdit from \"@/components/Project/components/executionAddOrEdit.vue\";\r\n\r\nexport default {\r\n  name: \"EngineerSampleOrder\",\r\n  components: {\r\n     Treeselect,executionAddOrEdit\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中的完整行数据\r\n      selectedRows: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 工程师打样单关联表格数据\r\n      engineerSampleOrderList: [],\r\n      // 研发部部门树列表\r\n      researchDeptDatas:[],\r\n      // 打样类别字典\r\n      dylbOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userId: null,\r\n        nickName: null,\r\n        sampleOrderCode: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        startDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        deptId: null,\r\n        deptIds: [],\r\n        associationStatus: null,\r\n        customerId: null,\r\n        productName: null,\r\n        confirmCode: null,\r\n        isOverdue: null,  // 增逾期任务过滤参数\r\n        laboratory: null  // 实验室筛选参数\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        userId: [\r\n          { required: true, message: \"工程师ID不能为空\", trigger: \"blur\" }\r\n        ],\r\n        sampleOrderCode: [\r\n          { required: true, message: \"打样单编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        completionStatus: [\r\n          { required: true, message: \"完成情况不能为空\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      statusOptions: [],\r\n      serviceModeOptions: [],\r\n      dateRange: [], // 新增的日期范围筛选\r\n      scheduledDateRange: [],\r\n      startDateRange: [],\r\n      actualStartTimeRange: [],\r\n      actualFinishTimeRange: [],\r\n      // 日期选择器配置\r\n      dataPickerOptions: {\r\n        shortcuts: [{\r\n          text: '今天',\r\n          onClick(picker) {\r\n            const today = new Date();\r\n            picker.$emit('pick', [today, today]);\r\n          }\r\n        }, {\r\n          text: '昨天',\r\n          onClick(picker) {\r\n            const yesterday = new Date();\r\n            yesterday.setTime(yesterday.getTime() - 3600 * 1000 * 24);\r\n            picker.$emit('pick', [yesterday, yesterday]);\r\n          }\r\n        }, {\r\n          text: '最近一周',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近一个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }]\r\n      },\r\n      // 状态更新对话框\r\n      statusOpen: false,\r\n      dashboardStats: {\r\n        \"total\": 0,\r\n        \"completed\": 0,\r\n        \"inProgress\": 0,\r\n        \"overdue\": 0\r\n      },\r\n      // 更改工程师对话框\r\n      changeEngineerOpen: false,\r\n      // 更改工程师表单\r\n      changeEngineerForm: {\r\n        id: null,\r\n        sampleOrderCode: null,\r\n        currentEngineerName: null,\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      },\r\n      // 更改工程师表单校验\r\n      changeEngineerRules: {\r\n        newEngineerId: [\r\n          { required: true, message: \"请选择工程师\", trigger: \"change\" }\r\n        ],\r\n        scheduledDate: [\r\n          { required: true, message: \"请选择日期\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 工程师选项\r\n      engineerOptions: [],\r\n      // 批次管理相关\r\n      batchManagementOpen: false,\r\n      activeBatches: [], // 进行中的批次列表\r\n      allBatches: [], // 所有批次列表\r\n      selectedOrderForBatch: null,\r\n      currentBatchRow: null, // 当前批次管理的行数据\r\n      // 批次详情对话框\r\n      batchDetailOpen: false,\r\n      batchDetailData: null,\r\n      // 结束批次对话框\r\n      finishBatchOpen: false,\r\n      finishBatchForm: {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      },\r\n      currentFinishBatch: null, // 当前要结束的批次（单个批次时使用）\r\n      finishBatchMode: 'all', // 'all' 表示结束所有批次，'single' 表示结束单个批次\r\n      // 添加实验记录对话框\r\n      addExperimentOpen: false,\r\n      addExperimentForm: {\r\n        experimentCode: '',\r\n        experimentNote: '',\r\n        remark: ''\r\n      },\r\n      addExperimentRules: {\r\n        experimentCode: [\r\n          { required: true, message: '实验编号不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      currentBatchForExperiment: null,\r\n      // 未来日期选择器配置\r\n      futureDatePickerOptions: {\r\n        disabledDate(time) {\r\n          return time.getTime() < Date.now() - 8.64e7; // 禁用今天之前的日期\r\n        }\r\n      },\r\n      engineerSelectType: 'specified',\r\n      searchedEngineers: [],\r\n      // 未分配打样单告警\r\n      unassignedOrders: [],\r\n      unassignedQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 8\r\n      },\r\n      unassignedTotal: 0,\r\n      // 未分配面板折叠状态\r\n      isUnassignedPanelCollapsed: true,\r\n      // 完成任务对话框\r\n      finishTaskOpen: false,\r\n      finishTaskLoading: false,\r\n      finishTaskForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      experimentCodeList: [], // 实验室编码列表\r\n      finishTaskRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentFinishRow: null,\r\n      // 更新实验室编码对话框\r\n      updateLaboratoryCodeOpen: false,\r\n      updateLaboratoryCodeLoading: false,\r\n      updateLaboratoryCodeForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      updateLaboratoryCodeRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentUpdateLaboratoryCodeRow: null,\r\n      // 导出选择对话框\r\n      exportDialogOpen: false,\r\n      exportOptions: [],\r\n      exportLoading: false,\r\n      readonly: true,\r\n      // 项目详情相关\r\n      currentProjectType: null,\r\n      confirmItemCodes: [],\r\n      customerOptions: [],\r\n      itemNames: [],\r\n      // 驳回对话框\r\n      rejectDialogOpen: false,\r\n      rejectLoading: false,\r\n      rejectForm: {\r\n        sampleOrderCode: '',\r\n        rejectReason: ''\r\n      },\r\n      // 逾期操作对话框\r\n      overdueOperationOpen: false,\r\n      overdueOperationLoading: false,\r\n      overdueOperationForm: {\r\n        sampleOrderCode: '',\r\n        expectedSampleTime: '',\r\n        reasonForNoSample: '',\r\n        solution: ''\r\n      },\r\n      currentOverdueRow: null,\r\n      rejectRules: {\r\n        rejectReason: [\r\n          { required: true, message: \"驳回理由不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentRejectRow: null,\r\n      // 新增打样单对话框\r\n      addSampleOrderOpen: false,\r\n      addSampleOrderLoading: false,\r\n      addSampleOrderForm: {\r\n        labNo: '',\r\n        categoryText: '',\r\n        dylb: '',\r\n        remark: ''\r\n      },\r\n      addSampleOrderRules: {\r\n        labNo: [\r\n          { required: true, message: \"实验室不能为空\", trigger: \"change\" }\r\n        ],\r\n        categoryText: [\r\n          { required: true, message: \"品类不能为空\", trigger: \"change\" }\r\n        ],\r\n        dylb: [\r\n          { required: true, message: \"打样单难度类别不能为空\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 产品类别选项\r\n      sckxxpgCplbs: []\r\n    };\r\n  },\r\n  computed: {\r\n    /** 计算是否可以编辑批次 - 只有状态为'1'(进行中)的打样单才可以编辑 */\r\n    canEditBatch() {\r\n      return this.selectedOrderForBatch && this.selectedOrderForBatch.completionStatus === 1;\r\n    }\r\n  },\r\n  created() {\r\n    // 设置默认日期范围为当天\r\n    const today = new Date();\r\n    const todayStr = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');\r\n    this.dateRange = [todayStr, todayStr];\r\n\r\n    this.getList();\r\n    customerBaseAll().then(res=> this.customerOptions = res)\r\n\r\n    this.getDicts(\"DYD_GCSZT\").then(response => {\r\n        this.statusOptions = response.data;\r\n    });\r\n    this.getDicts(\"CUSTOMER_SERVICE_MODE\").then(response => {\r\n      this.serviceModeOptions = response.data;\r\n    });\r\n    this.getDicts(\"project_nrw_dylb\").then(response => {\r\n      this.dylbOptions = response.data;\r\n    });\r\n    this.getDicts(\"SCKXXPG_CPLB\").then(response => {\r\n      this.sckxxpgCplbs = response.data;\r\n    });\r\n    this.loadDashboardStats();\r\n    // 获取研发部部门列表\r\n    getResearchDepartments().then(response => {\r\n      this.researchDeptDatas = this.handleTree(response.data, \"deptId\");\r\n    });\r\n    // 获取未分配打样单\r\n    this.handleUnassignedOrders();\r\n  },\r\n  methods: {\r\n    /** 计算产品/项目等级 */\r\n    getProjectLevel(row) {\r\n      const customerLevel = row.customerLevel || '';\r\n      const projectLevel = row.projectLevel || '';\r\n\r\n      // 如果 projectLevel 是空字符串或 \"/\" 就不相加\r\n      if (projectLevel === '' || projectLevel === '/') {\r\n        return customerLevel;\r\n      }\r\n\r\n      return customerLevel + projectLevel;\r\n    },\r\n    /** 查询工程师打样单关联列表 */\r\n    getList() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.loading = true;\r\n      params.associationStatus = 1\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.engineerSampleOrderList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        userId: null,\r\n        sampleOrderCode: null,\r\n        serviceMode: null,\r\n        difficultyLevelId: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        actualManHours: null,\r\n        estimatedManHours: null,\r\n        standardManHours: null,\r\n        qualityEvaluation: null,\r\n        isLocked: 0,\r\n        startDate: null,\r\n        endDate: null,\r\n        checkType: null,\r\n        sampleOrderRemark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.unassignedQueryParams.pageNum = 1;\r\n      this.getList();\r\n      this.loadDashboardStats();\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.dateRange = [] // 重置日期范围\r\n      this.scheduledDateRange = []\r\n      this.startDateRange = []\r\n      this.actualStartTimeRange = []\r\n      this.actualFinishTimeRange = []\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.selectedRows = selection\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加工程师打样单关联\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getEngineerSampleOrder(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改工程师打样单关联\";addEngineerSampleOrder\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          } else {\r\n            addEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除工程师打样单关联编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function () {\r\n        return delEngineerSampleOrder(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.msgSuccess(\"删除成功\");\r\n        this.loadDashboardStats();\r\n      }).catch(() => { });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 重置导出选项并显示选择对话框\r\n      this.exportOptions = [];\r\n      this.exportDialogOpen = true;\r\n    },\r\n    /** 确认导出操作 */\r\n    confirmExport() {\r\n      if (this.exportOptions.length === 0) {\r\n        this.$message.warning('请至少选择一种导出类型');\r\n        return;\r\n      }\r\n      this.exportLoading = true;\r\n      this.executeExports(0);\r\n    },\r\n\r\n    /** 串行执行导出操作 */\r\n    executeExports(index) {\r\n      if (index >= this.exportOptions.length) {\r\n        // 所有导出完成\r\n        this.exportLoading = false;\r\n        this.exportDialogOpen = false;\r\n        this.$message.success(`导出完成，共导出${this.exportOptions.length}个文件`);\r\n        return;\r\n      }\r\n\r\n      const option = this.exportOptions[index];\r\n      const associationStatus = option === 'assigned' ? 1 : 0;\r\n      const typeName = option === 'assigned' ? '已分配' : '未分配';\r\n\r\n      this.doExport(associationStatus, option).then(() => {\r\n        this.$message.success(`${typeName}打样单导出成功`);\r\n        // 继续导出下一个\r\n        this.executeExports(index + 1);\r\n      }).catch((error) => {\r\n        this.exportLoading = false;\r\n        this.$message.error(`${typeName}打样单导出失败: ${error.message || error}`);\r\n      });\r\n    },\r\n    /** 执行导出操作 */\r\n    doExport(associationStatus, exportType) {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = associationStatus;\r\n      params.exportType = exportType;\r\n      return exportEngineerSampleOrder(params).then(response => {\r\n        this.download(response.msg);\r\n      });\r\n    },\r\n    /** 执行导出打样单（已分配/未分配） */\r\n    doExportSampleOrder() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.$confirm('是否确认导出所有（已分配/未分配）打样单关联数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportEngineerSampleOrder(params);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"开始\"按钮操作 */\r\n    handleStart(row) {\r\n      this.$confirm('确定要将打样单：' + row.sampleOrderCode + '标记为进行中吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        updateSampleOrderStatus({\r\n          id: row.id,\r\n          status: '1'\r\n        }).then(response => {\r\n          this.msgSuccess('打样单已设置为已开始');\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      });\r\n    },\r\n    /** 下载打样单操作 */\r\n    handleDownloadSampleOrder(row) {\r\n      this.$confirm('是否确认导出打样单?', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportNrwItem({itemId: row.itemId,projectId: row.projectId})\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n      }).catch(() => {});\r\n    },\r\n    /** 批量导出打样单任务操作 */\r\n    handleBatchExportNrw() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$modal.msgError(\"请选择要导出的打样单任务\");\r\n        return;\r\n      }\r\n\r\n      // 构造批量导出数据，传递itemId和projectId\r\n      const exportData = this.selectedRows.map(row => ({\r\n        itemId: row.itemId,\r\n        projectId: row.projectId\r\n      }));\r\n\r\n      this.$confirm('是否确认导出所选打样单任务的内容物数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportMultipleNrw(exportData);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"完成\"按钮操作 */\r\n    handleFinish(row) {\r\n      this.currentFinishRow = row;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      // 加载实验室编码列表\r\n      this.loadExperimentCodeList(row.id);\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认完成任务 */\r\n    confirmFinishTask() {\r\n      this.$refs.finishTaskForm.validate(valid => {\r\n        // 处理实验室编号字段\r\n        let laboratoryCode = this.finishTaskForm.laboratoryCode;\r\n\r\n        // 如果是数组类型，使用逗号拼接\r\n        if (Array.isArray(laboratoryCode)) {\r\n          laboratoryCode = laboratoryCode.join(\",\");\r\n        }\r\n        if (valid) {\r\n          this.finishTaskLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentFinishRow.id,\r\n            status: '2',\r\n            laboratoryCode: laboratoryCode,\r\n            remark: this.finishTaskForm.remark\r\n          }).then(response => {\r\n            this.finishTaskLoading = false;\r\n            this.finishTaskOpen = false;\r\n            this.msgSuccess('打样单已设置为已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.finishTaskLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 从批次管理对话框完成任务 */\r\n    handleFinishFromBatch() {\r\n      this.currentFinishRow = this.currentBatchRow;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      // 加载实验室编码列表\r\n      this.loadExperimentCodeList(this.currentBatchRow.id);\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 从批次管理对话框开始任务 */\r\n    handleStartFromBatch() {\r\n      const row = this.currentBatchRow;\r\n      this.$confirm('确定要将打样单：' + row.sampleOrderCode + '标记为进行中吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        updateSampleOrderStatus({\r\n          id: row.id,\r\n          status: '1'\r\n        }).then(response => {\r\n          this.msgSuccess('打样单已设置为已开始');\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n          // 更新当前批次行数据的状态\r\n          this.currentBatchRow.completionStatus = '1';\r\n          this.selectedOrderForBatch.completionStatus = 1;\r\n          if (this.selectedOrderForBatch) {\r\n            this.loadBatchData(this.selectedOrderForBatch.id);\r\n          }\r\n        });\r\n      });\r\n    },\r\n    /** 点击\"更新实验室编码\"按钮操作 */\r\n    handleUpdateLaboratoryCode(row) {\r\n      this.currentUpdateLaboratoryCodeRow = row;\r\n      // 回显当前的实验室编码和备注\r\n      this.updateLaboratoryCodeForm.laboratoryCode = row.laboratoryCode || '';\r\n      this.updateLaboratoryCodeForm.remark = row.remark || '';\r\n      this.updateLaboratoryCodeOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.updateLaboratoryCodeForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认更新实验室编码 */\r\n    confirmUpdateLaboratoryCode() {\r\n      this.$refs.updateLaboratoryCodeForm.validate(valid => {\r\n        if (valid) {\r\n          this.updateLaboratoryCodeLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentUpdateLaboratoryCodeRow.id,\r\n            status: this.currentUpdateLaboratoryCodeRow.completionStatus, // 保持当前状态\r\n            laboratoryCode: this.updateLaboratoryCodeForm.laboratoryCode,\r\n            remark: this.updateLaboratoryCodeForm.remark\r\n          }).then(response => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n            this.updateLaboratoryCodeOpen = false;\r\n            this.msgSuccess('实验室编码更新成功');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"驳回\"按钮操作 */\r\n    handleReject(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认驳回 */\r\n    confirmReject() {\r\n      this.$refs.rejectForm.validate(valid => {\r\n        if (valid) {\r\n          this.rejectLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentRejectRow.id,\r\n            status: '3',\r\n            rejectReason: this.rejectForm.rejectReason\r\n          }).then(response => {\r\n            this.rejectLoading = false;\r\n            this.rejectDialogOpen = false;\r\n            this.msgSuccess('打样单已驳回');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n            // 如果是未分配打样单的驳回，也需要刷新未分配列表\r\n            this.handleUnassignedOrders();\r\n          }).catch(() => {\r\n            this.rejectLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"逾期操作\"按钮操作 */\r\n    handleOverdueOperation(row) {\r\n      this.currentOverdueRow = row;\r\n      this.overdueOperationForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.overdueOperationForm.expectedSampleTime = row.expectedSampleTime;\r\n      this.overdueOperationForm.reasonForNoSample = row.reasonForNoSample;\r\n      this.overdueOperationForm.solution = row.solution;\r\n      this.overdueOperationOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.overdueOperationForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认逾期操作 */\r\n    confirmOverdueOperation() {\r\n      this.$refs.overdueOperationForm.validate(valid => {\r\n        if (valid) {\r\n          this.overdueOperationLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentOverdueRow.id,\r\n            status: \"11\", // 特殊处理，只更新“逾期情况”填写的字段\r\n            expectedSampleTime: this.overdueOperationForm.expectedSampleTime,\r\n            reasonForNoSample: this.overdueOperationForm.reasonForNoSample,\r\n            solution: this.overdueOperationForm.solution\r\n          }).then(response => {\r\n            this.overdueOperationLoading = false;\r\n            this.overdueOperationOpen = false;\r\n            this.msgSuccess('逾期操作已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.overdueOperationLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 查看打样单详情 */\r\n    async orderDetail(row) {\r\n      try {\r\n        //获取执行ID\r\n        const orderId = row.projectOrderId;\r\n        let data = await getExecutionByOrderInfo(orderId);\r\n        if(data!=null && !isNull(data.id)){\r\n            let id = data.id;\r\n            this.$refs.executionAddOrEdit.open = true;\r\n            await this.$nextTick()\r\n            this.$refs.executionAddOrEdit.reset()\r\n            this.$refs.executionAddOrEdit.show(id, 1)\r\n        }else{\r\n          this.$message.error('获取数据失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('查看项目详情失败:', error);\r\n        this.$message.error('查看项目详情失败: ' + (error.message || '未知错误'));\r\n      }\r\n    },\r\n\r\n    /** 锁定状态改变处理 */\r\n    handleLockChange(value) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.form.isLocked = 1;\r\n        }).catch(() => {\r\n          this.form.isLocked = 0;\r\n        });\r\n      }\r\n    },\r\n    /** 表格中锁定状态改变处理 */\r\n    handleTableLockChange(value, row) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          // 调用更新接口\r\n          updateEngineerSampleOrder({\r\n            id: row.id,\r\n            isLocked: 1\r\n          }).then(response => {\r\n            this.msgSuccess(\"锁定成功\");\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          });\r\n        }).catch(() => {\r\n          // 取消操作，恢复原值\r\n          row.isLocked = 0;\r\n        });\r\n      } else {\r\n        // 解锁操作\r\n        updateEngineerSampleOrder({\r\n          id: row.id,\r\n          isLocked: 0\r\n        }).then(response => {\r\n          this.msgSuccess(\"解锁成功\");\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      }\r\n    },\r\n    /** 加载统计概览数据 */\r\n    loadDashboardStats() {\r\n      let params = { ...this.queryParams };\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      getDashboardStats(params).then(response => {\r\n        this.dashboardStats = response.data || {};\r\n      });\r\n    },\r\n    /** 更改工程师按钮操作 */\r\n    handleChangeEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: row.nickName,\r\n        oldEngineerId: row.userId,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 提交更改工程师 */\r\n    submitChangeEngineer() {\r\n      this.$refs[\"changeEngineerForm\"].validate(valid => {\r\n        if (valid) {\r\n          const data = {\r\n            sampleOrderId: this.changeEngineerForm.id,\r\n            oldEngineerId: this.changeEngineerForm.oldEngineerId,\r\n            newEngineerId: this.changeEngineerForm.newEngineerId,\r\n            scheduledDate: this.changeEngineerForm.scheduledDate,\r\n            adjustWorkSchedule: this.changeEngineerForm.adjustWorkSchedule\r\n          };\r\n\r\n          // 调用更改工程师的API接口\r\n          changeEngineer(data).then(response => {\r\n            this.msgSuccess(\"更改工程师成功\");\r\n            this.changeEngineerOpen = false;\r\n            this.getList();\r\n            // 获取未分配打样单\r\n            this.handleUnassignedOrders();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.msgError(\"更改工程师失败\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 获取未分配打样单列表 */\r\n    handleUnassignedOrders() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = 0\r\n      params.pageNum = this.unassignedQueryParams.pageNum;\r\n      params.pageSize = this.unassignedQueryParams.pageSize;\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.unassignedOrders = response.rows;\r\n        this.unassignedTotal = response.total;\r\n      });\r\n    },\r\n    /** 分配工程师操作 */\r\n    handleAssignEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: '',\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 删除未分配打样单 */\r\n    handleDeleteUnassigned(row) {\r\n      this.$confirm('是否确认删除打样单编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function() {\r\n        return delEngineerSampleOrder(row.id);\r\n      }).then(() => {\r\n        this.handleUnassignedOrders();\r\n        this.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 驳回未分配打样单 */\r\n    handleRejectUnassigned(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 未分配打样单分页大小改变 */\r\n    handleUnassignedSizeChange(newSize) {\r\n      this.unassignedQueryParams.pageSize = newSize;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 未分配打样单页码改变 */\r\n    handleUnassignedCurrentChange(newPage) {\r\n      this.unassignedQueryParams.pageNum = newPage;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 切换未分配面板显示状态 */\r\n    toggleUnassignedPanel() {\r\n      this.isUnassignedPanelCollapsed = !this.isUnassignedPanelCollapsed;\r\n    },\r\n    /** 处理状态过滤 */\r\n    handleStatusFilter(status) {\r\n      // 清除逾期过滤\r\n      this.queryParams.isOverdue = null;\r\n      this.queryParams.completionStatus = status;\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 处理逾期任务过滤 */\r\n    handleOverdueFilter() {\r\n      // 清除完成状态过滤\r\n      this.queryParams.completionStatus = null;\r\n      // 设置逾期过滤\r\n      this.queryParams.isOverdue = this.queryParams.isOverdue === 1 ? null : 1;\r\n      this.handleQuery();\r\n\r\n      if (this.queryParams.isOverdue === 1) {\r\n        this.$message.info('已筛选显示逾期任务');\r\n      } else {\r\n        this.$message.info('已清除逾期任务筛选');\r\n      }\r\n    },\r\n    /** 获取紧急程度类型 */\r\n    getUrgencyType(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return 'danger'; // 已超过最晚开始时间\r\n      }\r\n\r\n      if (daysToEnd <= 1) {\r\n        return 'danger'; // 紧急：1天内截止\r\n      } else if (daysToEnd <= 3) {\r\n        return 'warning'; // 警告：3天内截止\r\n      } else if (daysToEnd <= 7) {\r\n        return 'primary'; // 一般：7天内截止\r\n      } else {\r\n        return 'success'; // 充足时间\r\n      }\r\n    },\r\n    /** 获取紧急程度文本 */\r\n    getUrgencyText(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return '超期未开始';\r\n      }\r\n\r\n      if (daysToEnd <= 0) {\r\n        return '已逾期';\r\n      } else if (daysToEnd <= 1) {\r\n        return '紧急';\r\n      } else if (daysToEnd <= 3) {\r\n        return '较急';\r\n      } else if (daysToEnd <= 7) {\r\n        return '一般';\r\n      } else {\r\n        return '充足';\r\n      }\r\n    },\r\n    /** 刷新未分配打样单列表 */\r\n    handleRefreshUnassigned() {\r\n      this.handleUnassignedOrders();\r\n      this.$message.success('刷新成功');\r\n    },\r\n    /** 获取逾期信息 */\r\n    getOverdueInfo(row) {\r\n      const now = new Date();\r\n      const endDate = new Date(row.endDate);\r\n      let isOverdue = false;\r\n      let overdueDays = 0;\r\n      // 根据后台SQL逻辑判断逾期\r\n      if (row.completionStatus === 2) {\r\n        // 已完成且逾期：实际完成时间 > 截止日期\r\n        if (row.actualFinishTime) {\r\n          const actualFinishTime = new Date(row.actualFinishTime);\r\n          if (actualFinishTime > endDate) {\r\n            isOverdue = true;\r\n            overdueDays = Math.ceil((actualFinishTime - endDate) / (1000 * 60 * 60 * 24));\r\n          }\r\n        }\r\n      } else if (row.completionStatus === 1) {\r\n        // 进行中且逾期：当前时间 > 截止日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      } else if (row.completionStatus === 0) {\r\n        // 未开始且逾期：未开始但当前时间 > 开始日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      }\r\n\r\n      return {\r\n        isOverdue,\r\n        overdueDays\r\n      };\r\n    },\r\n\r\n    // ==================== 批次管理相关方法 ====================\r\n\r\n    /** 打开批次管理对话框 */\r\n    handleBatchManagement(row) {\r\n      this.selectedOrderForBatch = row;\r\n      this.currentBatchRow = row; // 保存当前行数据\r\n      this.batchManagementOpen = true;\r\n      this.loadBatchData(row.id);\r\n    },\r\n\r\n    /** 加载批次数据 */\r\n    async loadBatchData(engineerSampleOrderId) {\r\n      try {\r\n        // 加载进行中的批次\r\n        const activeBatchesResponse = await this.getActiveBatchesData(engineerSampleOrderId);\r\n        this.activeBatches = activeBatchesResponse.data || [];\r\n\r\n        // 为每个进行中的批次加载实验记录\r\n        for (let batch of this.activeBatches) {\r\n          await this.loadBatchExperiments(batch);\r\n        }\r\n\r\n        // 加载所有批次\r\n        const batchesResponse = await getBatchesByOrderId(engineerSampleOrderId);\r\n        this.allBatches = batchesResponse.data || [];\r\n      } catch (error) {\r\n        console.error('加载批次数据失败:', error);\r\n        this.$message.error('加载批次数据失败');\r\n      }\r\n    },\r\n\r\n    /** 加载批次的实验记录 */\r\n    async loadBatchExperiments(batch) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batch.id);\r\n        this.$set(batch, 'experiments', response.data || []);\r\n      } catch (error) {\r\n        console.error('加载批次实验记录失败:', error);\r\n        this.$set(batch, 'experiments', []);\r\n      }\r\n    },\r\n\r\n    /** 获取进行中的批次 */\r\n    async getActiveBatchesData(engineerSampleOrderId) {\r\n      try {\r\n        // 调用新的API获取进行中的批次\r\n        const response = await getActiveBatches(engineerSampleOrderId);\r\n        return response;\r\n      } catch (error) {\r\n        console.error('获取进行中批次失败:', error);\r\n        return { data: [] };\r\n      }\r\n    },\r\n\r\n    /** 开始新批次 */\r\n    async startNewBatch() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许开始新批次，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$confirm('确认开始新的打样批次？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        const response = await startNewBatch(this.selectedOrderForBatch.id, '');\r\n        this.$message.success('新批次开始成功');\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('开始新批次失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 开始单个批次 */\r\n    async startSingleBatch(batch) {\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许开始批次');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$confirm(`确认开始第${batch.batchIndex}批次？`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        await startSingleBatch(batch.id);\r\n        this.$message.success('批次开始成功');\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n        this.getList();\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('开始批次失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 结束单个批次 */\r\n    finishSingleBatch(batch) {\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许结束批次');\r\n        return;\r\n      }\r\n\r\n      // 设置为单个批次结束模式\r\n      this.finishBatchMode = 'single';\r\n      this.currentFinishBatch = batch;\r\n\r\n      // 重置表单\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 显示对话框\r\n      this.finishBatchOpen = true;\r\n    },\r\n\r\n    /** 结束所有进行中批次 */\r\n    finishAllActiveBatches() {\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许结束批次');\r\n        return;\r\n      }\r\n\r\n      // 设置为结束所有批次模式\r\n      this.finishBatchMode = 'all';\r\n      this.currentFinishBatch = null;\r\n\r\n      // 重置表单\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 显示对话框\r\n      this.finishBatchOpen = true;\r\n    },\r\n\r\n    /** 提交结束批次 */\r\n    async submitFinishBatch() {\r\n      try {\r\n        if (this.finishBatchMode === 'single') {\r\n          // 结束单个批次\r\n          await finishSingleBatch(\r\n            this.currentFinishBatch.id,\r\n            this.finishBatchForm.qualityEvaluation || '',\r\n            this.finishBatchForm.remark || '',\r\n            this.currentFinishBatch.laboratoryCode || ''\r\n          );\r\n          this.$message.success('批次结束成功');\r\n        } else {\r\n          // 结束所有进行中的批次\r\n          await finishAllActiveBatches(this.selectedOrderForBatch.id);\r\n          this.$message.success('所有批次结束成功');\r\n        }\r\n\r\n        this.finishBatchOpen = false;\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        console.error('结束批次失败:', error);\r\n        this.$message.error('结束批次失败: ' + (error.message || error));\r\n      }\r\n    },\r\n\r\n    /** 查看批次详情 */\r\n    async viewBatchDetail(batch) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batch.id);\r\n        const experiments = response.data || [];\r\n\r\n        // 设置批次详情数据\r\n        this.batchDetailData = {\r\n          ...batch,\r\n          experiments: experiments\r\n        };\r\n\r\n        this.batchDetailOpen = true;\r\n      } catch (error) {\r\n        console.error('查看批次详情失败:', error);\r\n        this.$message.error('查看批次详情失败');\r\n      }\r\n    },\r\n\r\n    /** 计算持续时间 */\r\n    calculateDuration(startTime) {\r\n      if (!startTime) return '0小时';\r\n\r\n      const start = new Date(startTime);\r\n      const now = new Date();\r\n      const diffMs = now - start;\r\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\r\n      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\r\n\r\n      if (diffHours > 0) {\r\n        return `${diffHours}小时${diffMinutes}分钟`;\r\n      } else {\r\n        return `${diffMinutes}分钟`;\r\n      }\r\n    },\r\n\r\n    /** 编辑实验室编号 */\r\n    editLaboratoryCode(batch) {\r\n      this.$set(batch, 'editingLab', true);\r\n      this.$nextTick(() => {\r\n        // 聚焦到输入框\r\n        const input = this.$el.querySelector(`input[value=\"${batch.laboratoryCode || ''}\"]`);\r\n        if (input) input.focus();\r\n      });\r\n    },\r\n\r\n    /** 显示添加实验记录对话框 */\r\n    showAddExperimentDialog(batch) {\r\n      this.currentBatchForExperiment = batch;\r\n      this.addExperimentForm = {\r\n        experimentCode: '',\r\n        experimentNote: '',\r\n        remark: ''\r\n      };\r\n      this.addExperimentOpen = true;\r\n    },\r\n\r\n    /** 关闭添加实验记录对话框 */\r\n    closeAddExperiment() {\r\n      this.addExperimentOpen = false;\r\n      this.currentBatchForExperiment = null;\r\n      this.$refs.addExperimentForm && this.$refs.addExperimentForm.resetFields();\r\n    },\r\n\r\n    /** 提交添加实验记录 */\r\n    async submitAddExperiment() {\r\n      try {\r\n        await this.$refs.addExperimentForm.validate();\r\n\r\n        const experimentRecord = {\r\n          batchId: this.currentBatchForExperiment.id,\r\n          experimentCode: this.addExperimentForm.experimentCode,\r\n          experimentNote: this.addExperimentForm.experimentNote || '',\r\n          remark: this.addExperimentForm.remark\r\n        };\r\n\r\n        await addExperimentToBatch(experimentRecord);\r\n\r\n        this.$message.success('实验记录添加成功');\r\n        this.addExperimentOpen = false;\r\n\r\n        // 重新加载当前批次的实验记录\r\n        await this.loadBatchExperiments(this.currentBatchForExperiment);\r\n      } catch (error) {\r\n        this.$message.error('添加实验记录失败: ' + (error.message || error));\r\n      }\r\n    },\r\n\r\n    /** 删除实验记录 */\r\n    async removeExperiment(batch, experiment) {\r\n      try {\r\n        await this.$confirm('确认删除该实验记录吗？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        await removeExperimentFromBatch(experiment.id);\r\n        this.$message.success('实验记录删除成功');\r\n\r\n        // 重新加载当前批次的实验记录\r\n        await this.loadBatchExperiments(batch);\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除实验记录失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 获取批次状态文本 */\r\n    getBatchStatusText(status) {\r\n      const statusMap = {\r\n        0: '未开始',\r\n        1: '进行中',\r\n        2: '已完成',\r\n        3: '已取消'\r\n      };\r\n      return statusMap[status] || '未知';\r\n    },\r\n\r\n    /** 获取批次状态类型 */\r\n    getBatchStatusType(status) {\r\n      const typeMap = {\r\n        0: 'info',\r\n        1: 'success',\r\n        2: 'primary',\r\n        3: 'danger'\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    /** 获取质量评价类型 */\r\n    getQualityEvaluationType(evaluation) {\r\n      if (!evaluation) return '';\r\n\r\n      const lowerEval = evaluation.toLowerCase();\r\n      if (lowerEval.includes('优秀') || lowerEval.includes('良好') || lowerEval.includes('好')) {\r\n        return 'success';\r\n      } else if (lowerEval.includes('一般') || lowerEval.includes('中等')) {\r\n        return 'warning';\r\n      } else if (lowerEval.includes('差') || lowerEval.includes('不合格') || lowerEval.includes('失败')) {\r\n        return 'danger';\r\n      } else {\r\n        return 'info';\r\n      }\r\n    },\r\n\r\n    /** 关闭批次管理对话框并清空数据 */\r\n    closeBatchManagement() {\r\n      // 清空批次管理相关的数据\r\n      this.activeBatches = [];\r\n      this.allBatches = [];\r\n      this.selectedOrderForBatch = null;\r\n      this.currentBatchRow = null; // 清空当前行数据\r\n      this.batchDetailData = null;\r\n\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 关闭所有相关的子对话框\r\n      this.batchDetailOpen = false;\r\n      this.addExperimentOpen = false;\r\n      this.finishBatchOpen = false;\r\n    },\r\n\r\n    /** 关闭批次详情对话框并清空数据 */\r\n    closeBatchDetail() {\r\n      // 清空批次详情数据\r\n      this.batchDetailData = null;\r\n    },\r\n\r\n    /** 关闭结束批次对话框并清空数据 */\r\n    closeFinishBatch() {\r\n      // 清空结束批次表单数据\r\n      this.finishBatchOpen = false;\r\n      this.finishBatchMode = 'all';\r\n      this.currentFinishBatch = null;\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n      // 清除表单验证状态\r\n      if (this.$refs.finishBatchForm) {\r\n        this.$refs.finishBatchForm.clearValidate();\r\n      }\r\n    },\r\n\r\n    /** 刷新批次数据 */\r\n    async refreshBatchData() {\r\n      if (this.selectedOrderForBatch) {\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n        this.$message.success('批次数据已刷新');\r\n      }\r\n    },\r\n\r\n    /** 加载实验室编码列表 */\r\n    async loadExperimentCodeList(engineerSampleOrderId) {\r\n      try {\r\n        const response = await getBatchExperimentCodeList(engineerSampleOrderId);\r\n        this.experimentCodeList = response.data || [];\r\n      } catch (error) {\r\n        console.error('加载实验室编码列表失败:', error);\r\n        this.experimentCodeList = [];\r\n        this.$message.error('加载实验室编码列表失败');\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.stats-row {\r\n  margin-bottom: 20px;\r\n}\r\n.stats-card {\r\n  border: none;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* 批次详情样式 */\r\n.batch-detail-container {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.batch-info-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-value {\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.remark-content {\r\n  background-color: #F5F7FA;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409EFF;\r\n  color: #606266;\r\n  line-height: 1.5;\r\n  margin-top: 8px;\r\n  min-height: 40px;\r\n}\r\n\r\n.experiments-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.empty-state, .empty-experiments {\r\n  background-color: #FAFAFA;\r\n  border-radius: 8px;\r\n  margin: 10px 0;\r\n}\r\n\r\n/* 历史批次表格样式优化 */\r\n.history-batches-card .el-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-batches-card .el-table th {\r\n  background-color: #F5F7FA;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.history-batches-card .el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n.stats-card:not(.total) {\r\n  cursor: pointer;\r\n}\r\n\r\n.stats-card:not(.total):hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stats-card.active {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  position: relative;\r\n}\r\n\r\n.stats-card.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 3px;\r\n  background: currentColor;\r\n}\r\n\r\n.stats-card.completed.active::after {\r\n  background: #67C23A;\r\n}\r\n\r\n.stats-card.in-progress.active::after {\r\n  background: #409EFF;\r\n}\r\n\r\n.stats-card.overdue.active::after {\r\n  background: #F56C6C;\r\n}\r\n\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n\r\n.stats-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n.stats-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n}\r\n.stats-icon i {\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n.stats-info {\r\n  flex: 1;\r\n}\r\n.stats-title {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-bottom: 8px;\r\n}\r\n.stats-number {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n.stats-card.total .stats-icon {\r\n  background: linear-gradient(135deg, #3a7bd5, #3a6073);\r\n}\r\n.stats-card.completed .stats-icon {\r\n  background: linear-gradient(135deg, #E6A23C, #F0C78A);\r\n}\r\n.stats-card.in-progress .stats-icon {\r\n  background: linear-gradient(135deg, #409EFF, #66B1FF);\r\n}\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n@media (max-width: 768px) {\r\n  .stats-card .stats-content {\r\n    padding: 15px;\r\n  }\r\n  .stats-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n    margin-right: 15px;\r\n  }\r\n  .stats-icon i {\r\n    font-size: 20px;\r\n  }\r\n  .stats-number {\r\n    font-size: 24px;\r\n  }\r\n}\r\n.other-engineer-select {\r\n  margin-top: 10px;\r\n}\r\n.select-item {\r\n  width: 100%;\r\n}\r\n\r\n/* 未分配打样单告警样式 */\r\n.alert-card {\r\n  margin-bottom: 20px;\r\n}\r\n.alert-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.alert-title {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n.alert-title span {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #FF1414;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-cards {\r\n  margin-top: 20px;\r\n}\r\n.alert-item {\r\n  margin-bottom: 20px;\r\n  transition: all 0.3s;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n  height: 225px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n.alert-item:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);\r\n  border-color: #409EFF;\r\n}\r\n.alert-item-header {\r\n  margin-bottom: 12px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #f0f2f5;\r\n  background: rgba(64, 158, 255, 0.02);\r\n  margin: -16px -16px 12px -16px;\r\n  padding: 12px 16px;\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n.order-code-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.order-code {\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n.urgency-tag {\r\n  margin-left: 8px;\r\n}\r\n.alert-item-content {\r\n  padding: 0;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  min-height: 0;\r\n}\r\n.info-section {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n}\r\n.info-row {\r\n  margin-bottom: 8px;\r\n}\r\n.info-row-double {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 12px;\r\n}\r\n.info-row-double .info-item {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-row-double .info-item.date-time-item {\r\n  flex: 1.5;\r\n}\r\n.info-row-double .info-item.standard-item {\r\n  flex: 1;\r\n}\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n.info-label {\r\n  margin-left: 6px;\r\n  margin-right: 4px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n.info-value {\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n.difficulty-text {\r\n  cursor: pointer;\r\n  border-bottom: 1px dashed #ccc;\r\n}\r\n.info-reason {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  color: #f56c6c;\r\n  margin-top: 8px;\r\n  padding: 6px 8px;\r\n  background-color: #fef0f0;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #f56c6c;\r\n  font-size: 12px;\r\n}\r\n.reason-text {\r\n  margin-left: 4px;\r\n  line-height: 1.4;\r\n  word-break: break-word;\r\n}\r\n.text-overflow {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-reason span {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.info-item i,\r\n.info-date i,\r\n.info-reason i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n.alert-item-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: auto;\r\n  padding: 12px 0 0 0;\r\n  border-top: 1px solid #f0f2f5;\r\n  flex-shrink: 0;\r\n  background: rgba(250, 251, 252, 0.5);\r\n  border-radius: 0 0 6px 6px;\r\n  margin-left: -16px;\r\n  margin-right: -16px;\r\n  padding-left: 16px;\r\n  padding-right: 16px;\r\n}\r\n.alert-item-footer .el-button {\r\n  border-radius: 4px;\r\n  font-weight: 500;\r\n}\r\n.alert-item-footer .el-button--primary {\r\n  background: #409EFF;\r\n  border-color: #409EFF;\r\n  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);\r\n}\r\n.alert-item-footer .el-button--primary:hover {\r\n  background: #66b1ff;\r\n  border-color: #66b1ff;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);\r\n}\r\n.alert-item-footer .el-button--text {\r\n  color: #909399;\r\n  transition: all 0.3s;\r\n}\r\n.alert-item-footer .el-button--text:hover {\r\n  color: #f56c6c;\r\n  background: rgba(245, 108, 108, 0.1);\r\n}\r\n.delete-btn:hover {\r\n  color: #f56c6c !important;\r\n}\r\n\r\n/* 确保卡片内容区域的统一布局 */\r\n.alert-item .el-card__body {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 16px;\r\n  position: relative;\r\n}\r\n\r\n/* 确保内容区域占据剩余空间 */\r\n.alert-item .alert-item-header {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.alert-item .alert-item-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n}\r\n\r\n.alert-item .alert-item-footer {\r\n  flex-shrink: 0;\r\n  margin-top: auto;\r\n}\r\n\r\n.alert-item .info-group {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.alert-item .info-date {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .alert-item {\r\n    height: auto;\r\n    min-height: 200px;\r\n  }\r\n\r\n  .info-row-double {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .alert-item-footer {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .alert-item-footer .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 批次管理相关样式 */\r\n.batch-management-container {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.current-batch-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-info-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.batch-info-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.experiment-section {\r\n  border-top: 1px solid #ebeef5;\r\n  padding-top: 15px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.section-header span {\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.history-batches-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.new-batch-section {\r\n  padding: 40px 0;\r\n  text-align: center;\r\n  background-color: #fafafa;\r\n  border: 2px dashed #dcdfe6;\r\n  border-radius: 6px;\r\n}\r\n\r\n.new-batch-section .el-button {\r\n  padding: 12px 30px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .batch-management-container {\r\n    max-height: 500px;\r\n  }\r\n\r\n  .batch-info-item {\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .section-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .section-header .el-button {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n\r\n/* 备注文本省略样式 */\r\n.remark-text-ellipsis {\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  line-clamp: 2;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  line-height: 1.4;\r\n  max-height: 2.8em; /* 2行的高度，基于line-height */\r\n  word-break: break-word;\r\n  white-space: normal;\r\n  cursor: pointer;\r\n}\r\n\r\n.remark-text-ellipsis:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 导出选择对话框样式 */\r\n.export-options {\r\n  padding: 10px 0;\r\n}\r\n\r\n.export-options .el-checkbox {\r\n  width: 100%;\r\n  margin-right: 0;\r\n  padding: 15px;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.export-options .el-checkbox:hover {\r\n  border-color: #409eff;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.export-options .el-checkbox.is-checked {\r\n  border-color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.export-options .el-checkbox__label {\r\n  width: 100%;\r\n  padding-left: 10px;\r\n}\r\n\r\n/* 实验记录相关样式 */\r\n.experiment-codes {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 4px;\r\n  min-height: 32px;\r\n}\r\n\r\n.experiment-codes .el-tag {\r\n  margin: 2px;\r\n  max-width: 120px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.experiment-codes .el-button {\r\n  margin: 2px;\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n}\r\n</style>\r\n"]}]}